const Product = require('../models/Product');
const { validationResult } = require('express-validator');

/**
 * Product Controller
 * Handles all product-related operations
 */
class ProductController {
  // Get all products
  async getAllProducts(req, res) {
    try {
      const { category, search, page = 1, limit = 10 } = req.query;
      
      // Build query
      let query = {};
      
      if (category && category !== 'all') {
        query.category = category;
      }
      
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { sku: { $regex: search, $options: 'i' } }
        ];
      }
      
      // Calculate pagination
      const skip = (page - 1) * limit;
      
      // Execute query
      const products = await Product.find(query)
        .skip(skip)
        .limit(parseInt(limit))
        .sort({ createdAt: -1 });
      
      const total = await Product.countDocuments(query);
      
      res.json({
        success: true,
        data: products,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      console.error('Error fetching products:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch products',
        error: error.message
      });
    }
  }

  // Get product by ID
  async getProductById(req, res) {
    try {
      const { id } = req.params;
      const product = await Product.findById(id);
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }
      
      res.json({
        success: true,
        data: product
      });
    } catch (error) {
      console.error('Error fetching product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch product',
        error: error.message
      });
    }
  }

  // Get product by SKU
  async getProductBySku(req, res) {
    try {
      const { sku } = req.params;
      const product = await Product.findOne({ sku });
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }
      
      res.json({
        success: true,
        data: product
      });
    } catch (error) {
      console.error('Error fetching product by SKU:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch product',
        error: error.message
      });
    }
  }

  // Create new product
  async createProduct(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const productData = req.body;
      
      // Check if SKU already exists
      const existingProduct = await Product.findOne({ sku: productData.sku });
      if (existingProduct) {
        return res.status(400).json({
          success: false,
          message: 'Product with this SKU already exists'
        });
      }
      
      // Create new product
      const product = new Product(productData);
      await product.save();
      
      res.status(201).json({
        success: true,
        message: 'Product created successfully',
        product
      });
    } catch (error) {
      console.error('Error creating product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create product',
        error: error.message
      });
    }
  }

  // Update product
  async updateProduct(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }
      
      // If SKU is being updated, check for duplicates
      if (updateData.sku) {
        const existingProduct = await Product.findOne({ 
          sku: updateData.sku, 
          _id: { $ne: id } 
        });
        if (existingProduct) {
          return res.status(400).json({
            success: false,
            message: 'Product with this SKU already exists'
          });
        }
      }
      
      const product = await Product.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }
      
      res.json({
        success: true,
        message: 'Product updated successfully',
        product
      });
    } catch (error) {
      console.error('Error updating product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update product',
        error: error.message
      });
    }
  }

  // Delete product
  async deleteProduct(req, res) {
    try {
      const { id } = req.params;
      
      const product = await Product.findByIdAndDelete(id);
      
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }
      
      res.json({
        success: true,
        message: 'Product deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete product',
        error: error.message
      });
    }
  }

  // Update stock
  async updateStock(req, res) {
    try {
      const { id } = req.params;
      const { stock, operation = 'set' } = req.body;
      
      const product = await Product.findById(id);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }
      
      let newStock;
      switch (operation) {
        case 'add':
          newStock = product.stock + stock;
          break;
        case 'subtract':
          newStock = Math.max(0, product.stock - stock);
          break;
        case 'set':
        default:
          newStock = stock;
          break;
      }
      
      product.stock = newStock;
      await product.save();
      
      res.json({
        success: true,
        message: 'Stock updated successfully',
        product
      });
    } catch (error) {
      console.error('Error updating stock:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update stock',
        error: error.message
      });
    }
  }

  // Get low stock products
  async getLowStockProducts(req, res) {
    try {
      const products = await Product.find({
        $expr: { $lte: ['$stock', '$lowStockThreshold'] }
      }).sort({ stock: 1 });
      
      res.json({
        success: true,
        data: products
      });
    } catch (error) {
      console.error('Error fetching low stock products:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch low stock products',
        error: error.message
      });
    }
  }

  // Get inventory statistics
  async getInventoryStats(req, res) {
    try {
      const totalProducts = await Product.countDocuments();
      const lowStockProducts = await Product.countDocuments({
        $expr: { $lte: ['$stock', '$lowStockThreshold'] }
      });
      const outOfStockProducts = await Product.countDocuments({ stock: 0 });
      
      const valueAggregation = await Product.aggregate([
        {
          $group: {
            _id: null,
            totalValue: { $sum: { $multiply: ['$price', '$stock'] } },
            averagePrice: { $avg: '$price' },
            totalStock: { $sum: '$stock' }
          }
        }
      ]);
      
      const categories = await Product.distinct('category');
      
      const stats = {
        totalProducts,
        lowStockProducts,
        outOfStockProducts,
        totalCategories: categories.length,
        totalValue: valueAggregation[0]?.totalValue || 0,
        averagePrice: valueAggregation[0]?.averagePrice || 0,
        totalStock: valueAggregation[0]?.totalStock || 0
      };
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error fetching inventory stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch inventory statistics',
        error: error.message
      });
    }
  }

  // Get categories
  async getCategories(req, res) {
    try {
      const categories = await Product.distinct('category');
      
      res.json({
        success: true,
        data: categories
      });
    } catch (error) {
      console.error('Error fetching categories:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch categories',
        error: error.message
      });
    }
  }
}

module.exports = new ProductController();
