const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [100, 'Product name cannot exceed 100 characters']
  },
  
  sku: {
    type: String,
    required: [true, 'SKU is required'],
    unique: true,
    trim: true,
    uppercase: true,
    maxlength: [50, 'SKU cannot exceed 50 characters']
  },
  
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'Laying',
      'ElectricalMaterial',
      'StructureMaterial',
      'PlumbingMaterials',
      'ToolsAndEquipment',
      'Electronics',
      'Groceries',
      'Fruits',
      'Vegetables',
      'Meat',
      'Dairy',
      'Beverages',
      'Grains'
    ]
  },
  
  stock: {
    type: Number,
    required: [true, 'Stock quantity is required'],
    min: [0, 'Stock cannot be negative'],
    default: 0
  },
  
  lowStockThreshold: {
    type: Number,
    required: [true, 'Low stock threshold is required'],
    min: [0, 'Low stock threshold cannot be negative'],
    default: 10
  },
  
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative']
  },
  
  manufacturing_date: {
    type: Date,
    validate: {
      validator: function(value) {
        return !value || value <= new Date();
      },
      message: 'Manufacturing date cannot be in the future'
    }
  },
  
  expiry_date: {
    type: Date,
    validate: {
      validator: function(value) {
        return !value || !this.manufacturing_date || value > this.manufacturing_date;
      },
      message: 'Expiry date must be after manufacturing date'
    }
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  
  supplier: {
    type: String,
    trim: true,
    maxlength: [100, 'Supplier name cannot exceed 100 characters']
  },
  
  location: {
    type: String,
    trim: true,
    maxlength: [100, 'Location cannot exceed 100 characters']
  },
  
  unit: {
    type: String,
    trim: true,
    default: 'pieces',
    maxlength: [20, 'Unit cannot exceed 20 characters']
  },
  
  weight: {
    type: Number,
    min: [0, 'Weight cannot be negative']
  },
  
  dimensions: {
    length: {
      type: Number,
      min: [0, 'Length cannot be negative']
    },
    width: {
      type: Number,
      min: [0, 'Width cannot be negative']
    },
    height: {
      type: Number,
      min: [0, 'Height cannot be negative']
    }
  },
  
  tags: [{
    type: String,
    trim: true
  }],
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for stock status
productSchema.virtual('stockStatus').get(function() {
  if (this.stock === 0) return 'out_of_stock';
  if (this.stock <= this.lowStockThreshold) return 'low_stock';
  return 'in_stock';
});

// Virtual for days until expiry
productSchema.virtual('daysUntilExpiry').get(function() {
  if (!this.expiry_date) return null;
  const today = new Date();
  const expiry = new Date(this.expiry_date);
  const diffTime = expiry - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for expired status
productSchema.virtual('isExpired').get(function() {
  if (!this.expiry_date) return false;
  return new Date() > new Date(this.expiry_date);
});

// Virtual for total value
productSchema.virtual('totalValue').get(function() {
  return this.stock * this.price;
});

// Indexes for better query performance
productSchema.index({ sku: 1 });
productSchema.index({ category: 1 });
productSchema.index({ name: 'text', sku: 'text' });
productSchema.index({ stock: 1, lowStockThreshold: 1 });
productSchema.index({ expiry_date: 1 });
productSchema.index({ createdAt: -1 });

// Pre-save middleware
productSchema.pre('save', function(next) {
  this.lastUpdated = new Date();
  next();
});

// Static methods
productSchema.statics.findLowStock = function() {
  return this.find({
    $expr: { $lte: ['$stock', '$lowStockThreshold'] }
  });
};

productSchema.statics.findExpired = function() {
  return this.find({
    expiry_date: { $lt: new Date() }
  });
};

productSchema.statics.findByCategory = function(category) {
  return this.find({ category });
};

productSchema.statics.searchProducts = function(query) {
  return this.find({
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { sku: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } }
    ]
  });
};

// Instance methods
productSchema.methods.updateStock = function(quantity, operation = 'set') {
  switch (operation) {
    case 'add':
      this.stock += quantity;
      break;
    case 'subtract':
      this.stock = Math.max(0, this.stock - quantity);
      break;
    case 'set':
    default:
      this.stock = quantity;
      break;
  }
  return this.save();
};

productSchema.methods.isLowStock = function() {
  return this.stock <= this.lowStockThreshold;
};

productSchema.methods.isOutOfStock = function() {
  return this.stock === 0;
};

module.exports = mongoose.model('Product', productSchema);
