{"version": 3, "sources": ["../../jspdf-autotable/dist/jspdf.plugin.autotable.js"], "sourcesContent": ["/*!\n * \n *               jsPDF AutoTable plugin v3.8.4\n *\n *               Copyright (c) 2024 <PERSON>, https://github.com/simonben<PERSON><PERSON>/jsPDF-AutoTable\n *               Licensed under the MIT License.\n *               http://opensource.org/licenses/mit-license\n *\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory((function webpackLoadOptionalExternalModule() { try { return require(\"jspdf\"); } catch(e) {} }()));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"jspdf\"], factory);\n\telse {\n\t\tvar a = typeof exports === 'object' ? factory((function webpackLoadOptionalExternalModule() { try { return require(\"jspdf\"); } catch(e) {} }())) : factory(root[\"jspdf\"]);\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(typeof globalThis !== 'undefined' ? globalThis : typeof this !== 'undefined' ? this : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : global , function(__WEBPACK_EXTERNAL_MODULE__964__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 172:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CellHookData = exports.HookData = void 0;\nvar HookData = /** @class */ (function () {\n    function HookData(doc, table, cursor) {\n        this.table = table;\n        this.pageNumber = table.pageNumber;\n        this.pageCount = this.pageNumber;\n        this.settings = table.settings;\n        this.cursor = cursor;\n        this.doc = doc.getDocument();\n    }\n    return HookData;\n}());\nexports.HookData = HookData;\nvar CellHookData = /** @class */ (function (_super) {\n    __extends(CellHookData, _super);\n    function CellHookData(doc, table, cell, row, column, cursor) {\n        var _this = _super.call(this, doc, table, cursor) || this;\n        _this.cell = cell;\n        _this.row = row;\n        _this.column = column;\n        _this.section = row.section;\n        return _this;\n    }\n    return CellHookData;\n}(HookData));\nexports.CellHookData = CellHookData;\n\n\n/***/ }),\n\n/***/ 340:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar htmlParser_1 = __webpack_require__(4);\nvar autoTableText_1 = __webpack_require__(136);\nvar documentHandler_1 = __webpack_require__(744);\nvar inputParser_1 = __webpack_require__(776);\nvar tableDrawer_1 = __webpack_require__(664);\nvar tableCalculator_1 = __webpack_require__(972);\nfunction default_1(jsPDF) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    jsPDF.API.autoTable = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var options;\n        if (args.length === 1) {\n            options = args[0];\n        }\n        else {\n            console.error('Use of deprecated autoTable initiation');\n            options = args[2] || {};\n            options.columns = args[0];\n            options.body = args[1];\n        }\n        var input = (0, inputParser_1.parseInput)(this, options);\n        var table = (0, tableCalculator_1.createTable)(this, input);\n        (0, tableDrawer_1.drawTable)(this, table);\n        return this;\n    };\n    // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n    jsPDF.API.lastAutoTable = false;\n    jsPDF.API.previousAutoTable = false; // deprecated in v3\n    jsPDF.API.autoTable.previous = false; // deprecated in v3\n    jsPDF.API.autoTableText = function (text, x, y, styles) {\n        (0, autoTableText_1.default)(text, x, y, styles, this);\n    };\n    jsPDF.API.autoTableSetDefaults = function (defaults) {\n        documentHandler_1.DocHandler.setDefaults(defaults, this);\n        return this;\n    };\n    jsPDF.autoTableSetDefaults = function (defaults, doc) {\n        documentHandler_1.DocHandler.setDefaults(defaults, doc);\n    };\n    jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n        var _a;\n        if (includeHiddenElements === void 0) { includeHiddenElements = false; }\n        if (typeof window === 'undefined') {\n            console.error('Cannot run autoTableHtmlToJson in non browser environment');\n            return null;\n        }\n        var doc = new documentHandler_1.DocHandler(this);\n        var _b = (0, htmlParser_1.parseHtml)(doc, tableElem, window, includeHiddenElements, false), head = _b.head, body = _b.body;\n        var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) { return c.content; })) || [];\n        return { columns: columns, rows: body, data: body };\n    };\n    /**\n     * @deprecated\n     */\n    jsPDF.API.autoTableEndPosY = function () {\n        console.error('Use of deprecated function: autoTableEndPosY. Use doc.lastAutoTable.finalY instead.');\n        var prev = this.lastAutoTable;\n        if (prev && prev.finalY) {\n            return prev.finalY;\n        }\n        else {\n            return 0;\n        }\n    };\n    /**\n     * @deprecated\n     */\n    jsPDF.API.autoTableAddPageContent = function (hook) {\n        console.error('Use of deprecated function: autoTableAddPageContent. Use jsPDF.autoTableSetDefaults({didDrawPage: () => {}}) instead.');\n        if (!jsPDF.API.autoTable.globalDefaults) {\n            jsPDF.API.autoTable.globalDefaults = {};\n        }\n        jsPDF.API.autoTable.globalDefaults.addPageContent = hook;\n        return this;\n    };\n    /**\n     * @deprecated\n     */\n    jsPDF.API.autoTableAddPage = function () {\n        console.error('Use of deprecated function: autoTableAddPage. Use doc.addPage()');\n        this.addPage();\n        return this;\n    };\n}\nexports[\"default\"] = default_1;\n\n\n/***/ }),\n\n/***/ 136:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction default_1(text, x, y, styles, doc) {\n    styles = styles || {};\n    var PHYSICAL_LINE_HEIGHT = 1.15;\n    var k = doc.internal.scaleFactor;\n    var fontSize = doc.internal.getFontSize() / k;\n    var lineHeightFactor = doc.getLineHeightFactor\n        ? doc.getLineHeightFactor()\n        : PHYSICAL_LINE_HEIGHT;\n    var lineHeight = fontSize * lineHeightFactor;\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    var splitText = '';\n    var lineCount = 1;\n    if (styles.valign === 'middle' ||\n        styles.valign === 'bottom' ||\n        styles.halign === 'center' ||\n        styles.halign === 'right') {\n        splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n        lineCount = splitText.length || 1;\n    }\n    // Align the top\n    y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n    if (styles.valign === 'middle')\n        y -= (lineCount / 2) * lineHeight;\n    else if (styles.valign === 'bottom')\n        y -= lineCount * lineHeight;\n    if (styles.halign === 'center' || styles.halign === 'right') {\n        var alignSize = fontSize;\n        if (styles.halign === 'center')\n            alignSize *= 0.5;\n        if (splitText && lineCount >= 1) {\n            for (var iLine = 0; iLine < splitText.length; iLine++) {\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                y += lineHeight;\n            }\n            return doc;\n        }\n        x -= doc.getStringUnitWidth(text) * alignSize;\n    }\n    if (styles.halign === 'justify') {\n        doc.text(text, x, y, {\n            maxWidth: styles.maxWidth || 100,\n            align: 'justify',\n        });\n    }\n    else {\n        doc.text(text, x, y);\n    }\n    return doc;\n}\nexports[\"default\"] = default_1;\n\n\n/***/ }),\n\n/***/ 420:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPageAvailableWidth = exports.parseSpacing = exports.getFillStyle = exports.addTableBorder = exports.getStringWidth = void 0;\nfunction getStringWidth(text, styles, doc) {\n    doc.applyStyles(styles, true);\n    var textArr = Array.isArray(text) ? text : [text];\n    var widestLineWidth = textArr\n        .map(function (text) { return doc.getTextWidth(text); })\n        .reduce(function (a, b) { return Math.max(a, b); }, 0);\n    return widestLineWidth;\n}\nexports.getStringWidth = getStringWidth;\nfunction addTableBorder(doc, table, startPos, cursor) {\n    var lineWidth = table.settings.tableLineWidth;\n    var lineColor = table.settings.tableLineColor;\n    doc.applyStyles({ lineWidth: lineWidth, lineColor: lineColor });\n    var fillStyle = getFillStyle(lineWidth, false);\n    if (fillStyle) {\n        doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n    }\n}\nexports.addTableBorder = addTableBorder;\nfunction getFillStyle(lineWidth, fillColor) {\n    var drawLine = lineWidth > 0;\n    var drawBackground = fillColor || fillColor === 0;\n    if (drawLine && drawBackground) {\n        return 'DF'; // Fill then stroke\n    }\n    else if (drawLine) {\n        return 'S'; // Only stroke (transparent background)\n    }\n    else if (drawBackground) {\n        return 'F'; // Only fill, no stroke\n    }\n    else {\n        return null;\n    }\n}\nexports.getFillStyle = getFillStyle;\nfunction parseSpacing(value, defaultValue) {\n    var _a, _b, _c, _d;\n    value = value || defaultValue;\n    if (Array.isArray(value)) {\n        if (value.length >= 4) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[3],\n            };\n        }\n        else if (value.length === 3) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[1],\n            };\n        }\n        else if (value.length === 2) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[0],\n                left: value[1],\n            };\n        }\n        else if (value.length === 1) {\n            value = value[0];\n        }\n        else {\n            value = defaultValue;\n        }\n    }\n    if (typeof value === 'object') {\n        if (typeof value.vertical === 'number') {\n            value.top = value.vertical;\n            value.bottom = value.vertical;\n        }\n        if (typeof value.horizontal === 'number') {\n            value.right = value.horizontal;\n            value.left = value.horizontal;\n        }\n        return {\n            left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n            top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n            right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n            bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue,\n        };\n    }\n    if (typeof value !== 'number') {\n        value = defaultValue;\n    }\n    return { top: value, right: value, bottom: value, left: value };\n}\nexports.parseSpacing = parseSpacing;\nfunction getPageAvailableWidth(doc, table) {\n    var margins = parseSpacing(table.settings.margin, 0);\n    return doc.pageSize().width - (margins.left + margins.right);\n}\nexports.getPageAvailableWidth = getPageAvailableWidth;\n\n\n/***/ }),\n\n/***/ 796:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getTheme = exports.defaultStyles = exports.HtmlRowInput = void 0;\nvar HtmlRowInput = /** @class */ (function (_super) {\n    __extends(HtmlRowInput, _super);\n    function HtmlRowInput(element) {\n        var _this = _super.call(this) || this;\n        _this._element = element;\n        return _this;\n    }\n    return HtmlRowInput;\n}(Array));\nexports.HtmlRowInput = HtmlRowInput;\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n    return {\n        font: 'helvetica', // helvetica, times, courier\n        fontStyle: 'normal', // normal, bold, italic, bolditalic\n        overflow: 'linebreak', // linebreak, ellipsize, visible or hidden\n        fillColor: false, // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n        textColor: 20,\n        halign: 'left', // left, center, right, justify\n        valign: 'top', // top, middle, bottom\n        fontSize: 10,\n        cellPadding: 5 / scaleFactor, // number or {top,left,right,left,vertical,horizontal}\n        lineColor: 200,\n        lineWidth: 0,\n        cellWidth: 'auto', // 'auto'|'wrap'|number\n        minCellHeight: 0,\n        minCellWidth: 0,\n    };\n}\nexports.defaultStyles = defaultStyles;\nfunction getTheme(name) {\n    var themes = {\n        striped: {\n            table: { fillColor: 255, textColor: 80, fontStyle: 'normal' },\n            head: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            body: {},\n            foot: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            alternateRow: { fillColor: 245 },\n        },\n        grid: {\n            table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal',\n                lineWidth: 0.1,\n            },\n            head: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            body: {},\n            foot: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            alternateRow: {},\n        },\n        plain: {\n            head: { fontStyle: 'bold' },\n            foot: { fontStyle: 'bold' },\n        },\n    };\n    return themes[name];\n}\nexports.getTheme = getTheme;\n\n\n/***/ }),\n\n/***/ 903:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseCss = void 0;\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nvar common_1 = __webpack_require__(420);\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n    var result = {};\n    var pxScaleFactor = 96 / 72;\n    var backgroundColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['backgroundColor'];\n    });\n    if (backgroundColor != null)\n        result.fillColor = backgroundColor;\n    var textColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['color'];\n    });\n    if (textColor != null)\n        result.textColor = textColor;\n    var padding = parsePadding(style, scaleFactor);\n    if (padding)\n        result.cellPadding = padding;\n    var borderColorSide = 'borderTopColor';\n    var finalScaleFactor = pxScaleFactor * scaleFactor;\n    var btw = style.borderTopWidth;\n    if (style.borderBottomWidth === btw &&\n        style.borderRightWidth === btw &&\n        style.borderLeftWidth === btw) {\n        var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n        if (borderWidth)\n            result.lineWidth = borderWidth;\n    }\n    else {\n        result.lineWidth = {\n            top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n            right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n            bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n            left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor,\n        };\n        // Choose border color of first available side\n        // could be improved by supporting object as lineColor\n        if (!result.lineWidth.top) {\n            if (result.lineWidth.right) {\n                borderColorSide = 'borderRightColor';\n            }\n            else if (result.lineWidth.bottom) {\n                borderColorSide = 'borderBottomColor';\n            }\n            else if (result.lineWidth.left) {\n                borderColorSide = 'borderLeftColor';\n            }\n        }\n    }\n    var borderColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)[borderColorSide];\n    });\n    if (borderColor != null)\n        result.lineColor = borderColor;\n    var accepted = ['left', 'right', 'center', 'justify'];\n    if (accepted.indexOf(style.textAlign) !== -1) {\n        result.halign = style.textAlign;\n    }\n    accepted = ['middle', 'bottom', 'top'];\n    if (accepted.indexOf(style.verticalAlign) !== -1) {\n        result.valign = style.verticalAlign;\n    }\n    var res = parseInt(style.fontSize || '');\n    if (!isNaN(res))\n        result.fontSize = res / pxScaleFactor;\n    var fontStyle = parseFontStyle(style);\n    if (fontStyle)\n        result.fontStyle = fontStyle;\n    var font = (style.fontFamily || '').toLowerCase();\n    if (supportedFonts.indexOf(font) !== -1) {\n        result.font = font;\n    }\n    return result;\n}\nexports.parseCss = parseCss;\nfunction parseFontStyle(style) {\n    var res = '';\n    if (style.fontWeight === 'bold' ||\n        style.fontWeight === 'bolder' ||\n        parseInt(style.fontWeight) >= 700) {\n        res = 'bold';\n    }\n    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n        res += 'italic';\n    }\n    return res;\n}\nfunction parseColor(element, styleGetter) {\n    var cssColor = realColor(element, styleGetter);\n    if (!cssColor)\n        return null;\n    var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n    if (!rgba || !Array.isArray(rgba)) {\n        return null;\n    }\n    var color = [\n        parseInt(rgba[1]),\n        parseInt(rgba[2]),\n        parseInt(rgba[3]),\n    ];\n    var alpha = parseInt(rgba[4]);\n    if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n        return null;\n    }\n    return color;\n}\nfunction realColor(elem, styleGetter) {\n    var bg = styleGetter(elem);\n    if (bg === 'rgba(0, 0, 0, 0)' ||\n        bg === 'transparent' ||\n        bg === 'initial' ||\n        bg === 'inherit') {\n        if (elem.parentElement == null) {\n            return null;\n        }\n        return realColor(elem.parentElement, styleGetter);\n    }\n    else {\n        return bg;\n    }\n}\nfunction parsePadding(style, scaleFactor) {\n    var val = [\n        style.paddingTop,\n        style.paddingRight,\n        style.paddingBottom,\n        style.paddingLeft,\n    ];\n    var pxScaleFactor = 96 / (72 / scaleFactor);\n    var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n    var inputPadding = val.map(function (n) {\n        return parseInt(n || '0') / pxScaleFactor;\n    });\n    var padding = (0, common_1.parseSpacing)(inputPadding, 0);\n    if (linePadding > padding.top) {\n        padding.top = linePadding;\n    }\n    if (linePadding > padding.bottom) {\n        padding.bottom = linePadding;\n    }\n    return padding;\n}\n\n\n/***/ }),\n\n/***/ 744:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DocHandler = void 0;\nvar globalDefaults = {};\nvar DocHandler = /** @class */ (function () {\n    function DocHandler(jsPDFDocument) {\n        this.jsPDFDocument = jsPDFDocument;\n        this.userStyles = {\n            // Black for versions of jspdf without getTextColor\n            textColor: jsPDFDocument.getTextColor\n                ? this.jsPDFDocument.getTextColor()\n                : 0,\n            fontSize: jsPDFDocument.internal.getFontSize(),\n            fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n            font: jsPDFDocument.internal.getFont().fontName,\n            // 0 for versions of jspdf without getLineWidth\n            lineWidth: jsPDFDocument.getLineWidth\n                ? this.jsPDFDocument.getLineWidth()\n                : 0,\n            // Black for versions of jspdf without getDrawColor\n            lineColor: jsPDFDocument.getDrawColor\n                ? this.jsPDFDocument.getDrawColor()\n                : 0,\n        };\n    }\n    DocHandler.setDefaults = function (defaults, doc) {\n        if (doc === void 0) { doc = null; }\n        if (doc) {\n            doc.__autoTableDocumentDefaults = defaults;\n        }\n        else {\n            globalDefaults = defaults;\n        }\n    };\n    DocHandler.unifyColor = function (c) {\n        if (Array.isArray(c)) {\n            return c;\n        }\n        else if (typeof c === 'number') {\n            return [c, c, c];\n        }\n        else if (typeof c === 'string') {\n            return [c];\n        }\n        else {\n            return null;\n        }\n    };\n    DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n        // Font style needs to be applied before font\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n        var _a, _b, _c;\n        if (fontOnly === void 0) { fontOnly = false; }\n        if (styles.fontStyle)\n            this.jsPDFDocument.setFontStyle &&\n                this.jsPDFDocument.setFontStyle(styles.fontStyle);\n        var _d = this.jsPDFDocument.internal.getFont(), fontStyle = _d.fontStyle, fontName = _d.fontName;\n        if (styles.font)\n            fontName = styles.font;\n        if (styles.fontStyle) {\n            fontStyle = styles.fontStyle;\n            var availableFontStyles = this.getFontList()[fontName];\n            if (availableFontStyles &&\n                availableFontStyles.indexOf(fontStyle) === -1) {\n                // Common issue was that the default bold in headers\n                // made custom fonts not work. For example:\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                this.jsPDFDocument.setFontStyle &&\n                    this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                fontStyle = availableFontStyles[0];\n            }\n        }\n        this.jsPDFDocument.setFont(fontName, fontStyle);\n        if (styles.fontSize)\n            this.jsPDFDocument.setFontSize(styles.fontSize);\n        if (fontOnly) {\n            return; // Performance improvement\n        }\n        var color = DocHandler.unifyColor(styles.fillColor);\n        if (color)\n            (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n        color = DocHandler.unifyColor(styles.textColor);\n        if (color)\n            (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n        color = DocHandler.unifyColor(styles.lineColor);\n        if (color)\n            (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n        if (typeof styles.lineWidth === 'number') {\n            this.jsPDFDocument.setLineWidth(styles.lineWidth);\n        }\n    };\n    DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n        return this.jsPDFDocument.splitTextToSize(text, size, opts);\n    };\n    /**\n     * Adds a rectangle to the PDF\n     * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n     * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n     * @param width Width (in units declared at inception of PDF document)\n     * @param height Height (in units declared at inception of PDF document)\n     * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n     */\n    DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n        // null is excluded from fillStyle possible values because it isn't needed\n        // and is prone to bugs as it's used to postpone setting the style\n        // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n        return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n    };\n    DocHandler.prototype.getLastAutoTable = function () {\n        return this.jsPDFDocument.lastAutoTable || null;\n    };\n    DocHandler.prototype.getTextWidth = function (text) {\n        return this.jsPDFDocument.getTextWidth(text);\n    };\n    DocHandler.prototype.getDocument = function () {\n        return this.jsPDFDocument;\n    };\n    DocHandler.prototype.setPage = function (page) {\n        this.jsPDFDocument.setPage(page);\n    };\n    DocHandler.prototype.addPage = function () {\n        return this.jsPDFDocument.addPage();\n    };\n    DocHandler.prototype.getFontList = function () {\n        return this.jsPDFDocument.getFontList();\n    };\n    DocHandler.prototype.getGlobalOptions = function () {\n        return globalDefaults || {};\n    };\n    DocHandler.prototype.getDocumentOptions = function () {\n        return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n    };\n    DocHandler.prototype.pageSize = function () {\n        var pageSize = this.jsPDFDocument.internal.pageSize;\n        // JSPDF 1.4 uses get functions instead of properties on pageSize\n        if (pageSize.width == null) {\n            pageSize = {\n                width: pageSize.getWidth(),\n                height: pageSize.getHeight(),\n            };\n        }\n        return pageSize;\n    };\n    DocHandler.prototype.scaleFactor = function () {\n        return this.jsPDFDocument.internal.scaleFactor;\n    };\n    DocHandler.prototype.getLineHeightFactor = function () {\n        var doc = this.jsPDFDocument;\n        return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n    };\n    DocHandler.prototype.getLineHeight = function (fontSize) {\n        return (fontSize / this.scaleFactor()) * this.getLineHeightFactor();\n    };\n    DocHandler.prototype.pageNumber = function () {\n        var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n        if (!pageInfo) {\n            // Only recent versions of jspdf has pageInfo\n            return this.jsPDFDocument.internal.getNumberOfPages();\n        }\n        return pageInfo.pageNumber;\n    };\n    return DocHandler;\n}());\nexports.DocHandler = DocHandler;\n\n\n/***/ }),\n\n/***/ 4:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseHtml = void 0;\nvar cssParser_1 = __webpack_require__(903);\nvar config_1 = __webpack_require__(796);\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n    var _a, _b;\n    if (includeHiddenHtml === void 0) { includeHiddenHtml = false; }\n    if (useCss === void 0) { useCss = false; }\n    var tableElement;\n    if (typeof input === 'string') {\n        tableElement = window.document.querySelector(input);\n    }\n    else {\n        tableElement = input;\n    }\n    var supportedFonts = Object.keys(doc.getFontList());\n    var scaleFactor = doc.scaleFactor();\n    var head = [], body = [], foot = [];\n    if (!tableElement) {\n        console.error('Html table could not be found with input: ', input);\n        return { head: head, body: body, foot: foot };\n    }\n    for (var i = 0; i < tableElement.rows.length; i++) {\n        var element = tableElement.rows[i];\n        var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n        var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n        if (!row)\n            continue;\n        if (tagName === 'thead') {\n            head.push(row);\n        }\n        else if (tagName === 'tfoot') {\n            foot.push(row);\n        }\n        else {\n            // Add to body both if parent is tbody or table\n            body.push(row);\n        }\n    }\n    return { head: head, body: body, foot: foot };\n}\nexports.parseHtml = parseHtml;\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n    var resultRow = new config_1.HtmlRowInput(row);\n    for (var i = 0; i < row.cells.length; i++) {\n        var cell = row.cells[i];\n        var style_1 = window.getComputedStyle(cell);\n        if (includeHidden || style_1.display !== 'none') {\n            var cellStyles = void 0;\n            if (useCss) {\n                cellStyles = (0, cssParser_1.parseCss)(supportedFonts, cell, scaleFactor, style_1, window);\n            }\n            resultRow.push({\n                rowSpan: cell.rowSpan,\n                colSpan: cell.colSpan,\n                styles: cellStyles,\n                _element: cell,\n                content: parseCellContent(cell),\n            });\n        }\n    }\n    var style = window.getComputedStyle(row);\n    if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n        return resultRow;\n    }\n}\nfunction parseCellContent(orgCell) {\n    // Work on cloned node to make sure no changes are applied to html table\n    var cell = orgCell.cloneNode(true);\n    // Remove extra space and line breaks in markup to make it more similar to\n    // what would be shown in html\n    cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n    // Preserve <br> tags as line breaks in the pdf\n    cell.innerHTML = cell.innerHTML\n        .split(/<br.*?>/) //start with '<br' and ends with '>'.\n        .map(function (part) { return part.trim(); })\n        .join('\\n');\n    // innerText for ie\n    return cell.innerText || cell.textContent || '';\n}\n\n\n/***/ }),\n\n/***/ 776:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseInput = void 0;\nvar htmlParser_1 = __webpack_require__(4);\nvar polyfills_1 = __webpack_require__(356);\nvar common_1 = __webpack_require__(420);\nvar documentHandler_1 = __webpack_require__(744);\nvar inputValidator_1 = __webpack_require__(792);\nfunction parseInput(d, current) {\n    var doc = new documentHandler_1.DocHandler(d);\n    var document = doc.getDocumentOptions();\n    var global = doc.getGlobalOptions();\n    (0, inputValidator_1.default)(doc, global, document, current);\n    var options = (0, polyfills_1.assign)({}, global, document, current);\n    var win;\n    if (typeof window !== 'undefined') {\n        win = window;\n    }\n    var styles = parseStyles(global, document, current);\n    var hooks = parseHooks(global, document, current);\n    var settings = parseSettings(doc, options);\n    var content = parseContent(doc, options, win);\n    return {\n        id: current.tableId,\n        content: content,\n        hooks: hooks,\n        styles: styles,\n        settings: settings,\n    };\n}\nexports.parseInput = parseInput;\nfunction parseStyles(gInput, dInput, cInput) {\n    var styleOptions = {\n        styles: {},\n        headStyles: {},\n        bodyStyles: {},\n        footStyles: {},\n        alternateRowStyles: {},\n        columnStyles: {},\n    };\n    var _loop_1 = function (prop) {\n        if (prop === 'columnStyles') {\n            var global_1 = gInput[prop];\n            var document_1 = dInput[prop];\n            var current = cInput[prop];\n            styleOptions.columnStyles = (0, polyfills_1.assign)({}, global_1, document_1, current);\n        }\n        else {\n            var allOptions = [gInput, dInput, cInput];\n            var styles = allOptions.map(function (opts) { return opts[prop] || {}; });\n            styleOptions[prop] = (0, polyfills_1.assign)({}, styles[0], styles[1], styles[2]);\n        }\n    };\n    for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n        var prop = _a[_i];\n        _loop_1(prop);\n    }\n    return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n    var allOptions = [global, document, current];\n    var result = {\n        didParseCell: [],\n        willDrawCell: [],\n        didDrawCell: [],\n        willDrawPage: [],\n        didDrawPage: [],\n    };\n    for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n        var options = allOptions_1[_i];\n        if (options.didParseCell)\n            result.didParseCell.push(options.didParseCell);\n        if (options.willDrawCell)\n            result.willDrawCell.push(options.willDrawCell);\n        if (options.didDrawCell)\n            result.didDrawCell.push(options.didDrawCell);\n        if (options.willDrawPage)\n            result.willDrawPage.push(options.willDrawPage);\n        if (options.didDrawPage)\n            result.didDrawPage.push(options.didDrawPage);\n    }\n    return result;\n}\nfunction parseSettings(doc, options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n    var margin = (0, common_1.parseSpacing)(options.margin, 40 / doc.scaleFactor());\n    var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n    var showFoot;\n    if (options.showFoot === true) {\n        showFoot = 'everyPage';\n    }\n    else if (options.showFoot === false) {\n        showFoot = 'never';\n    }\n    else {\n        showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n    }\n    var showHead;\n    if (options.showHead === true) {\n        showHead = 'everyPage';\n    }\n    else if (options.showHead === false) {\n        showHead = 'never';\n    }\n    else {\n        showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n    }\n    var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n    var theme = options.theme || (useCss ? 'plain' : 'striped');\n    var horizontalPageBreak = !!options.horizontalPageBreak;\n    var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n    return {\n        includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n        useCss: useCss,\n        theme: theme,\n        startY: startY,\n        margin: margin,\n        pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n        rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n        tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n        showHead: showHead,\n        showFoot: showFoot,\n        tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n        tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n        horizontalPageBreak: horizontalPageBreak,\n        horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n        horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows',\n    };\n}\nfunction getStartY(doc, userStartY) {\n    var previous = doc.getLastAutoTable();\n    var sf = doc.scaleFactor();\n    var currentPage = doc.pageNumber();\n    var isSamePageAsPreviousTable = false;\n    if (previous && previous.startPageNumber) {\n        var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n        isSamePageAsPreviousTable = endingPage === currentPage;\n    }\n    if (typeof userStartY === 'number') {\n        return userStartY;\n    }\n    else if (userStartY == null || userStartY === false) {\n        if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n            // Some users had issues with overlapping tables when they used multiple\n            // tables without setting startY so setting it here to a sensible default.\n            return previous.finalY + 20 / sf;\n        }\n    }\n    return null;\n}\nfunction parseContent(doc, options, window) {\n    var head = options.head || [];\n    var body = options.body || [];\n    var foot = options.foot || [];\n    if (options.html) {\n        var hidden = options.includeHiddenHtml;\n        if (window) {\n            var htmlContent = (0, htmlParser_1.parseHtml)(doc, options.html, window, hidden, options.useCss) || {};\n            head = htmlContent.head || head;\n            body = htmlContent.body || head;\n            foot = htmlContent.foot || head;\n        }\n        else {\n            console.error('Cannot parse html in non browser environment');\n        }\n    }\n    var columns = options.columns || parseColumns(head, body, foot);\n    return {\n        columns: columns,\n        head: head,\n        body: body,\n        foot: foot,\n    };\n}\nfunction parseColumns(head, body, foot) {\n    var firstRow = head[0] || body[0] || foot[0] || [];\n    var result = [];\n    Object.keys(firstRow)\n        .filter(function (key) { return key !== '_element'; })\n        .forEach(function (key) {\n        var colSpan = 1;\n        var input;\n        if (Array.isArray(firstRow)) {\n            input = firstRow[parseInt(key)];\n        }\n        else {\n            input = firstRow[key];\n        }\n        if (typeof input === 'object' && !Array.isArray(input)) {\n            colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n        }\n        for (var i = 0; i < colSpan; i++) {\n            var id = void 0;\n            if (Array.isArray(firstRow)) {\n                id = result.length;\n            }\n            else {\n                id = key + (i > 0 ? \"_\".concat(i) : '');\n            }\n            var rowResult = { dataKey: id };\n            result.push(rowResult);\n        }\n    });\n    return result;\n}\n\n\n/***/ }),\n\n/***/ 792:\n/***/ (function(__unused_webpack_module, exports) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nfunction default_1(doc, global, document, current) {\n    var _loop_1 = function (options) {\n        if (options && typeof options !== 'object') {\n            console.error('The options parameter should be of type object, is: ' + typeof options);\n        }\n        if (typeof options.extendWidth !== 'undefined') {\n            options.tableWidth = options.extendWidth ? 'auto' : 'wrap';\n            console.error('Use of deprecated option: extendWidth, use tableWidth instead.');\n        }\n        if (typeof options.margins !== 'undefined') {\n            if (typeof options.margin === 'undefined')\n                options.margin = options.margins;\n            console.error('Use of deprecated option: margins, use margin instead.');\n        }\n        if (options.startY && typeof options.startY !== 'number') {\n            console.error('Invalid value for startY option', options.startY);\n            delete options.startY;\n        }\n        if (!options.didDrawPage &&\n            (options.afterPageContent ||\n                options.beforePageContent ||\n                options.afterPageAdd)) {\n            console.error('The afterPageContent, beforePageContent and afterPageAdd hooks are deprecated. Use didDrawPage instead');\n            options.didDrawPage = function (data) {\n                doc.applyStyles(doc.userStyles);\n                if (options.beforePageContent)\n                    options.beforePageContent(data);\n                doc.applyStyles(doc.userStyles);\n                if (options.afterPageContent)\n                    options.afterPageContent(data);\n                doc.applyStyles(doc.userStyles);\n                if (options.afterPageAdd && data.pageNumber > 1) {\n                    ;\n                    data.afterPageAdd(data);\n                }\n                doc.applyStyles(doc.userStyles);\n            };\n        }\n        ;\n        [\n            'createdHeaderCell',\n            'drawHeaderRow',\n            'drawRow',\n            'drawHeaderCell',\n        ].forEach(function (name) {\n            if (options[name]) {\n                console.error(\"The \\\"\".concat(name, \"\\\" hook has changed in version 3.0, check the changelog for how to migrate.\"));\n            }\n        });\n        [\n            ['showFoot', 'showFooter'],\n            ['showHead', 'showHeader'],\n            ['didDrawPage', 'addPageContent'],\n            ['didParseCell', 'createdCell'],\n            ['headStyles', 'headerStyles'],\n        ].forEach(function (_a) {\n            var current = _a[0], deprecated = _a[1];\n            if (options[deprecated]) {\n                console.error(\"Use of deprecated option \".concat(deprecated, \". Use \").concat(current, \" instead\"));\n                options[current] = options[deprecated];\n            }\n        });\n        [\n            ['padding', 'cellPadding'],\n            ['lineHeight', 'rowHeight'],\n            'fontSize',\n            'overflow',\n        ].forEach(function (o) {\n            var deprecatedOption = typeof o === 'string' ? o : o[0];\n            var style = typeof o === 'string' ? o : o[1];\n            if (typeof options[deprecatedOption] !== 'undefined') {\n                if (typeof options.styles[style] === 'undefined') {\n                    options.styles[style] = options[deprecatedOption];\n                }\n                console.error('Use of deprecated option: ' +\n                    deprecatedOption +\n                    ', use the style ' +\n                    style +\n                    ' instead.');\n            }\n        });\n        for (var _b = 0, _c = [\n            'styles',\n            'bodyStyles',\n            'headStyles',\n            'footStyles',\n        ]; _b < _c.length; _b++) {\n            var styleProp = _c[_b];\n            checkStyles(options[styleProp] || {});\n        }\n        var columnStyles = options['columnStyles'] || {};\n        for (var _d = 0, _e = Object.keys(columnStyles); _d < _e.length; _d++) {\n            var key = _e[_d];\n            checkStyles(columnStyles[key] || {});\n        }\n    };\n    for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n        var options = _a[_i];\n        _loop_1(options);\n    }\n}\nexports[\"default\"] = default_1;\nfunction checkStyles(styles) {\n    if (styles.rowHeight) {\n        console.error('Use of deprecated style rowHeight. It is renamed to minCellHeight.');\n        if (!styles.minCellHeight) {\n            styles.minCellHeight = styles.rowHeight;\n        }\n    }\n    else if (styles.columnWidth) {\n        console.error('Use of deprecated style columnWidth. It is renamed to cellWidth.');\n        if (!styles.cellWidth) {\n            styles.cellWidth = styles.columnWidth;\n        }\n    }\n}\n\n\n/***/ }),\n\n/***/ 260:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Column = exports.Cell = exports.Row = exports.Table = void 0;\nvar config_1 = __webpack_require__(796);\nvar HookData_1 = __webpack_require__(172);\nvar common_1 = __webpack_require__(420);\nvar Table = /** @class */ (function () {\n    function Table(input, content) {\n        this.pageNumber = 1;\n        // Deprecated, use pageNumber instead\n        // Not using getter since:\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/596\n        this.pageCount = 1;\n        this.id = input.id;\n        this.settings = input.settings;\n        this.styles = input.styles;\n        this.hooks = input.hooks;\n        this.columns = content.columns;\n        this.head = content.head;\n        this.body = content.body;\n        this.foot = content.foot;\n    }\n    Table.prototype.getHeadHeight = function (columns) {\n        return this.head.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.getFootHeight = function (columns) {\n        return this.foot.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.allRows = function () {\n        return this.head.concat(this.body).concat(this.foot);\n    };\n    Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n        for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n            var handler = handlers_1[_i];\n            var data = new HookData_1.CellHookData(doc, this, cell, row, column, cursor);\n            var result = handler(data) === false;\n            // Make sure text is always string[] since user can assign string\n            cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n            if (result) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Table.prototype.callEndPageHooks = function (doc, cursor) {\n        doc.applyStyles(doc.userStyles);\n        for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData_1.HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n        for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData_1.HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.getWidth = function (pageWidth) {\n        if (typeof this.settings.tableWidth === 'number') {\n            return this.settings.tableWidth;\n        }\n        else if (this.settings.tableWidth === 'wrap') {\n            var wrappedWidth = this.columns.reduce(function (total, col) { return total + col.wrappedWidth; }, 0);\n            return wrappedWidth;\n        }\n        else {\n            var margin = this.settings.margin;\n            return pageWidth - margin.left - margin.right;\n        }\n    };\n    return Table;\n}());\nexports.Table = Table;\nvar Row = /** @class */ (function () {\n    function Row(raw, index, section, cells, spansMultiplePages) {\n        if (spansMultiplePages === void 0) { spansMultiplePages = false; }\n        this.height = 0;\n        this.raw = raw;\n        if (raw instanceof config_1.HtmlRowInput) {\n            this.raw = raw._element;\n            this.element = raw._element;\n        }\n        this.index = index;\n        this.section = section;\n        this.cells = cells;\n        this.spansMultiplePages = spansMultiplePages;\n    }\n    Row.prototype.getMaxCellHeight = function (columns) {\n        var _this = this;\n        return columns.reduce(function (acc, column) { var _a; return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0);\n    };\n    Row.prototype.hasRowSpan = function (columns) {\n        var _this = this;\n        return (columns.filter(function (column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return false;\n            return cell.rowSpan > 1;\n        }).length > 0);\n    };\n    Row.prototype.canEntireRowFit = function (height, columns) {\n        return this.getMaxCellHeight(columns) <= height;\n    };\n    Row.prototype.getMinimumRowHeight = function (columns, doc) {\n        var _this = this;\n        return columns.reduce(function (acc, column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return 0;\n            var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n            var vPadding = cell.padding('vertical');\n            var oneRowHeight = vPadding + lineHeight;\n            return oneRowHeight > acc ? oneRowHeight : acc;\n        }, 0);\n    };\n    return Row;\n}());\nexports.Row = Row;\nvar Cell = /** @class */ (function () {\n    function Cell(raw, styles, section) {\n        var _a, _b;\n        this.contentHeight = 0;\n        this.contentWidth = 0;\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.height = 0;\n        this.x = 0;\n        this.y = 0;\n        this.styles = styles;\n        this.section = section;\n        this.raw = raw;\n        var content = raw;\n        if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n            this.rowSpan = raw.rowSpan || 1;\n            this.colSpan = raw.colSpan || 1;\n            content = (_b = (_a = raw.content) !== null && _a !== void 0 ? _a : raw.title) !== null && _b !== void 0 ? _b : raw;\n            if (raw._element) {\n                this.raw = raw._element;\n            }\n        }\n        else {\n            this.rowSpan = 1;\n            this.colSpan = 1;\n        }\n        // Stringify 0 and false, but not undefined or null\n        var text = content != null ? '' + content : '';\n        var splitRegex = /\\r\\n|\\r|\\n/g;\n        this.text = text.split(splitRegex);\n    }\n    Cell.prototype.getTextPos = function () {\n        var y;\n        if (this.styles.valign === 'top') {\n            y = this.y + this.padding('top');\n        }\n        else if (this.styles.valign === 'bottom') {\n            y = this.y + this.height - this.padding('bottom');\n        }\n        else {\n            var netHeight = this.height - this.padding('vertical');\n            y = this.y + netHeight / 2 + this.padding('top');\n        }\n        var x;\n        if (this.styles.halign === 'right') {\n            x = this.x + this.width - this.padding('right');\n        }\n        else if (this.styles.halign === 'center') {\n            var netWidth = this.width - this.padding('horizontal');\n            x = this.x + netWidth / 2 + this.padding('left');\n        }\n        else {\n            x = this.x + this.padding('left');\n        }\n        return { x: x, y: y };\n    };\n    // TODO (v4): replace parameters with only (lineHeight)\n    Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n        if (lineHeightFactor === void 0) { lineHeightFactor = 1.15; }\n        var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n        var lineHeight = (this.styles.fontSize / scaleFactor) * lineHeightFactor;\n        var height = lineCount * lineHeight + this.padding('vertical');\n        return Math.max(height, this.styles.minCellHeight);\n    };\n    Cell.prototype.padding = function (name) {\n        var padding = (0, common_1.parseSpacing)(this.styles.cellPadding, 0);\n        if (name === 'vertical') {\n            return padding.top + padding.bottom;\n        }\n        else if (name === 'horizontal') {\n            return padding.left + padding.right;\n        }\n        else {\n            return padding[name];\n        }\n    };\n    return Cell;\n}());\nexports.Cell = Cell;\nvar Column = /** @class */ (function () {\n    function Column(dataKey, raw, index) {\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.dataKey = dataKey;\n        this.raw = raw;\n        this.index = index;\n    }\n    Column.prototype.getMaxCustomCellWidth = function (table) {\n        var max = 0;\n        for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n            var row = _a[_i];\n            var cell = row.cells[this.index];\n            if (cell && typeof cell.styles.cellWidth === 'number') {\n                max = Math.max(max, cell.styles.cellWidth);\n            }\n        }\n        return max;\n    };\n    return Column;\n}());\nexports.Column = Column;\n\n\n/***/ }),\n\n/***/ 356:\n/***/ (function(__unused_webpack_module, exports) {\n\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.assign = void 0;\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n    if (target == null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n    }\n    var to = Object(target);\n    for (var index = 1; index < arguments.length; index++) {\n        // eslint-disable-next-line prefer-rest-params\n        var nextSource = arguments[index];\n        if (nextSource != null) {\n            // Skip over if undefined or null\n            for (var nextKey in nextSource) {\n                // Avoid bugs when hasOwnProperty is shadowed\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                }\n            }\n        }\n    }\n    return to;\n}\nexports.assign = assign;\n\n\n/***/ }),\n\n/***/ 972:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createTable = void 0;\nvar documentHandler_1 = __webpack_require__(744);\nvar models_1 = __webpack_require__(260);\nvar widthCalculator_1 = __webpack_require__(324);\nvar config_1 = __webpack_require__(796);\nvar polyfills_1 = __webpack_require__(356);\nfunction createTable(jsPDFDoc, input) {\n    var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n    var content = parseContent(input, doc.scaleFactor());\n    var table = new models_1.Table(input, content);\n    (0, widthCalculator_1.calculateWidths)(doc, table);\n    doc.applyStyles(doc.userStyles);\n    return table;\n}\nexports.createTable = createTable;\nfunction parseContent(input, sf) {\n    var content = input.content;\n    var columns = createColumns(content.columns);\n    // If no head or foot is set, try generating it with content from columns\n    if (content.head.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'head');\n        if (sectionRow)\n            content.head.push(sectionRow);\n    }\n    if (content.foot.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'foot');\n        if (sectionRow)\n            content.foot.push(sectionRow);\n    }\n    var theme = input.settings.theme;\n    var styles = input.styles;\n    return {\n        columns: columns,\n        head: parseSection('head', content.head, columns, styles, theme, sf),\n        body: parseSection('body', content.body, columns, styles, theme, sf),\n        foot: parseSection('foot', content.foot, columns, styles, theme, sf),\n    };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n    var rowSpansLeftForColumn = {};\n    var result = sectionRows.map(function (rawRow, rowIndex) {\n        var skippedRowForRowSpans = 0;\n        var cells = {};\n        var colSpansAdded = 0;\n        var columnSpansLeft = 0;\n        for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n            var column = columns_1[_i];\n            if (rowSpansLeftForColumn[column.index] == null ||\n                rowSpansLeftForColumn[column.index].left === 0) {\n                if (columnSpansLeft === 0) {\n                    var rawCell = void 0;\n                    if (Array.isArray(rawRow)) {\n                        rawCell =\n                            rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                    }\n                    else {\n                        rawCell = rawRow[column.dataKey];\n                    }\n                    var cellInputStyles = {};\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                        cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                    }\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                    var cell = new models_1.Cell(rawCell, styles, sectionName);\n                    // dataKey is not used internally no more but keep for\n                    // backwards compat in hooks\n                    cells[column.dataKey] = cell;\n                    cells[column.index] = cell;\n                    columnSpansLeft = cell.colSpan - 1;\n                    rowSpansLeftForColumn[column.index] = {\n                        left: cell.rowSpan - 1,\n                        times: columnSpansLeft,\n                    };\n                }\n                else {\n                    columnSpansLeft--;\n                    colSpansAdded++;\n                }\n            }\n            else {\n                rowSpansLeftForColumn[column.index].left--;\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                skippedRowForRowSpans++;\n            }\n        }\n        return new models_1.Row(rawRow, rowIndex, sectionName, cells);\n    });\n    return result;\n}\nfunction generateSectionRow(columns, section) {\n    var sectionRow = {};\n    columns.forEach(function (col) {\n        if (col.raw != null) {\n            var title = getSectionTitle(section, col.raw);\n            if (title != null)\n                sectionRow[col.dataKey] = title;\n        }\n    });\n    return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n    if (section === 'head') {\n        if (typeof column === 'object') {\n            return column.header || column.title || null;\n        }\n        else if (typeof column === 'string' || typeof column === 'number') {\n            return column;\n        }\n    }\n    else if (section === 'foot' && typeof column === 'object') {\n        return column.footer;\n    }\n    return null;\n}\nfunction createColumns(columns) {\n    return columns.map(function (input, index) {\n        var _a, _b;\n        var key;\n        if (typeof input === 'object') {\n            key = (_b = (_a = input.dataKey) !== null && _a !== void 0 ? _a : input.key) !== null && _b !== void 0 ? _b : index;\n        }\n        else {\n            key = index;\n        }\n        return new models_1.Column(key, input, index);\n    });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n    var theme = (0, config_1.getTheme)(themeName);\n    var sectionStyles;\n    if (sectionName === 'head') {\n        sectionStyles = styles.headStyles;\n    }\n    else if (sectionName === 'body') {\n        sectionStyles = styles.bodyStyles;\n    }\n    else if (sectionName === 'foot') {\n        sectionStyles = styles.footStyles;\n    }\n    var otherStyles = (0, polyfills_1.assign)({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n    var columnStyles = styles.columnStyles[column.dataKey] ||\n        styles.columnStyles[column.index] ||\n        {};\n    var colStyles = sectionName === 'body' ? columnStyles : {};\n    var rowStyles = sectionName === 'body' && rowIndex % 2 === 0\n        ? (0, polyfills_1.assign)({}, theme.alternateRow, styles.alternateRowStyles)\n        : {};\n    var defaultStyle = (0, config_1.defaultStyles)(scaleFactor);\n    var themeStyles = (0, polyfills_1.assign)({}, defaultStyle, otherStyles, rowStyles, colStyles);\n    return (0, polyfills_1.assign)(themeStyles, cellInputStyles);\n}\n\n\n/***/ }),\n\n/***/ 664:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.addPage = exports.drawTable = void 0;\nvar common_1 = __webpack_require__(420);\nvar models_1 = __webpack_require__(260);\nvar documentHandler_1 = __webpack_require__(744);\nvar polyfills_1 = __webpack_require__(356);\nvar autoTableText_1 = __webpack_require__(136);\nvar tablePrinter_1 = __webpack_require__(224);\nfunction drawTable(jsPDFDoc, table) {\n    var settings = table.settings;\n    var startY = settings.startY;\n    var margin = settings.margin;\n    var cursor = {\n        x: margin.left,\n        y: startY,\n    };\n    var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n    if (settings.pageBreak === 'avoid') {\n        var rows = table.body;\n        var tableHeight = rows.reduce(function (acc, row) { return acc + row.height; }, 0);\n        minTableBottomPos += tableHeight;\n    }\n    var doc = new documentHandler_1.DocHandler(jsPDFDoc);\n    if (settings.pageBreak === 'always' ||\n        (settings.startY != null && minTableBottomPos > doc.pageSize().height)) {\n        nextPage(doc);\n        cursor.y = margin.top;\n    }\n    table.callWillDrawPageHooks(doc, cursor);\n    var startPos = (0, polyfills_1.assign)({}, cursor);\n    table.startPageNumber = doc.pageNumber();\n    if (settings.horizontalPageBreak) {\n        // managed flow for split columns\n        printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n    }\n    else {\n        // normal flow\n        doc.applyStyles(doc.userStyles);\n        if (settings.showHead === 'firstPage' ||\n            settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n        doc.applyStyles(doc.userStyles);\n        table.body.forEach(function (row, index) {\n            var isLastRow = index === table.body.length - 1;\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n        });\n        doc.applyStyles(doc.userStyles);\n        if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n            table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n    }\n    (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n    table.callEndPageHooks(doc, cursor);\n    table.finalY = cursor.y;\n    jsPDFDoc.lastAutoTable = table;\n    jsPDFDoc.previousAutoTable = table; // Deprecated\n    if (jsPDFDoc.autoTable)\n        jsPDFDoc.autoTable.previous = table; // Deprecated\n    doc.applyStyles(doc.userStyles);\n}\nexports.drawTable = drawTable;\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n    // calculate width of columns and render only those which can fit into page\n    var allColumnsCanFitResult = (0, tablePrinter_1.calculateAllColumnsCanFitInPage)(doc, table);\n    var settings = table.settings;\n    if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n        allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n            doc.applyStyles(doc.userStyles);\n            // add page to print next columns in new page\n            if (index > 0) {\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n            }\n            else {\n                // print head for selected columns\n                printHead(doc, table, cursor, colsAndIndexes.columns);\n            }\n            // print body & footer for selected columns\n            printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n            printFoot(doc, table, cursor, colsAndIndexes.columns);\n        });\n    }\n    else {\n        var lastRowIndexOfLastPage_1 = -1;\n        var firstColumnsToFitResult = allColumnsCanFitResult[0];\n        var _loop_1 = function () {\n            // Print the first columns, taking note of the last row printed\n            var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n            if (firstColumnsToFitResult) {\n                doc.applyStyles(doc.userStyles);\n                var firstColumnsToFit = firstColumnsToFitResult.columns;\n                if (lastRowIndexOfLastPage_1 >= 0) {\n                    // When adding a page here, make sure not to print the footers\n                    // because they were already printed before on this same loop\n                    addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n                }\n                else {\n                    printHead(doc, table, cursor, firstColumnsToFit);\n                }\n                lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n                printFoot(doc, table, cursor, firstColumnsToFit);\n            }\n            // Check how many rows were printed, so that the next columns would not print more rows than that\n            var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n            // Print the next columns, never exceding maxNumberOfRows\n            allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n                doc.applyStyles(doc.userStyles);\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n                printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n                printFoot(doc, table, cursor, colsAndIndexes.columns);\n            });\n            lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n        };\n        while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n            _loop_1();\n        }\n    }\n}\nfunction printHead(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n        var isLastRow = index === table.body.length - 1;\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n    doc.applyStyles(doc.userStyles);\n    maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n    var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n    var lastPrintedRowIndex = -1;\n    table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n        var isLastRow = startRowIndex + index === table.body.length - 1;\n        var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n        if (row.canEntireRowFit(remainingSpace, columns)) {\n            printRow(doc, table, row, cursor, columns);\n            lastPrintedRowIndex = startRowIndex + index;\n        }\n    });\n    return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n    var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n    var vPadding = cell.padding('vertical');\n    var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n    return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n    var cells = {};\n    row.spansMultiplePages = true;\n    row.height = 0;\n    var rowHeight = 0;\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n        var column = _a[_i];\n        var cell = row.cells[column.index];\n        if (!cell)\n            continue;\n        if (!Array.isArray(cell.text)) {\n            cell.text = [cell.text];\n        }\n        var remainderCell = new models_1.Cell(cell.raw, cell.styles, cell.section);\n        remainderCell = (0, polyfills_1.assign)(remainderCell, cell);\n        remainderCell.text = [];\n        var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n        if (cell.text.length > remainingLineCount) {\n            remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n        }\n        var scaleFactor = doc.scaleFactor();\n        var lineHeightFactor = doc.getLineHeightFactor();\n        cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (cell.contentHeight >= remainingPageSpace) {\n            cell.contentHeight = remainingPageSpace;\n            remainderCell.styles.minCellHeight -= remainingPageSpace;\n        }\n        if (cell.contentHeight > row.height) {\n            row.height = cell.contentHeight;\n        }\n        remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (remainderCell.contentHeight > rowHeight) {\n            rowHeight = remainderCell.contentHeight;\n        }\n        cells[column.index] = remainderCell;\n    }\n    var remainderRow = new models_1.Row(row.raw, -1, row.section, cells, true);\n    remainderRow.height = rowHeight;\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n        var column = _c[_b];\n        var remainderCell = remainderRow.cells[column.index];\n        if (remainderCell) {\n            remainderCell.height = remainderRow.height;\n        }\n        var cell = row.cells[column.index];\n        if (cell) {\n            cell.height = row.height;\n        }\n    }\n    return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n    var pageHeight = doc.pageSize().height;\n    var margin = table.settings.margin;\n    var marginHeight = margin.top + margin.bottom;\n    var maxRowHeight = pageHeight - marginHeight;\n    if (row.section === 'body') {\n        // Should also take into account that head and foot is not\n        // on every page with some settings\n        maxRowHeight -=\n            table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    }\n    var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n    var minRowFits = minRowHeight < remainingPageSpace;\n    if (minRowHeight > maxRowHeight) {\n        console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n        return true;\n    }\n    if (!minRowFits) {\n        return false;\n    }\n    var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n    var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n    if (rowHigherThanPage) {\n        if (rowHasRowSpanCell) {\n            console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n        }\n        return true;\n    }\n    if (rowHasRowSpanCell) {\n        // Currently a new page is required whenever a rowspan row don't fit a page.\n        return false;\n    }\n    if (table.settings.rowPageBreak === 'avoid') {\n        return false;\n    }\n    // In all other cases print the row on current page\n    return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n        // The row fits in the current page\n        printRow(doc, table, row, cursor, columns);\n    }\n    else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n        // The row gets split in two here, each piece in one page\n        var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n        printRow(doc, table, row, cursor, columns);\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n    }\n    else {\n        // The row get printed entirelly on the next page\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n    cursor.x = table.settings.margin.left;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n        var column = columns_1[_i];\n        var cell = row.cells[column.index];\n        if (!cell) {\n            cursor.x += column.width;\n            continue;\n        }\n        doc.applyStyles(cell.styles);\n        cell.x = cursor.x;\n        cell.y = cursor.y;\n        var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n        if (result === false) {\n            cursor.x += column.width;\n            continue;\n        }\n        drawCellRect(doc, cell, cursor);\n        var textPos = cell.getTextPos();\n        (0, autoTableText_1.default)(cell.text, textPos.x, textPos.y, {\n            halign: cell.styles.halign,\n            valign: cell.styles.valign,\n            maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right')),\n        }, doc.getDocument());\n        table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n        cursor.x += column.width;\n    }\n    cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n    var cellStyles = cell.styles;\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n    // TODO (v4): better solution?\n    doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n    if (typeof cellStyles.lineWidth === 'number') {\n        // Draw cell background with normal borders\n        var fillStyle = (0, common_1.getFillStyle)(cellStyles.lineWidth, cellStyles.fillColor);\n        if (fillStyle) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n        }\n    }\n    else if (typeof cellStyles.lineWidth === 'object') {\n        // Draw cell background\n        if (cellStyles.fillColor) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n        }\n        // Draw cell individual borders\n        drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n    }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n    var x1, y1, x2, y2;\n    if (lineWidth.top) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.top, x1, y1, x2, y2);\n    }\n    if (lineWidth.bottom) {\n        x1 = cursor.x;\n        y1 = cursor.y + cell.height;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.bottom, x1, y1, x2, y2);\n    }\n    if (lineWidth.left) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.left, x1, y1, x2, y2);\n    }\n    if (lineWidth.right) {\n        x1 = cursor.x + cell.width;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.right, x1, y1, x2, y2);\n    }\n    function drawLine(width, x1, y1, x2, y2) {\n        doc.getDocument().setLineWidth(width);\n        doc.getDocument().line(x1, y1, x2, y2, 'S');\n    }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n    var bottomContentHeight = table.settings.margin.bottom;\n    var showFoot = table.settings.showFoot;\n    if (showFoot === 'everyPage' || (showFoot === 'lastPage' && isLastRow)) {\n        bottomContentHeight += table.getFootHeight(table.columns);\n    }\n    return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n    if (columns === void 0) { columns = []; }\n    if (suppressFooter === void 0) { suppressFooter = false; }\n    doc.applyStyles(doc.userStyles);\n    if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n    // Add user content just before adding new page ensure it will\n    // be drawn above other things on the page\n    table.callEndPageHooks(doc, cursor);\n    var margin = table.settings.margin;\n    (0, common_1.addTableBorder)(doc, table, startPos, cursor);\n    nextPage(doc);\n    table.pageNumber++;\n    table.pageCount++;\n    cursor.x = margin.left;\n    cursor.y = margin.top;\n    startPos.y = margin.top;\n    // call didAddPage hooks before any content is added to the page\n    table.callWillDrawPageHooks(doc, cursor);\n    if (table.settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n        doc.applyStyles(doc.userStyles);\n    }\n}\nexports.addPage = addPage;\nfunction nextPage(doc) {\n    var current = doc.pageNumber();\n    doc.setPage(current + 1);\n    var newCurrent = doc.pageNumber();\n    if (newCurrent === current) {\n        doc.addPage();\n        return true;\n    }\n    return false;\n}\n\n\n/***/ }),\n\n/***/ 224:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.calculateAllColumnsCanFitInPage = void 0;\nvar common_1 = __webpack_require__(420);\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n    var _a;\n    if (config === void 0) { config = {}; }\n    // Get page width\n    var remainingWidth = (0, common_1.getPageAvailableWidth)(doc, table);\n    // Get column data key to repeat\n    var repeatColumnsMap = new Map();\n    var colIndexes = [];\n    var columns = [];\n    var horizontalPageBreakRepeat = [];\n    table.settings.horizontalPageBreakRepeat;\n    if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n        horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n        // It can be a single value of type string or number (even number: 0)\n    }\n    else if (typeof table.settings.horizontalPageBreakRepeat === 'string' ||\n        typeof table.settings.horizontalPageBreakRepeat === 'number') {\n        horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n    }\n    // Code to repeat the given column in split pages\n    horizontalPageBreakRepeat.forEach(function (field) {\n        var col = table.columns.find(function (item) { return item.dataKey === field || item.index === field; });\n        if (col && !repeatColumnsMap.has(col.index)) {\n            repeatColumnsMap.set(col.index, true);\n            colIndexes.push(col.index);\n            columns.push(table.columns[col.index]);\n            remainingWidth -= col.wrappedWidth;\n        }\n    });\n    var first = true;\n    var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n    while (i < table.columns.length) {\n        // Prevent duplicates\n        if (repeatColumnsMap.has(i)) {\n            i++;\n            continue;\n        }\n        var colWidth = table.columns[i].wrappedWidth;\n        // Take at least one column even if it doesn't fit\n        if (first || remainingWidth >= colWidth) {\n            first = false;\n            colIndexes.push(i);\n            columns.push(table.columns[i]);\n            remainingWidth -= colWidth;\n        }\n        else {\n            break;\n        }\n        i++;\n    }\n    return { colIndexes: colIndexes, columns: columns, lastIndex: i - 1 };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n    var allResults = [];\n    for (var i = 0; i < table.columns.length; i++) {\n        var result = getColumnsCanFitInPage(doc, table, { start: i });\n        if (result.columns.length) {\n            allResults.push(result);\n            i = result.lastIndex;\n        }\n    }\n    return allResults;\n}\nexports.calculateAllColumnsCanFitInPage = calculateAllColumnsCanFitInPage;\n\n\n/***/ }),\n\n/***/ 324:\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ellipsize = exports.resizeColumns = exports.calculateWidths = void 0;\nvar common_1 = __webpack_require__(420);\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n    calculate(doc, table);\n    var resizableColumns = [];\n    var initialTableWidth = 0;\n    table.columns.forEach(function (column) {\n        var customWidth = column.getMaxCustomCellWidth(table);\n        if (customWidth) {\n            // final column width\n            column.width = customWidth;\n        }\n        else {\n            // initial column width (will be resized)\n            column.width = column.wrappedWidth;\n            resizableColumns.push(column);\n        }\n        initialTableWidth += column.width;\n    });\n    // width difference that needs to be distributed\n    var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n    // first resize attempt: with respect to minReadableWidth and minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n            return Math.max(column.minReadableWidth, column.minWidth);\n        });\n    }\n    // second resize attempt: ignore minReadableWidth but respect minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) { return column.minWidth; });\n    }\n    resizeWidth = Math.abs(resizeWidth);\n    if (!table.settings.horizontalPageBreak &&\n        resizeWidth > 0.1 / doc.scaleFactor()) {\n        // Table can't get smaller due to custom-width or minWidth restrictions\n        // We can't really do much here. Up to user to for example\n        // reduce font size, increase page size or remove custom cell widths\n        // to allow more columns to be reduced in size\n        resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n        console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n    }\n    applyColSpans(table);\n    fitContent(table, doc);\n    applyRowSpans(table);\n}\nexports.calculateWidths = calculateWidths;\nfunction calculate(doc, table) {\n    var sf = doc.scaleFactor();\n    var horizontalPageBreak = table.settings.horizontalPageBreak;\n    var availablePageWidth = (0, common_1.getPageAvailableWidth)(doc, table);\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            var hooks = table.hooks.didParseCell;\n            table.callCellHooks(doc, hooks, cell, row, column, null);\n            var padding = cell.padding('horizontal');\n            cell.contentWidth = (0, common_1.getStringWidth)(cell.text, cell.styles, doc) + padding;\n            // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n            // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n            // them in the split process to ensure correct word separation and width\n            // calculation.\n            var longestWordWidth = (0, common_1.getStringWidth)(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n            cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n            if (typeof cell.styles.cellWidth === 'number') {\n                cell.minWidth = cell.styles.cellWidth;\n                cell.wrappedWidth = cell.styles.cellWidth;\n            }\n            else if (cell.styles.cellWidth === 'wrap' ||\n                horizontalPageBreak === true) {\n                // cell width should not be more than available page width\n                if (cell.contentWidth > availablePageWidth) {\n                    cell.minWidth = availablePageWidth;\n                    cell.wrappedWidth = availablePageWidth;\n                }\n                else {\n                    cell.minWidth = cell.contentWidth;\n                    cell.wrappedWidth = cell.contentWidth;\n                }\n            }\n            else {\n                // auto\n                var defaultMinWidth = 10 / sf;\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                cell.wrappedWidth = cell.contentWidth;\n                if (cell.minWidth > cell.wrappedWidth) {\n                    cell.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n            // Could probably be improved upon however.\n            if (cell && cell.colSpan === 1) {\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n            }\n            else {\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\n                // or if the column only have colspan cells. Since the width of colspan cells\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\n                // user to at least do it manually.\n                // Note that this is not perfect for now since for example row and table styles are\n                // not accounted for\n                var columnStyles = table.styles.columnStyles[column.dataKey] ||\n                    table.styles.columnStyles[column.index] ||\n                    {};\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                if (cellWidth && typeof cellWidth === 'number') {\n                    column.minWidth = cellWidth;\n                    column.wrappedWidth = cellWidth;\n                }\n            }\n            if (cell) {\n                // Make sure all columns get at least min width even though width calculations are not based on them\n                if (cell.colSpan > 1 && !column.minWidth) {\n                    column.minWidth = cell.minWidth;\n                }\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\n                    column.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n    var initialResizeWidth = resizeWidth;\n    var sumWrappedWidth = columns.reduce(function (acc, column) { return acc + column.wrappedWidth; }, 0);\n    for (var i = 0; i < columns.length; i++) {\n        var column = columns[i];\n        var ratio = column.wrappedWidth / sumWrappedWidth;\n        var suggestedChange = initialResizeWidth * ratio;\n        var suggestedWidth = column.width + suggestedChange;\n        var minWidth = getMinWidth(column);\n        var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n        resizeWidth -= newWidth - column.width;\n        column.width = newWidth;\n    }\n    resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n    // Run the resizer again if there's remaining width needs\n    // to be distributed and there're columns that can be resized\n    if (resizeWidth) {\n        var resizableColumns = columns.filter(function (column) {\n            return resizeWidth < 0\n                ? column.width > getMinWidth(column) // check if column can shrink\n                : true; // check if column can grow\n        });\n        if (resizableColumns.length) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n        }\n    }\n    return resizeWidth;\n}\nexports.resizeColumns = resizeColumns;\nfunction applyRowSpans(table) {\n    var rowSpanCells = {};\n    var colRowSpansLeft = 1;\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var data = rowSpanCells[column.index];\n            if (colRowSpansLeft > 1) {\n                colRowSpansLeft--;\n                delete row.cells[column.index];\n            }\n            else if (data) {\n                data.cell.height += row.height;\n                colRowSpansLeft = data.cell.colSpan;\n                delete row.cells[column.index];\n                data.left--;\n                if (data.left <= 1) {\n                    delete rowSpanCells[column.index];\n                }\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell) {\n                    continue;\n                }\n                cell.height = row.height;\n                if (cell.rowSpan > 1) {\n                    var remaining = all.length - rowIndex;\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                    rowSpanCells[column.index] = { cell: cell, left: left, row: row };\n                }\n            }\n        }\n    }\n}\nfunction applyColSpans(table) {\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        var colSpanCell = null;\n        var combinedColSpanWidth = 0;\n        var colSpansLeft = 0;\n        for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n            var column = table.columns[columnIndex];\n            // Width and colspan\n            colSpansLeft -= 1;\n            if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                combinedColSpanWidth += column.width;\n                delete row.cells[column.index];\n            }\n            else if (colSpanCell) {\n                var cell = colSpanCell;\n                delete row.cells[column.index];\n                colSpanCell = null;\n                cell.width = column.width + combinedColSpanWidth;\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell)\n                    continue;\n                colSpansLeft = cell.colSpan;\n                combinedColSpanWidth = 0;\n                if (cell.colSpan > 1) {\n                    colSpanCell = cell;\n                    combinedColSpanWidth += column.width;\n                    continue;\n                }\n                cell.width = column.width + combinedColSpanWidth;\n            }\n        }\n    }\n}\nfunction fitContent(table, doc) {\n    var rowSpanHeight = { count: 0, height: 0 };\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n        var row = _a[_i];\n        for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n            var column = _c[_b];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            doc.applyStyles(cell.styles, true);\n            var textSpace = cell.width - cell.padding('horizontal');\n            if (cell.styles.overflow === 'linebreak') {\n                // Add one pt to textSpace to fix rounding error\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), { fontSize: cell.styles.fontSize });\n            }\n            else if (cell.styles.overflow === 'ellipsize') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n            }\n            else if (cell.styles.overflow === 'hidden') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n            }\n            else if (typeof cell.styles.overflow === 'function') {\n                var result = cell.styles.overflow(cell.text, textSpace);\n                if (typeof result === 'string') {\n                    cell.text = [result];\n                }\n                else {\n                    cell.text = result;\n                }\n            }\n            cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n            var realContentHeight = cell.contentHeight / cell.rowSpan;\n            if (cell.rowSpan > 1 &&\n                rowSpanHeight.count * rowSpanHeight.height <\n                    realContentHeight * cell.rowSpan) {\n                rowSpanHeight = { height: realContentHeight, count: cell.rowSpan };\n            }\n            else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                if (rowSpanHeight.height > realContentHeight) {\n                    realContentHeight = rowSpanHeight.height;\n                }\n            }\n            if (realContentHeight > row.height) {\n                row.height = realContentHeight;\n            }\n        }\n        rowSpanHeight.count--;\n    }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n    return text.map(function (str) { return ellipsizeStr(str, width, styles, doc, overflow); });\n}\nexports.ellipsize = ellipsize;\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n    var precision = 10000 * doc.scaleFactor();\n    width = Math.ceil(width * precision) / precision;\n    if (width >= (0, common_1.getStringWidth)(text, styles, doc)) {\n        return text;\n    }\n    while (width < (0, common_1.getStringWidth)(text + overflow, styles, doc)) {\n        if (text.length <= 1) {\n            break;\n        }\n        text = text.substring(0, text.length - 1);\n    }\n    return text.trim() + overflow;\n}\n\n\n/***/ }),\n\n/***/ 964:\n/***/ (function(module) {\n\nif(typeof __WEBPACK_EXTERNAL_MODULE__964__ === 'undefined') { var e = new Error(\"Cannot find module 'undefined'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__964__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n!function() {\nvar exports = __webpack_exports__;\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Cell = exports.Column = exports.Row = exports.Table = exports.CellHookData = exports.__drawTable = exports.__createTable = exports.applyPlugin = void 0;\nvar applyPlugin_1 = __webpack_require__(340);\nvar inputParser_1 = __webpack_require__(776);\nvar tableDrawer_1 = __webpack_require__(664);\nvar tableCalculator_1 = __webpack_require__(972);\nvar models_1 = __webpack_require__(260);\nObject.defineProperty(exports, \"Table\", ({ enumerable: true, get: function () { return models_1.Table; } }));\nvar HookData_1 = __webpack_require__(172);\nObject.defineProperty(exports, \"CellHookData\", ({ enumerable: true, get: function () { return HookData_1.CellHookData; } }));\nvar models_2 = __webpack_require__(260);\nObject.defineProperty(exports, \"Cell\", ({ enumerable: true, get: function () { return models_2.Cell; } }));\nObject.defineProperty(exports, \"Column\", ({ enumerable: true, get: function () { return models_2.Column; } }));\nObject.defineProperty(exports, \"Row\", ({ enumerable: true, get: function () { return models_2.Row; } }));\n// export { applyPlugin } didn't export applyPlugin\n// to index.d.ts for some reason\nfunction applyPlugin(jsPDF) {\n    (0, applyPlugin_1.default)(jsPDF);\n}\nexports.applyPlugin = applyPlugin;\nfunction autoTable(d, options) {\n    var input = (0, inputParser_1.parseInput)(d, options);\n    var table = (0, tableCalculator_1.createTable)(d, input);\n    (0, tableDrawer_1.drawTable)(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n    var input = (0, inputParser_1.parseInput)(d, options);\n    return (0, tableCalculator_1.createTable)(d, input);\n}\nexports.__createTable = __createTable;\nfunction __drawTable(d, table) {\n    (0, tableDrawer_1.drawTable)(d, table);\n}\nexports.__drawTable = __drawTable;\ntry {\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    var jsPDF = __webpack_require__(964);\n    // Webpack imported jspdf instead of jsPDF for some reason\n    // while it seemed to work everywhere else.\n    if (jsPDF.jsPDF)\n        jsPDF = jsPDF.jsPDF;\n    applyPlugin(jsPDF);\n}\ncatch (error) {\n    // Importing jspdf in nodejs environments does not work as of jspdf\n    // 1.5.3 so we need to silence potential errors to support using for example\n    // the nodejs jspdf dist files with the exported applyPlugin\n}\nexports[\"default\"] = autoTable;\n\n}();\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AASA,KAAC,SAAS,iCAAiC,MAAM,SAAS;AACzD,UAAG,OAAO,YAAY,YAAY,OAAO,WAAW;AACnD,eAAO,UAAU,QAAS,SAAS,oCAAoC;AAAE,cAAI;AAAE,mBAAO;AAAA,UAAkB,SAAQ,GAAG;AAAA,UAAC;AAAA,QAAE,EAAE,CAAE;AAAA,eACnH,OAAO,WAAW,cAAc,OAAO;AAC9C,eAAO,CAAC,OAAO,GAAG,OAAO;AAAA,WACrB;AACJ,YAAI,IAAI,OAAO,YAAY,WAAW,QAAS,SAAS,oCAAoC;AAAE,cAAI;AAAE,mBAAO;AAAA,UAAkB,SAAQ,GAAG;AAAA,UAAC;AAAA,QAAE,EAAE,CAAE,IAAI,QAAQ,KAAK,OAAO,CAAC;AACxK,iBAAQ,KAAK,EAAG,EAAC,OAAO,YAAY,WAAW,UAAU,MAAM,CAAC,IAAI,EAAE,CAAC;AAAA,MACxE;AAAA,IACD,GAAG,OAAO,eAAe,cAAc,aAAa,OAAO,YAAS,cAAc,UAAO,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,QAAS,SAAS,kCAAkC;AAC3N;AAAA;AAAA,QAAiB,WAAW;AAClB;AACA,cAAI,sBAAuB;AAAA;AAAA,YAE/B;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAAS;AAGlD,oBAAI,YAAa,QAAQ,KAAK,aAAe,2BAAY;AACrD,sBAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oCAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,sBAAAD,GAAE,YAAYC;AAAA,oBAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,+BAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,oBAAG;AACpG,2BAAO,cAAc,GAAG,CAAC;AAAA,kBAC7B;AACA,yBAAO,SAAU,GAAG,GAAG;AACnB,wBAAI,OAAO,MAAM,cAAc,MAAM;AACjC,4BAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kCAAc,GAAG,CAAC;AAClB,6BAAS,KAAK;AAAE,2BAAK,cAAc;AAAA,oBAAG;AACtC,sBAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,kBACtF;AAAA,gBACJ,EAAG;AACH,uBAAO,eAAeF,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,eAAeA,SAAQ,WAAW;AAC1C,oBAAI;AAAA;AAAA,kBAA0B,2BAAY;AACtC,6BAASG,UAAS,KAAK,OAAO,QAAQ;AAClC,2BAAK,QAAQ;AACb,2BAAK,aAAa,MAAM;AACxB,2BAAK,YAAY,KAAK;AACtB,2BAAK,WAAW,MAAM;AACtB,2BAAK,SAAS;AACd,2BAAK,MAAM,IAAI,YAAY;AAAA,oBAC/B;AACA,2BAAOA;AAAA,kBACX,EAAE;AAAA;AACF,gBAAAH,SAAQ,WAAW;AACnB,oBAAI;AAAA;AAAA,kBAA8B,SAAU,QAAQ;AAChD,8BAAUI,eAAc,MAAM;AAC9B,6BAASA,cAAa,KAAK,OAAO,MAAM,KAAK,QAAQ,QAAQ;AACzD,0BAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK;AACrD,4BAAM,OAAO;AACb,4BAAM,MAAM;AACZ,4BAAM,SAAS;AACf,4BAAM,UAAU,IAAI;AACpB,6BAAO;AAAA,oBACX;AACA,2BAAOA;AAAA,kBACX,EAAE,QAAQ;AAAA;AACV,gBAAAJ,SAAQ,eAAe;AAAA,cAGjB;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAASK,sBAAqB;AAGvE,uBAAO,eAAeL,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,oBAAI,eAAeK,qBAAoB,CAAC;AACxC,oBAAI,kBAAkBA,qBAAoB,GAAG;AAC7C,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,oBAAI,gBAAgBA,qBAAoB,GAAG;AAC3C,oBAAI,gBAAgBA,qBAAoB,GAAG;AAC3C,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,yBAAS,UAAU,OAAO;AAEtB,wBAAM,IAAI,YAAY,WAAY;AAC9B,wBAAI,OAAO,CAAC;AACZ,6BAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,2BAAK,EAAE,IAAI,UAAU,EAAE;AAAA,oBAC3B;AACA,wBAAI;AACJ,wBAAI,KAAK,WAAW,GAAG;AACnB,gCAAU,KAAK,CAAC;AAAA,oBACpB,OACK;AACD,8BAAQ,MAAM,wCAAwC;AACtD,gCAAU,KAAK,CAAC,KAAK,CAAC;AACtB,8BAAQ,UAAU,KAAK,CAAC;AACxB,8BAAQ,OAAO,KAAK,CAAC;AAAA,oBACzB;AACA,wBAAI,SAAS,GAAG,cAAc,YAAY,MAAM,OAAO;AACvD,wBAAI,SAAS,GAAG,kBAAkB,aAAa,MAAM,KAAK;AAC1D,qBAAC,GAAG,cAAc,WAAW,MAAM,KAAK;AACxC,2BAAO;AAAA,kBACX;AAEA,wBAAM,IAAI,gBAAgB;AAC1B,wBAAM,IAAI,oBAAoB;AAC9B,wBAAM,IAAI,UAAU,WAAW;AAC/B,wBAAM,IAAI,gBAAgB,SAAU,MAAM,GAAG,GAAG,QAAQ;AACpD,qBAAC,GAAG,gBAAgB,SAAS,MAAM,GAAG,GAAG,QAAQ,IAAI;AAAA,kBACzD;AACA,wBAAM,IAAI,uBAAuB,SAAU,UAAU;AACjD,sCAAkB,WAAW,YAAY,UAAU,IAAI;AACvD,2BAAO;AAAA,kBACX;AACA,wBAAM,uBAAuB,SAAU,UAAU,KAAK;AAClD,sCAAkB,WAAW,YAAY,UAAU,GAAG;AAAA,kBAC1D;AACA,wBAAM,IAAI,sBAAsB,SAAU,WAAW,uBAAuB;AACxE,wBAAI;AACJ,wBAAI,0BAA0B,QAAQ;AAAE,8CAAwB;AAAA,oBAAO;AACvE,wBAAI,OAAO,WAAW,aAAa;AAC/B,8BAAQ,MAAM,2DAA2D;AACzE,6BAAO;AAAA,oBACX;AACA,wBAAI,MAAM,IAAI,kBAAkB,WAAW,IAAI;AAC/C,wBAAI,MAAM,GAAG,aAAa,WAAW,KAAK,WAAW,QAAQ,uBAAuB,KAAK,GAAG,OAAO,GAAG,MAAM,OAAO,GAAG;AACtH,wBAAI,YAAY,KAAK,KAAK,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,SAAU,GAAG;AAAE,6BAAO,EAAE;AAAA,oBAAS,CAAC,MAAM,CAAC;AACnH,2BAAO,EAAE,SAAkB,MAAM,MAAM,MAAM,KAAK;AAAA,kBACtD;AAIA,wBAAM,IAAI,mBAAmB,WAAY;AACrC,4BAAQ,MAAM,qFAAqF;AACnG,wBAAI,OAAO,KAAK;AAChB,wBAAI,QAAQ,KAAK,QAAQ;AACrB,6BAAO,KAAK;AAAA,oBAChB,OACK;AACD,6BAAO;AAAA,oBACX;AAAA,kBACJ;AAIA,wBAAM,IAAI,0BAA0B,SAAU,MAAM;AAChD,4BAAQ,MAAM,uHAAuH;AACrI,wBAAI,CAAC,MAAM,IAAI,UAAU,gBAAgB;AACrC,4BAAM,IAAI,UAAU,iBAAiB,CAAC;AAAA,oBAC1C;AACA,0BAAM,IAAI,UAAU,eAAe,iBAAiB;AACpD,2BAAO;AAAA,kBACX;AAIA,wBAAM,IAAI,mBAAmB,WAAY;AACrC,4BAAQ,MAAM,iEAAiE;AAC/E,yBAAK,QAAQ;AACb,2BAAO;AAAA,kBACX;AAAA,gBACJ;AACA,gBAAAL,SAAQ,SAAS,IAAI;AAAA,cAGf;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAAS;AAGlD,uBAAO,eAAeA,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAK9D,yBAAS,UAAU,MAAM,GAAG,GAAG,QAAQ,KAAK;AACxC,2BAAS,UAAU,CAAC;AACpB,sBAAI,uBAAuB;AAC3B,sBAAI,IAAI,IAAI,SAAS;AACrB,sBAAI,WAAW,IAAI,SAAS,YAAY,IAAI;AAC5C,sBAAI,mBAAmB,IAAI,sBACrB,IAAI,oBAAoB,IACxB;AACN,sBAAI,aAAa,WAAW;AAC5B,sBAAI,aAAa;AACjB,sBAAI,YAAY;AAChB,sBAAI,YAAY;AAChB,sBAAI,OAAO,WAAW,YAClB,OAAO,WAAW,YAClB,OAAO,WAAW,YAClB,OAAO,WAAW,SAAS;AAC3B,gCAAY,OAAO,SAAS,WAAW,KAAK,MAAM,UAAU,IAAI;AAChE,gCAAY,UAAU,UAAU;AAAA,kBACpC;AAEA,uBAAK,YAAY,IAAI;AACrB,sBAAI,OAAO,WAAW;AAClB,yBAAM,YAAY,IAAK;AAAA,2BAClB,OAAO,WAAW;AACvB,yBAAK,YAAY;AACrB,sBAAI,OAAO,WAAW,YAAY,OAAO,WAAW,SAAS;AACzD,wBAAI,YAAY;AAChB,wBAAI,OAAO,WAAW;AAClB,mCAAa;AACjB,wBAAI,aAAa,aAAa,GAAG;AAC7B,+BAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AACnD,4BAAI,KAAK,UAAU,KAAK,GAAG,IAAI,IAAI,mBAAmB,UAAU,KAAK,CAAC,IAAI,WAAW,CAAC;AACtF,6BAAK;AAAA,sBACT;AACA,6BAAO;AAAA,oBACX;AACA,yBAAK,IAAI,mBAAmB,IAAI,IAAI;AAAA,kBACxC;AACA,sBAAI,OAAO,WAAW,WAAW;AAC7B,wBAAI,KAAK,MAAM,GAAG,GAAG;AAAA,sBACjB,UAAU,OAAO,YAAY;AAAA,sBAC7B,OAAO;AAAA,oBACX,CAAC;AAAA,kBACL,OACK;AACD,wBAAI,KAAK,MAAM,GAAG,CAAC;AAAA,kBACvB;AACA,yBAAO;AAAA,gBACX;AACA,gBAAAA,SAAQ,SAAS,IAAI;AAAA,cAGf;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAAS;AAGlD,uBAAO,eAAeA,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,wBAAwBA,SAAQ,eAAeA,SAAQ,eAAeA,SAAQ,iBAAiBA,SAAQ,iBAAiB;AAChI,yBAAS,eAAe,MAAM,QAAQ,KAAK;AACvC,sBAAI,YAAY,QAAQ,IAAI;AAC5B,sBAAI,UAAU,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAChD,sBAAI,kBAAkB,QACjB,IAAI,SAAUM,OAAM;AAAE,2BAAO,IAAI,aAAaA,KAAI;AAAA,kBAAG,CAAC,EACtD,OAAO,SAAU,GAAG,GAAG;AAAE,2BAAO,KAAK,IAAI,GAAG,CAAC;AAAA,kBAAG,GAAG,CAAC;AACzD,yBAAO;AAAA,gBACX;AACA,gBAAAN,SAAQ,iBAAiB;AACzB,yBAAS,eAAe,KAAK,OAAO,UAAU,QAAQ;AAClD,sBAAI,YAAY,MAAM,SAAS;AAC/B,sBAAI,YAAY,MAAM,SAAS;AAC/B,sBAAI,YAAY,EAAE,WAAsB,UAAqB,CAAC;AAC9D,sBAAI,YAAY,aAAa,WAAW,KAAK;AAC7C,sBAAI,WAAW;AACX,wBAAI,KAAK,SAAS,GAAG,SAAS,GAAG,MAAM,SAAS,IAAI,SAAS,EAAE,KAAK,GAAG,OAAO,IAAI,SAAS,GAAG,SAAS;AAAA,kBAC3G;AAAA,gBACJ;AACA,gBAAAA,SAAQ,iBAAiB;AACzB,yBAAS,aAAa,WAAW,WAAW;AACxC,sBAAI,WAAW,YAAY;AAC3B,sBAAI,iBAAiB,aAAa,cAAc;AAChD,sBAAI,YAAY,gBAAgB;AAC5B,2BAAO;AAAA,kBACX,WACS,UAAU;AACf,2BAAO;AAAA,kBACX,WACS,gBAAgB;AACrB,2BAAO;AAAA,kBACX,OACK;AACD,2BAAO;AAAA,kBACX;AAAA,gBACJ;AACA,gBAAAA,SAAQ,eAAe;AACvB,yBAAS,aAAa,OAAO,cAAc;AACvC,sBAAI,IAAI,IAAI,IAAI;AAChB,0BAAQ,SAAS;AACjB,sBAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,wBAAI,MAAM,UAAU,GAAG;AACnB,6BAAO;AAAA,wBACH,KAAK,MAAM,CAAC;AAAA,wBACZ,OAAO,MAAM,CAAC;AAAA,wBACd,QAAQ,MAAM,CAAC;AAAA,wBACf,MAAM,MAAM,CAAC;AAAA,sBACjB;AAAA,oBACJ,WACS,MAAM,WAAW,GAAG;AACzB,6BAAO;AAAA,wBACH,KAAK,MAAM,CAAC;AAAA,wBACZ,OAAO,MAAM,CAAC;AAAA,wBACd,QAAQ,MAAM,CAAC;AAAA,wBACf,MAAM,MAAM,CAAC;AAAA,sBACjB;AAAA,oBACJ,WACS,MAAM,WAAW,GAAG;AACzB,6BAAO;AAAA,wBACH,KAAK,MAAM,CAAC;AAAA,wBACZ,OAAO,MAAM,CAAC;AAAA,wBACd,QAAQ,MAAM,CAAC;AAAA,wBACf,MAAM,MAAM,CAAC;AAAA,sBACjB;AAAA,oBACJ,WACS,MAAM,WAAW,GAAG;AACzB,8BAAQ,MAAM,CAAC;AAAA,oBACnB,OACK;AACD,8BAAQ;AAAA,oBACZ;AAAA,kBACJ;AACA,sBAAI,OAAO,UAAU,UAAU;AAC3B,wBAAI,OAAO,MAAM,aAAa,UAAU;AACpC,4BAAM,MAAM,MAAM;AAClB,4BAAM,SAAS,MAAM;AAAA,oBACzB;AACA,wBAAI,OAAO,MAAM,eAAe,UAAU;AACtC,4BAAM,QAAQ,MAAM;AACpB,4BAAM,OAAO,MAAM;AAAA,oBACvB;AACA,2BAAO;AAAA,sBACH,OAAO,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,sBACzD,MAAM,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,KAAK;AAAA,sBACvD,QAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK;AAAA,sBAC3D,SAAS,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,oBACjE;AAAA,kBACJ;AACA,sBAAI,OAAO,UAAU,UAAU;AAC3B,4BAAQ;AAAA,kBACZ;AACA,yBAAO,EAAE,KAAK,OAAO,OAAO,OAAO,QAAQ,OAAO,MAAM,MAAM;AAAA,gBAClE;AACA,gBAAAA,SAAQ,eAAe;AACvB,yBAAS,sBAAsB,KAAK,OAAO;AACvC,sBAAI,UAAU,aAAa,MAAM,SAAS,QAAQ,CAAC;AACnD,yBAAO,IAAI,SAAS,EAAE,SAAS,QAAQ,OAAO,QAAQ;AAAA,gBAC1D;AACA,gBAAAA,SAAQ,wBAAwB;AAAA,cAG1B;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAAS;AAGlD,oBAAI,YAAa,QAAQ,KAAK,aAAe,2BAAY;AACrD,sBAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oCAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,sBAAAD,GAAE,YAAYC;AAAA,oBAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,+BAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,oBAAG;AACpG,2BAAO,cAAc,GAAG,CAAC;AAAA,kBAC7B;AACA,yBAAO,SAAU,GAAG,GAAG;AACnB,wBAAI,OAAO,MAAM,cAAc,MAAM;AACjC,4BAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kCAAc,GAAG,CAAC;AAClB,6BAAS,KAAK;AAAE,2BAAK,cAAc;AAAA,oBAAG;AACtC,sBAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,kBACtF;AAAA,gBACJ,EAAG;AACH,uBAAO,eAAeF,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,WAAWA,SAAQ,gBAAgBA,SAAQ,eAAe;AAClE,oBAAI;AAAA;AAAA,kBAA8B,SAAU,QAAQ;AAChD,8BAAUO,eAAc,MAAM;AAC9B,6BAASA,cAAa,SAAS;AAC3B,0BAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,4BAAM,WAAW;AACjB,6BAAO;AAAA,oBACX;AACA,2BAAOA;AAAA,kBACX,EAAE,KAAK;AAAA;AACP,gBAAAP,SAAQ,eAAe;AAEvB,yBAAS,cAAc,aAAa;AAChC,yBAAO;AAAA,oBACH,MAAM;AAAA;AAAA,oBACN,WAAW;AAAA;AAAA,oBACX,UAAU;AAAA;AAAA,oBACV,WAAW;AAAA;AAAA,oBACX,WAAW;AAAA,oBACX,QAAQ;AAAA;AAAA,oBACR,QAAQ;AAAA;AAAA,oBACR,UAAU;AAAA,oBACV,aAAa,IAAI;AAAA;AAAA,oBACjB,WAAW;AAAA,oBACX,WAAW;AAAA,oBACX,WAAW;AAAA;AAAA,oBACX,eAAe;AAAA,oBACf,cAAc;AAAA,kBAClB;AAAA,gBACJ;AACA,gBAAAA,SAAQ,gBAAgB;AACxB,yBAAS,SAAS,MAAM;AACpB,sBAAI,SAAS;AAAA,oBACT,SAAS;AAAA,sBACL,OAAO,EAAE,WAAW,KAAK,WAAW,IAAI,WAAW,SAAS;AAAA,sBAC5D,MAAM,EAAE,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,GAAG,GAAG,WAAW,OAAO;AAAA,sBACrE,MAAM,CAAC;AAAA,sBACP,MAAM,EAAE,WAAW,KAAK,WAAW,CAAC,IAAI,KAAK,GAAG,GAAG,WAAW,OAAO;AAAA,sBACrE,cAAc,EAAE,WAAW,IAAI;AAAA,oBACnC;AAAA,oBACA,MAAM;AAAA,sBACF,OAAO;AAAA,wBACH,WAAW;AAAA,wBACX,WAAW;AAAA,wBACX,WAAW;AAAA,wBACX,WAAW;AAAA,sBACf;AAAA,sBACA,MAAM;AAAA,wBACF,WAAW;AAAA,wBACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,wBACxB,WAAW;AAAA,wBACX,WAAW;AAAA,sBACf;AAAA,sBACA,MAAM,CAAC;AAAA,sBACP,MAAM;AAAA,wBACF,WAAW;AAAA,wBACX,WAAW,CAAC,IAAI,KAAK,GAAG;AAAA,wBACxB,WAAW;AAAA,wBACX,WAAW;AAAA,sBACf;AAAA,sBACA,cAAc,CAAC;AAAA,oBACnB;AAAA,oBACA,OAAO;AAAA,sBACH,MAAM,EAAE,WAAW,OAAO;AAAA,sBAC1B,MAAM,EAAE,WAAW,OAAO;AAAA,oBAC9B;AAAA,kBACJ;AACA,yBAAO,OAAO,IAAI;AAAA,gBACtB;AACA,gBAAAA,SAAQ,WAAW;AAAA,cAGb;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAASK,sBAAqB;AAGvE,uBAAO,eAAeL,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,WAAW;AAInB,oBAAI,WAAWK,qBAAoB,GAAG;AACtC,yBAAS,SAAS,gBAAgB,SAAS,aAAa,OAAOG,SAAQ;AACnE,sBAAI,SAAS,CAAC;AACd,sBAAI,gBAAgB,KAAK;AACzB,sBAAI,kBAAkB,WAAW,SAAS,SAAU,MAAM;AACtD,2BAAOA,QAAO,iBAAiB,IAAI,EAAE,iBAAiB;AAAA,kBAC1D,CAAC;AACD,sBAAI,mBAAmB;AACnB,2BAAO,YAAY;AACvB,sBAAI,YAAY,WAAW,SAAS,SAAU,MAAM;AAChD,2BAAOA,QAAO,iBAAiB,IAAI,EAAE,OAAO;AAAA,kBAChD,CAAC;AACD,sBAAI,aAAa;AACb,2BAAO,YAAY;AACvB,sBAAI,UAAU,aAAa,OAAO,WAAW;AAC7C,sBAAI;AACA,2BAAO,cAAc;AACzB,sBAAI,kBAAkB;AACtB,sBAAI,mBAAmB,gBAAgB;AACvC,sBAAI,MAAM,MAAM;AAChB,sBAAI,MAAM,sBAAsB,OAC5B,MAAM,qBAAqB,OAC3B,MAAM,oBAAoB,KAAK;AAC/B,wBAAI,eAAe,WAAW,GAAG,KAAK,KAAK;AAC3C,wBAAI;AACA,6BAAO,YAAY;AAAA,kBAC3B,OACK;AACD,2BAAO,YAAY;AAAA,sBACf,MAAM,WAAW,MAAM,cAAc,KAAK,KAAK;AAAA,sBAC/C,QAAQ,WAAW,MAAM,gBAAgB,KAAK,KAAK;AAAA,sBACnD,SAAS,WAAW,MAAM,iBAAiB,KAAK,KAAK;AAAA,sBACrD,OAAO,WAAW,MAAM,eAAe,KAAK,KAAK;AAAA,oBACrD;AAGA,wBAAI,CAAC,OAAO,UAAU,KAAK;AACvB,0BAAI,OAAO,UAAU,OAAO;AACxB,0CAAkB;AAAA,sBACtB,WACS,OAAO,UAAU,QAAQ;AAC9B,0CAAkB;AAAA,sBACtB,WACS,OAAO,UAAU,MAAM;AAC5B,0CAAkB;AAAA,sBACtB;AAAA,oBACJ;AAAA,kBACJ;AACA,sBAAI,cAAc,WAAW,SAAS,SAAU,MAAM;AAClD,2BAAOA,QAAO,iBAAiB,IAAI,EAAE,eAAe;AAAA,kBACxD,CAAC;AACD,sBAAI,eAAe;AACf,2BAAO,YAAY;AACvB,sBAAI,WAAW,CAAC,QAAQ,SAAS,UAAU,SAAS;AACpD,sBAAI,SAAS,QAAQ,MAAM,SAAS,MAAM,IAAI;AAC1C,2BAAO,SAAS,MAAM;AAAA,kBAC1B;AACA,6BAAW,CAAC,UAAU,UAAU,KAAK;AACrC,sBAAI,SAAS,QAAQ,MAAM,aAAa,MAAM,IAAI;AAC9C,2BAAO,SAAS,MAAM;AAAA,kBAC1B;AACA,sBAAI,MAAM,SAAS,MAAM,YAAY,EAAE;AACvC,sBAAI,CAAC,MAAM,GAAG;AACV,2BAAO,WAAW,MAAM;AAC5B,sBAAI,YAAY,eAAe,KAAK;AACpC,sBAAI;AACA,2BAAO,YAAY;AACvB,sBAAI,QAAQ,MAAM,cAAc,IAAI,YAAY;AAChD,sBAAI,eAAe,QAAQ,IAAI,MAAM,IAAI;AACrC,2BAAO,OAAO;AAAA,kBAClB;AACA,yBAAO;AAAA,gBACX;AACA,gBAAAR,SAAQ,WAAW;AACnB,yBAAS,eAAe,OAAO;AAC3B,sBAAI,MAAM;AACV,sBAAI,MAAM,eAAe,UACrB,MAAM,eAAe,YACrB,SAAS,MAAM,UAAU,KAAK,KAAK;AACnC,0BAAM;AAAA,kBACV;AACA,sBAAI,MAAM,cAAc,YAAY,MAAM,cAAc,WAAW;AAC/D,2BAAO;AAAA,kBACX;AACA,yBAAO;AAAA,gBACX;AACA,yBAAS,WAAW,SAAS,aAAa;AACtC,sBAAI,WAAW,UAAU,SAAS,WAAW;AAC7C,sBAAI,CAAC;AACD,2BAAO;AACX,sBAAI,OAAO,SAAS,MAAM,wDAAwD;AAClF,sBAAI,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,GAAG;AAC/B,2BAAO;AAAA,kBACX;AACA,sBAAI,QAAQ;AAAA,oBACR,SAAS,KAAK,CAAC,CAAC;AAAA,oBAChB,SAAS,KAAK,CAAC,CAAC;AAAA,oBAChB,SAAS,KAAK,CAAC,CAAC;AAAA,kBACpB;AACA,sBAAI,QAAQ,SAAS,KAAK,CAAC,CAAC;AAC5B,sBAAI,UAAU,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,MAAM,MAAM,CAAC,CAAC,GAAG;AACtE,2BAAO;AAAA,kBACX;AACA,yBAAO;AAAA,gBACX;AACA,yBAAS,UAAU,MAAM,aAAa;AAClC,sBAAI,KAAK,YAAY,IAAI;AACzB,sBAAI,OAAO,sBACP,OAAO,iBACP,OAAO,aACP,OAAO,WAAW;AAClB,wBAAI,KAAK,iBAAiB,MAAM;AAC5B,6BAAO;AAAA,oBACX;AACA,2BAAO,UAAU,KAAK,eAAe,WAAW;AAAA,kBACpD,OACK;AACD,2BAAO;AAAA,kBACX;AAAA,gBACJ;AACA,yBAAS,aAAa,OAAO,aAAa;AACtC,sBAAI,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,MAAM;AAAA,kBACV;AACA,sBAAI,gBAAgB,MAAM,KAAK;AAC/B,sBAAI,eAAe,SAAS,MAAM,UAAU,IAAI,SAAS,MAAM,QAAQ,KAAK,cAAc;AAC1F,sBAAI,eAAe,IAAI,IAAI,SAAU,GAAG;AACpC,2BAAO,SAAS,KAAK,GAAG,IAAI;AAAA,kBAChC,CAAC;AACD,sBAAI,WAAW,GAAG,SAAS,cAAc,cAAc,CAAC;AACxD,sBAAI,cAAc,QAAQ,KAAK;AAC3B,4BAAQ,MAAM;AAAA,kBAClB;AACA,sBAAI,cAAc,QAAQ,QAAQ;AAC9B,4BAAQ,SAAS;AAAA,kBACrB;AACA,yBAAO;AAAA,gBACX;AAAA,cAGM;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAAS;AAGlD,uBAAO,eAAeA,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,aAAa;AACrB,oBAAI,iBAAiB,CAAC;AACtB,oBAAI;AAAA;AAAA,kBAA4B,WAAY;AACxC,6BAASS,YAAW,eAAe;AAC/B,2BAAK,gBAAgB;AACrB,2BAAK,aAAa;AAAA;AAAA,wBAEd,WAAW,cAAc,eACnB,KAAK,cAAc,aAAa,IAChC;AAAA,wBACN,UAAU,cAAc,SAAS,YAAY;AAAA,wBAC7C,WAAW,cAAc,SAAS,QAAQ,EAAE;AAAA,wBAC5C,MAAM,cAAc,SAAS,QAAQ,EAAE;AAAA;AAAA,wBAEvC,WAAW,cAAc,eACnB,KAAK,cAAc,aAAa,IAChC;AAAA;AAAA,wBAEN,WAAW,cAAc,eACnB,KAAK,cAAc,aAAa,IAChC;AAAA,sBACV;AAAA,oBACJ;AACA,oBAAAA,YAAW,cAAc,SAAU,UAAU,KAAK;AAC9C,0BAAI,QAAQ,QAAQ;AAAE,8BAAM;AAAA,sBAAM;AAClC,0BAAI,KAAK;AACL,4BAAI,8BAA8B;AAAA,sBACtC,OACK;AACD,yCAAiB;AAAA,sBACrB;AAAA,oBACJ;AACA,oBAAAA,YAAW,aAAa,SAAU,GAAG;AACjC,0BAAI,MAAM,QAAQ,CAAC,GAAG;AAClB,+BAAO;AAAA,sBACX,WACS,OAAO,MAAM,UAAU;AAC5B,+BAAO,CAAC,GAAG,GAAG,CAAC;AAAA,sBACnB,WACS,OAAO,MAAM,UAAU;AAC5B,+BAAO,CAAC,CAAC;AAAA,sBACb,OACK;AACD,+BAAO;AAAA,sBACX;AAAA,oBACJ;AACA,oBAAAA,YAAW,UAAU,cAAc,SAAU,QAAQ,UAAU;AAG3D,0BAAI,IAAI,IAAI;AACZ,0BAAI,aAAa,QAAQ;AAAE,mCAAW;AAAA,sBAAO;AAC7C,0BAAI,OAAO;AACP,6BAAK,cAAc,gBACf,KAAK,cAAc,aAAa,OAAO,SAAS;AACxD,0BAAI,KAAK,KAAK,cAAc,SAAS,QAAQ,GAAG,YAAY,GAAG,WAAW,WAAW,GAAG;AACxF,0BAAI,OAAO;AACP,mCAAW,OAAO;AACtB,0BAAI,OAAO,WAAW;AAClB,oCAAY,OAAO;AACnB,4BAAI,sBAAsB,KAAK,YAAY,EAAE,QAAQ;AACrD,4BAAI,uBACA,oBAAoB,QAAQ,SAAS,MAAM,IAAI;AAI/C,+BAAK,cAAc,gBACf,KAAK,cAAc,aAAa,oBAAoB,CAAC,CAAC;AAC1D,sCAAY,oBAAoB,CAAC;AAAA,wBACrC;AAAA,sBACJ;AACA,2BAAK,cAAc,QAAQ,UAAU,SAAS;AAC9C,0BAAI,OAAO;AACP,6BAAK,cAAc,YAAY,OAAO,QAAQ;AAClD,0BAAI,UAAU;AACV;AAAA,sBACJ;AACA,0BAAI,QAAQA,YAAW,WAAW,OAAO,SAAS;AAClD,0BAAI;AACA,yBAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AAC1D,8BAAQA,YAAW,WAAW,OAAO,SAAS;AAC9C,0BAAI;AACA,yBAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AAC1D,8BAAQA,YAAW,WAAW,OAAO,SAAS;AAC9C,0BAAI;AACA,yBAAC,KAAK,KAAK,eAAe,aAAa,MAAM,IAAI,KAAK;AAC1D,0BAAI,OAAO,OAAO,cAAc,UAAU;AACtC,6BAAK,cAAc,aAAa,OAAO,SAAS;AAAA,sBACpD;AAAA,oBACJ;AACA,oBAAAA,YAAW,UAAU,kBAAkB,SAAU,MAAM,MAAM,MAAM;AAC/D,6BAAO,KAAK,cAAc,gBAAgB,MAAM,MAAM,IAAI;AAAA,oBAC9D;AASA,oBAAAA,YAAW,UAAU,OAAO,SAAU,GAAG,GAAG,OAAO,QAAQ,WAAW;AAIlE,6BAAO,KAAK,cAAc,KAAK,GAAG,GAAG,OAAO,QAAQ,SAAS;AAAA,oBACjE;AACA,oBAAAA,YAAW,UAAU,mBAAmB,WAAY;AAChD,6BAAO,KAAK,cAAc,iBAAiB;AAAA,oBAC/C;AACA,oBAAAA,YAAW,UAAU,eAAe,SAAU,MAAM;AAChD,6BAAO,KAAK,cAAc,aAAa,IAAI;AAAA,oBAC/C;AACA,oBAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,6BAAO,KAAK;AAAA,oBAChB;AACA,oBAAAA,YAAW,UAAU,UAAU,SAAU,MAAM;AAC3C,2BAAK,cAAc,QAAQ,IAAI;AAAA,oBACnC;AACA,oBAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,6BAAO,KAAK,cAAc,QAAQ;AAAA,oBACtC;AACA,oBAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,6BAAO,KAAK,cAAc,YAAY;AAAA,oBAC1C;AACA,oBAAAA,YAAW,UAAU,mBAAmB,WAAY;AAChD,6BAAO,kBAAkB,CAAC;AAAA,oBAC9B;AACA,oBAAAA,YAAW,UAAU,qBAAqB,WAAY;AAClD,6BAAO,KAAK,cAAc,+BAA+B,CAAC;AAAA,oBAC9D;AACA,oBAAAA,YAAW,UAAU,WAAW,WAAY;AACxC,0BAAI,WAAW,KAAK,cAAc,SAAS;AAE3C,0BAAI,SAAS,SAAS,MAAM;AACxB,mCAAW;AAAA,0BACP,OAAO,SAAS,SAAS;AAAA,0BACzB,QAAQ,SAAS,UAAU;AAAA,wBAC/B;AAAA,sBACJ;AACA,6BAAO;AAAA,oBACX;AACA,oBAAAA,YAAW,UAAU,cAAc,WAAY;AAC3C,6BAAO,KAAK,cAAc,SAAS;AAAA,oBACvC;AACA,oBAAAA,YAAW,UAAU,sBAAsB,WAAY;AACnD,0BAAI,MAAM,KAAK;AACf,6BAAO,IAAI,sBAAsB,IAAI,oBAAoB,IAAI;AAAA,oBACjE;AACA,oBAAAA,YAAW,UAAU,gBAAgB,SAAU,UAAU;AACrD,6BAAQ,WAAW,KAAK,YAAY,IAAK,KAAK,oBAAoB;AAAA,oBACtE;AACA,oBAAAA,YAAW,UAAU,aAAa,WAAY;AAC1C,0BAAI,WAAW,KAAK,cAAc,SAAS,mBAAmB;AAC9D,0BAAI,CAAC,UAAU;AAEX,+BAAO,KAAK,cAAc,SAAS,iBAAiB;AAAA,sBACxD;AACA,6BAAO,SAAS;AAAA,oBACpB;AACA,2BAAOA;AAAA,kBACX,EAAE;AAAA;AACF,gBAAAT,SAAQ,aAAa;AAAA,cAGf;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAASK,sBAAqB;AAGvE,uBAAO,eAAeL,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,YAAY;AACpB,oBAAI,cAAcK,qBAAoB,GAAG;AACzC,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,yBAAS,UAAU,KAAK,OAAOG,SAAQ,mBAAmB,QAAQ;AAC9D,sBAAI,IAAI;AACR,sBAAI,sBAAsB,QAAQ;AAAE,wCAAoB;AAAA,kBAAO;AAC/D,sBAAI,WAAW,QAAQ;AAAE,6BAAS;AAAA,kBAAO;AACzC,sBAAI;AACJ,sBAAI,OAAO,UAAU,UAAU;AAC3B,mCAAeA,QAAO,SAAS,cAAc,KAAK;AAAA,kBACtD,OACK;AACD,mCAAe;AAAA,kBACnB;AACA,sBAAI,iBAAiB,OAAO,KAAK,IAAI,YAAY,CAAC;AAClD,sBAAI,cAAc,IAAI,YAAY;AAClC,sBAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;AAClC,sBAAI,CAAC,cAAc;AACf,4BAAQ,MAAM,8CAA8C,KAAK;AACjE,2BAAO,EAAE,MAAY,MAAY,KAAW;AAAA,kBAChD;AACA,2BAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ,KAAK;AAC/C,wBAAI,UAAU,aAAa,KAAK,CAAC;AACjC,wBAAI,WAAW,MAAM,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AACxM,wBAAI,MAAM,gBAAgB,gBAAgB,aAAaA,SAAQ,SAAS,mBAAmB,MAAM;AACjG,wBAAI,CAAC;AACD;AACJ,wBAAI,YAAY,SAAS;AACrB,2BAAK,KAAK,GAAG;AAAA,oBACjB,WACS,YAAY,SAAS;AAC1B,2BAAK,KAAK,GAAG;AAAA,oBACjB,OACK;AAED,2BAAK,KAAK,GAAG;AAAA,oBACjB;AAAA,kBACJ;AACA,yBAAO,EAAE,MAAY,MAAY,KAAW;AAAA,gBAChD;AACA,gBAAAR,SAAQ,YAAY;AACpB,yBAAS,gBAAgB,gBAAgB,aAAaQ,SAAQ,KAAK,eAAe,QAAQ;AACtF,sBAAI,YAAY,IAAI,SAAS,aAAa,GAAG;AAC7C,2BAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,KAAK;AACvC,wBAAI,OAAO,IAAI,MAAM,CAAC;AACtB,wBAAI,UAAUA,QAAO,iBAAiB,IAAI;AAC1C,wBAAI,iBAAiB,QAAQ,YAAY,QAAQ;AAC7C,0BAAI,aAAa;AACjB,0BAAI,QAAQ;AACR,sCAAc,GAAG,YAAY,UAAU,gBAAgB,MAAM,aAAa,SAASA,OAAM;AAAA,sBAC7F;AACA,gCAAU,KAAK;AAAA,wBACX,SAAS,KAAK;AAAA,wBACd,SAAS,KAAK;AAAA,wBACd,QAAQ;AAAA,wBACR,UAAU;AAAA,wBACV,SAAS,iBAAiB,IAAI;AAAA,sBAClC,CAAC;AAAA,oBACL;AAAA,kBACJ;AACA,sBAAI,QAAQA,QAAO,iBAAiB,GAAG;AACvC,sBAAI,UAAU,SAAS,MAAM,iBAAiB,MAAM,YAAY,SAAS;AACrE,2BAAO;AAAA,kBACX;AAAA,gBACJ;AACA,yBAAS,iBAAiB,SAAS;AAE/B,sBAAI,OAAO,QAAQ,UAAU,IAAI;AAGjC,uBAAK,YAAY,KAAK,UAAU,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,GAAG;AAErE,uBAAK,YAAY,KAAK,UACjB,MAAM,SAAS,EACf,IAAI,SAAU,MAAM;AAAE,2BAAO,KAAK,KAAK;AAAA,kBAAG,CAAC,EAC3C,KAAK,IAAI;AAEd,yBAAO,KAAK,aAAa,KAAK,eAAe;AAAA,gBACjD;AAAA,cAGM;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBR,UAASK,sBAAqB;AAGvE,uBAAO,eAAeL,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,aAAa;AACrB,oBAAI,eAAeK,qBAAoB,CAAC;AACxC,oBAAI,cAAcA,qBAAoB,GAAG;AACzC,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,oBAAI,mBAAmBA,qBAAoB,GAAG;AAC9C,yBAAS,WAAW,GAAG,SAAS;AAC5B,sBAAI,MAAM,IAAI,kBAAkB,WAAW,CAAC;AAC5C,sBAAI,WAAW,IAAI,mBAAmB;AACtC,sBAAIK,UAAS,IAAI,iBAAiB;AAClC,mBAAC,GAAG,iBAAiB,SAAS,KAAKA,SAAQ,UAAU,OAAO;AAC5D,sBAAI,WAAW,GAAG,YAAY,QAAQ,CAAC,GAAGA,SAAQ,UAAU,OAAO;AACnE,sBAAI;AACJ,sBAAI,OAAO,WAAW,aAAa;AAC/B,0BAAM;AAAA,kBACV;AACA,sBAAI,SAAS,YAAYA,SAAQ,UAAU,OAAO;AAClD,sBAAI,QAAQ,WAAWA,SAAQ,UAAU,OAAO;AAChD,sBAAI,WAAW,cAAc,KAAK,OAAO;AACzC,sBAAI,UAAU,aAAa,KAAK,SAAS,GAAG;AAC5C,yBAAO;AAAA,oBACH,IAAI,QAAQ;AAAA,oBACZ;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,kBACJ;AAAA,gBACJ;AACA,gBAAAV,SAAQ,aAAa;AACrB,yBAAS,YAAY,QAAQ,QAAQ,QAAQ;AACzC,sBAAI,eAAe;AAAA,oBACf,QAAQ,CAAC;AAAA,oBACT,YAAY,CAAC;AAAA,oBACb,YAAY,CAAC;AAAA,oBACb,YAAY,CAAC;AAAA,oBACb,oBAAoB,CAAC;AAAA,oBACrB,cAAc,CAAC;AAAA,kBACnB;AACA,sBAAI,UAAU,SAAUW,OAAM;AAC1B,wBAAIA,UAAS,gBAAgB;AACzB,0BAAI,WAAW,OAAOA,KAAI;AAC1B,0BAAI,aAAa,OAAOA,KAAI;AAC5B,0BAAI,UAAU,OAAOA,KAAI;AACzB,mCAAa,gBAAgB,GAAG,YAAY,QAAQ,CAAC,GAAG,UAAU,YAAY,OAAO;AAAA,oBACzF,OACK;AACD,0BAAI,aAAa,CAAC,QAAQ,QAAQ,MAAM;AACxC,0BAAI,SAAS,WAAW,IAAI,SAAU,MAAM;AAAE,+BAAO,KAAKA,KAAI,KAAK,CAAC;AAAA,sBAAG,CAAC;AACxE,mCAAaA,KAAI,KAAK,GAAG,YAAY,QAAQ,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,oBACpF;AAAA,kBACJ;AACA,2BAAS,KAAK,GAAG,KAAK,OAAO,KAAK,YAAY,GAAG,KAAK,GAAG,QAAQ,MAAM;AACnE,wBAAI,OAAO,GAAG,EAAE;AAChB,4BAAQ,IAAI;AAAA,kBAChB;AACA,yBAAO;AAAA,gBACX;AACA,yBAAS,WAAWD,SAAQ,UAAU,SAAS;AAC3C,sBAAI,aAAa,CAACA,SAAQ,UAAU,OAAO;AAC3C,sBAAI,SAAS;AAAA,oBACT,cAAc,CAAC;AAAA,oBACf,cAAc,CAAC;AAAA,oBACf,aAAa,CAAC;AAAA,oBACd,cAAc,CAAC;AAAA,oBACf,aAAa,CAAC;AAAA,kBAClB;AACA,2BAAS,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,QAAQ,MAAM;AACxE,wBAAI,UAAU,aAAa,EAAE;AAC7B,wBAAI,QAAQ;AACR,6BAAO,aAAa,KAAK,QAAQ,YAAY;AACjD,wBAAI,QAAQ;AACR,6BAAO,aAAa,KAAK,QAAQ,YAAY;AACjD,wBAAI,QAAQ;AACR,6BAAO,YAAY,KAAK,QAAQ,WAAW;AAC/C,wBAAI,QAAQ;AACR,6BAAO,aAAa,KAAK,QAAQ,YAAY;AACjD,wBAAI,QAAQ;AACR,6BAAO,YAAY,KAAK,QAAQ,WAAW;AAAA,kBACnD;AACA,yBAAO;AAAA,gBACX;AACA,yBAAS,cAAc,KAAK,SAAS;AACjC,sBAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChD,sBAAI,UAAU,GAAG,SAAS,cAAc,QAAQ,QAAQ,KAAK,IAAI,YAAY,CAAC;AAC9E,sBAAI,UAAU,KAAK,UAAU,KAAK,QAAQ,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,OAAO;AAC3F,sBAAI;AACJ,sBAAI,QAAQ,aAAa,MAAM;AAC3B,+BAAW;AAAA,kBACf,WACS,QAAQ,aAAa,OAAO;AACjC,+BAAW;AAAA,kBACf,OACK;AACD,gCAAY,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAAA,kBACxE;AACA,sBAAI;AACJ,sBAAI,QAAQ,aAAa,MAAM;AAC3B,+BAAW;AAAA,kBACf,WACS,QAAQ,aAAa,OAAO;AACjC,+BAAW;AAAA,kBACf,OACK;AACD,gCAAY,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAAA,kBACxE;AACA,sBAAI,UAAU,KAAK,QAAQ,YAAY,QAAQ,OAAO,SAAS,KAAK;AACpE,sBAAI,QAAQ,QAAQ,UAAU,SAAS,UAAU;AACjD,sBAAI,sBAAsB,CAAC,CAAC,QAAQ;AACpC,sBAAI,6BAA6B,KAAK,QAAQ,+BAA+B,QAAQ,OAAO,SAAS,KAAK;AAC1G,yBAAO;AAAA,oBACH,oBAAoB,KAAK,QAAQ,uBAAuB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBACrF;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA,YAAY,KAAK,QAAQ,eAAe,QAAQ,OAAO,SAAS,KAAK;AAAA,oBACrE,eAAe,KAAK,QAAQ,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBAC3E,aAAa,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBACvE;AAAA,oBACA;AAAA,oBACA,iBAAiB,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBAC/E,iBAAiB,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,SAAS,KAAK;AAAA,oBAC/E;AAAA,oBACA;AAAA,oBACA,+BAA+B,KAAK,QAAQ,kCAAkC,QAAQ,OAAO,SAAS,KAAK;AAAA,kBAC/G;AAAA,gBACJ;AACA,yBAAS,UAAU,KAAK,YAAY;AAChC,sBAAI,WAAW,IAAI,iBAAiB;AACpC,sBAAI,KAAK,IAAI,YAAY;AACzB,sBAAI,cAAc,IAAI,WAAW;AACjC,sBAAI,4BAA4B;AAChC,sBAAI,YAAY,SAAS,iBAAiB;AACtC,wBAAI,aAAa,SAAS,kBAAkB,SAAS,aAAa;AAClE,gDAA4B,eAAe;AAAA,kBAC/C;AACA,sBAAI,OAAO,eAAe,UAAU;AAChC,2BAAO;AAAA,kBACX,WACS,cAAc,QAAQ,eAAe,OAAO;AACjD,wBAAI,8BAA8B,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,WAAW,MAAM;AAG5G,6BAAO,SAAS,SAAS,KAAK;AAAA,oBAClC;AAAA,kBACJ;AACA,yBAAO;AAAA,gBACX;AACA,yBAAS,aAAa,KAAK,SAASF,SAAQ;AACxC,sBAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,sBAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,sBAAI,OAAO,QAAQ,QAAQ,CAAC;AAC5B,sBAAI,QAAQ,MAAM;AACd,wBAAI,SAAS,QAAQ;AACrB,wBAAIA,SAAQ;AACR,0BAAI,eAAe,GAAG,aAAa,WAAW,KAAK,QAAQ,MAAMA,SAAQ,QAAQ,QAAQ,MAAM,KAAK,CAAC;AACrG,6BAAO,YAAY,QAAQ;AAC3B,6BAAO,YAAY,QAAQ;AAC3B,6BAAO,YAAY,QAAQ;AAAA,oBAC/B,OACK;AACD,8BAAQ,MAAM,8CAA8C;AAAA,oBAChE;AAAA,kBACJ;AACA,sBAAI,UAAU,QAAQ,WAAW,aAAa,MAAM,MAAM,IAAI;AAC9D,yBAAO;AAAA,oBACH;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,kBACJ;AAAA,gBACJ;AACA,yBAAS,aAAa,MAAM,MAAM,MAAM;AACpC,sBAAI,WAAW,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC;AACjD,sBAAI,SAAS,CAAC;AACd,yBAAO,KAAK,QAAQ,EACf,OAAO,SAAU,KAAK;AAAE,2BAAO,QAAQ;AAAA,kBAAY,CAAC,EACpD,QAAQ,SAAU,KAAK;AACxB,wBAAI,UAAU;AACd,wBAAI;AACJ,wBAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,8BAAQ,SAAS,SAAS,GAAG,CAAC;AAAA,oBAClC,OACK;AACD,8BAAQ,SAAS,GAAG;AAAA,oBACxB;AACA,wBAAI,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AACpD,iCAAW,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY;AAAA,oBAC/E;AACA,6BAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9B,0BAAI,KAAK;AACT,0BAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,6BAAK,OAAO;AAAA,sBAChB,OACK;AACD,6BAAK,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI;AAAA,sBACxC;AACA,0BAAI,YAAY,EAAE,SAAS,GAAG;AAC9B,6BAAO,KAAK,SAAS;AAAA,oBACzB;AAAA,kBACJ,CAAC;AACD,yBAAO;AAAA,gBACX;AAAA,cAGM;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBR,UAAS;AAGlD,uBAAO,eAAeA,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,yBAAS,UAAU,KAAKU,SAAQ,UAAU,SAAS;AAC/C,sBAAI,UAAU,SAAUE,UAAS;AAC7B,wBAAIA,YAAW,OAAOA,aAAY,UAAU;AACxC,8BAAQ,MAAM,yDAAyD,OAAOA,QAAO;AAAA,oBACzF;AACA,wBAAI,OAAOA,SAAQ,gBAAgB,aAAa;AAC5C,sBAAAA,SAAQ,aAAaA,SAAQ,cAAc,SAAS;AACpD,8BAAQ,MAAM,gEAAgE;AAAA,oBAClF;AACA,wBAAI,OAAOA,SAAQ,YAAY,aAAa;AACxC,0BAAI,OAAOA,SAAQ,WAAW;AAC1B,wBAAAA,SAAQ,SAASA,SAAQ;AAC7B,8BAAQ,MAAM,wDAAwD;AAAA,oBAC1E;AACA,wBAAIA,SAAQ,UAAU,OAAOA,SAAQ,WAAW,UAAU;AACtD,8BAAQ,MAAM,mCAAmCA,SAAQ,MAAM;AAC/D,6BAAOA,SAAQ;AAAA,oBACnB;AACA,wBAAI,CAACA,SAAQ,gBACRA,SAAQ,oBACLA,SAAQ,qBACRA,SAAQ,eAAe;AAC3B,8BAAQ,MAAM,wGAAwG;AACtH,sBAAAA,SAAQ,cAAc,SAAU,MAAM;AAClC,4BAAI,YAAY,IAAI,UAAU;AAC9B,4BAAIA,SAAQ;AACR,0BAAAA,SAAQ,kBAAkB,IAAI;AAClC,4BAAI,YAAY,IAAI,UAAU;AAC9B,4BAAIA,SAAQ;AACR,0BAAAA,SAAQ,iBAAiB,IAAI;AACjC,4BAAI,YAAY,IAAI,UAAU;AAC9B,4BAAIA,SAAQ,gBAAgB,KAAK,aAAa,GAAG;AAC7C;AACA,+BAAK,aAAa,IAAI;AAAA,wBAC1B;AACA,4BAAI,YAAY,IAAI,UAAU;AAAA,sBAClC;AAAA,oBACJ;AACA;AACA;AAAA,sBACI;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,oBACJ,EAAE,QAAQ,SAAU,MAAM;AACtB,0BAAIA,SAAQ,IAAI,GAAG;AACf,gCAAQ,MAAM,QAAS,OAAO,MAAM,4EAA6E,CAAC;AAAA,sBACtH;AAAA,oBACJ,CAAC;AACD;AAAA,sBACI,CAAC,YAAY,YAAY;AAAA,sBACzB,CAAC,YAAY,YAAY;AAAA,sBACzB,CAAC,eAAe,gBAAgB;AAAA,sBAChC,CAAC,gBAAgB,aAAa;AAAA,sBAC9B,CAAC,cAAc,cAAc;AAAA,oBACjC,EAAE,QAAQ,SAAUC,KAAI;AACpB,0BAAIC,WAAUD,IAAG,CAAC,GAAG,aAAaA,IAAG,CAAC;AACtC,0BAAID,SAAQ,UAAU,GAAG;AACrB,gCAAQ,MAAM,4BAA4B,OAAO,YAAY,QAAQ,EAAE,OAAOE,UAAS,UAAU,CAAC;AAClG,wBAAAF,SAAQE,QAAO,IAAIF,SAAQ,UAAU;AAAA,sBACzC;AAAA,oBACJ,CAAC;AACD;AAAA,sBACI,CAAC,WAAW,aAAa;AAAA,sBACzB,CAAC,cAAc,WAAW;AAAA,sBAC1B;AAAA,sBACA;AAAA,oBACJ,EAAE,QAAQ,SAAU,GAAG;AACnB,0BAAI,mBAAmB,OAAO,MAAM,WAAW,IAAI,EAAE,CAAC;AACtD,0BAAI,QAAQ,OAAO,MAAM,WAAW,IAAI,EAAE,CAAC;AAC3C,0BAAI,OAAOA,SAAQ,gBAAgB,MAAM,aAAa;AAClD,4BAAI,OAAOA,SAAQ,OAAO,KAAK,MAAM,aAAa;AAC9C,0BAAAA,SAAQ,OAAO,KAAK,IAAIA,SAAQ,gBAAgB;AAAA,wBACpD;AACA,gCAAQ,MAAM,+BACV,mBACA,qBACA,QACA,WAAW;AAAA,sBACnB;AAAA,oBACJ,CAAC;AACD,6BAAS,KAAK,GAAG,KAAK;AAAA,sBAClB;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA;AAAA,oBACJ,GAAG,KAAK,GAAG,QAAQ,MAAM;AACrB,0BAAI,YAAY,GAAG,EAAE;AACrB,kCAAYA,SAAQ,SAAS,KAAK,CAAC,CAAC;AAAA,oBACxC;AACA,wBAAI,eAAeA,SAAQ,cAAc,KAAK,CAAC;AAC/C,6BAAS,KAAK,GAAG,KAAK,OAAO,KAAK,YAAY,GAAG,KAAK,GAAG,QAAQ,MAAM;AACnE,0BAAI,MAAM,GAAG,EAAE;AACf,kCAAY,aAAa,GAAG,KAAK,CAAC,CAAC;AAAA,oBACvC;AAAA,kBACJ;AACA,2BAAS,KAAK,GAAG,KAAK,CAACF,SAAQ,UAAU,OAAO,GAAG,KAAK,GAAG,QAAQ,MAAM;AACrE,wBAAI,UAAU,GAAG,EAAE;AACnB,4BAAQ,OAAO;AAAA,kBACnB;AAAA,gBACJ;AACA,gBAAAV,SAAQ,SAAS,IAAI;AACrB,yBAAS,YAAY,QAAQ;AACzB,sBAAI,OAAO,WAAW;AAClB,4BAAQ,MAAM,oEAAoE;AAClF,wBAAI,CAAC,OAAO,eAAe;AACvB,6BAAO,gBAAgB,OAAO;AAAA,oBAClC;AAAA,kBACJ,WACS,OAAO,aAAa;AACzB,4BAAQ,MAAM,kEAAkE;AAChF,wBAAI,CAAC,OAAO,WAAW;AACnB,6BAAO,YAAY,OAAO;AAAA,oBAC9B;AAAA,kBACJ;AAAA,gBACJ;AAAA,cAGM;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAASK,sBAAqB;AAGvE,uBAAO,eAAeL,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,SAASA,SAAQ,OAAOA,SAAQ,MAAMA,SAAQ,QAAQ;AAC9D,oBAAI,WAAWK,qBAAoB,GAAG;AACtC,oBAAI,aAAaA,qBAAoB,GAAG;AACxC,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI;AAAA;AAAA,kBAAuB,WAAY;AACnC,6BAASU,OAAM,OAAO,SAAS;AAC3B,2BAAK,aAAa;AAIlB,2BAAK,YAAY;AACjB,2BAAK,KAAK,MAAM;AAChB,2BAAK,WAAW,MAAM;AACtB,2BAAK,SAAS,MAAM;AACpB,2BAAK,QAAQ,MAAM;AACnB,2BAAK,UAAU,QAAQ;AACvB,2BAAK,OAAO,QAAQ;AACpB,2BAAK,OAAO,QAAQ;AACpB,2BAAK,OAAO,QAAQ;AAAA,oBACxB;AACA,oBAAAA,OAAM,UAAU,gBAAgB,SAAU,SAAS;AAC/C,6BAAO,KAAK,KAAK,OAAO,SAAU,KAAK,KAAK;AAAE,+BAAO,MAAM,IAAI,iBAAiB,OAAO;AAAA,sBAAG,GAAG,CAAC;AAAA,oBAClG;AACA,oBAAAA,OAAM,UAAU,gBAAgB,SAAU,SAAS;AAC/C,6BAAO,KAAK,KAAK,OAAO,SAAU,KAAK,KAAK;AAAE,+BAAO,MAAM,IAAI,iBAAiB,OAAO;AAAA,sBAAG,GAAG,CAAC;AAAA,oBAClG;AACA,oBAAAA,OAAM,UAAU,UAAU,WAAY;AAClC,6BAAO,KAAK,KAAK,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK,IAAI;AAAA,oBACvD;AACA,oBAAAA,OAAM,UAAU,gBAAgB,SAAU,KAAK,UAAU,MAAM,KAAK,QAAQ,QAAQ;AAChF,+BAAS,KAAK,GAAG,aAAa,UAAU,KAAK,WAAW,QAAQ,MAAM;AAClE,4BAAI,UAAU,WAAW,EAAE;AAC3B,4BAAI,OAAO,IAAI,WAAW,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ,MAAM;AAC3E,4BAAI,SAAS,QAAQ,IAAI,MAAM;AAE/B,6BAAK,OAAO,MAAM,QAAQ,KAAK,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,IAAI;AAC7D,4BAAI,QAAQ;AACR,iCAAO;AAAA,wBACX;AAAA,sBACJ;AACA,6BAAO;AAAA,oBACX;AACA,oBAAAA,OAAM,UAAU,mBAAmB,SAAU,KAAK,QAAQ;AACtD,0BAAI,YAAY,IAAI,UAAU;AAC9B,+BAAS,KAAK,GAAG,KAAK,KAAK,MAAM,aAAa,KAAK,GAAG,QAAQ,MAAM;AAChE,4BAAI,UAAU,GAAG,EAAE;AACnB,gCAAQ,IAAI,WAAW,SAAS,KAAK,MAAM,MAAM,CAAC;AAAA,sBACtD;AAAA,oBACJ;AACA,oBAAAA,OAAM,UAAU,wBAAwB,SAAU,KAAK,QAAQ;AAC3D,+BAAS,KAAK,GAAG,KAAK,KAAK,MAAM,cAAc,KAAK,GAAG,QAAQ,MAAM;AACjE,4BAAI,UAAU,GAAG,EAAE;AACnB,gCAAQ,IAAI,WAAW,SAAS,KAAK,MAAM,MAAM,CAAC;AAAA,sBACtD;AAAA,oBACJ;AACA,oBAAAA,OAAM,UAAU,WAAW,SAAU,WAAW;AAC5C,0BAAI,OAAO,KAAK,SAAS,eAAe,UAAU;AAC9C,+BAAO,KAAK,SAAS;AAAA,sBACzB,WACS,KAAK,SAAS,eAAe,QAAQ;AAC1C,4BAAI,eAAe,KAAK,QAAQ,OAAO,SAAU,OAAO,KAAK;AAAE,iCAAO,QAAQ,IAAI;AAAA,wBAAc,GAAG,CAAC;AACpG,+BAAO;AAAA,sBACX,OACK;AACD,4BAAI,SAAS,KAAK,SAAS;AAC3B,+BAAO,YAAY,OAAO,OAAO,OAAO;AAAA,sBAC5C;AAAA,oBACJ;AACA,2BAAOA;AAAA,kBACX,EAAE;AAAA;AACF,gBAAAf,SAAQ,QAAQ;AAChB,oBAAI;AAAA;AAAA,kBAAqB,WAAY;AACjC,6BAASgB,KAAI,KAAK,OAAO,SAAS,OAAO,oBAAoB;AACzD,0BAAI,uBAAuB,QAAQ;AAAE,6CAAqB;AAAA,sBAAO;AACjE,2BAAK,SAAS;AACd,2BAAK,MAAM;AACX,0BAAI,eAAe,SAAS,cAAc;AACtC,6BAAK,MAAM,IAAI;AACf,6BAAK,UAAU,IAAI;AAAA,sBACvB;AACA,2BAAK,QAAQ;AACb,2BAAK,UAAU;AACf,2BAAK,QAAQ;AACb,2BAAK,qBAAqB;AAAA,oBAC9B;AACA,oBAAAA,KAAI,UAAU,mBAAmB,SAAU,SAAS;AAChD,0BAAI,QAAQ;AACZ,6BAAO,QAAQ,OAAO,SAAU,KAAK,QAAQ;AAAE,4BAAI;AAAI,+BAAO,KAAK,IAAI,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,CAAC;AAAA,sBAAG,GAAG,CAAC;AAAA,oBAC7K;AACA,oBAAAA,KAAI,UAAU,aAAa,SAAU,SAAS;AAC1C,0BAAI,QAAQ;AACZ,6BAAQ,QAAQ,OAAO,SAAU,QAAQ;AACrC,4BAAI,OAAO,MAAM,MAAM,OAAO,KAAK;AACnC,4BAAI,CAAC;AACD,iCAAO;AACX,+BAAO,KAAK,UAAU;AAAA,sBAC1B,CAAC,EAAE,SAAS;AAAA,oBAChB;AACA,oBAAAA,KAAI,UAAU,kBAAkB,SAAU,QAAQ,SAAS;AACvD,6BAAO,KAAK,iBAAiB,OAAO,KAAK;AAAA,oBAC7C;AACA,oBAAAA,KAAI,UAAU,sBAAsB,SAAU,SAAS,KAAK;AACxD,0BAAI,QAAQ;AACZ,6BAAO,QAAQ,OAAO,SAAU,KAAK,QAAQ;AACzC,4BAAI,OAAO,MAAM,MAAM,OAAO,KAAK;AACnC,4BAAI,CAAC;AACD,iCAAO;AACX,4BAAI,aAAa,IAAI,cAAc,KAAK,OAAO,QAAQ;AACvD,4BAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,4BAAI,eAAe,WAAW;AAC9B,+BAAO,eAAe,MAAM,eAAe;AAAA,sBAC/C,GAAG,CAAC;AAAA,oBACR;AACA,2BAAOA;AAAA,kBACX,EAAE;AAAA;AACF,gBAAAhB,SAAQ,MAAM;AACd,oBAAI;AAAA;AAAA,kBAAsB,WAAY;AAClC,6BAASiB,MAAK,KAAK,QAAQ,SAAS;AAChC,0BAAI,IAAI;AACR,2BAAK,gBAAgB;AACrB,2BAAK,eAAe;AACpB,2BAAK,eAAe;AACpB,2BAAK,mBAAmB;AACxB,2BAAK,WAAW;AAChB,2BAAK,QAAQ;AACb,2BAAK,SAAS;AACd,2BAAK,IAAI;AACT,2BAAK,IAAI;AACT,2BAAK,SAAS;AACd,2BAAK,UAAU;AACf,2BAAK,MAAM;AACX,0BAAI,UAAU;AACd,0BAAI,OAAO,QAAQ,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG,GAAG;AAC/D,6BAAK,UAAU,IAAI,WAAW;AAC9B,6BAAK,UAAU,IAAI,WAAW;AAC9B,mCAAW,MAAM,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,KAAK,IAAI,WAAW,QAAQ,OAAO,SAAS,KAAK;AAChH,4BAAI,IAAI,UAAU;AACd,+BAAK,MAAM,IAAI;AAAA,wBACnB;AAAA,sBACJ,OACK;AACD,6BAAK,UAAU;AACf,6BAAK,UAAU;AAAA,sBACnB;AAEA,0BAAI,OAAO,WAAW,OAAO,KAAK,UAAU;AAC5C,0BAAI,aAAa;AACjB,2BAAK,OAAO,KAAK,MAAM,UAAU;AAAA,oBACrC;AACA,oBAAAA,MAAK,UAAU,aAAa,WAAY;AACpC,0BAAI;AACJ,0BAAI,KAAK,OAAO,WAAW,OAAO;AAC9B,4BAAI,KAAK,IAAI,KAAK,QAAQ,KAAK;AAAA,sBACnC,WACS,KAAK,OAAO,WAAW,UAAU;AACtC,4BAAI,KAAK,IAAI,KAAK,SAAS,KAAK,QAAQ,QAAQ;AAAA,sBACpD,OACK;AACD,4BAAI,YAAY,KAAK,SAAS,KAAK,QAAQ,UAAU;AACrD,4BAAI,KAAK,IAAI,YAAY,IAAI,KAAK,QAAQ,KAAK;AAAA,sBACnD;AACA,0BAAI;AACJ,0BAAI,KAAK,OAAO,WAAW,SAAS;AAChC,4BAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAAA,sBAClD,WACS,KAAK,OAAO,WAAW,UAAU;AACtC,4BAAI,WAAW,KAAK,QAAQ,KAAK,QAAQ,YAAY;AACrD,4BAAI,KAAK,IAAI,WAAW,IAAI,KAAK,QAAQ,MAAM;AAAA,sBACnD,OACK;AACD,4BAAI,KAAK,IAAI,KAAK,QAAQ,MAAM;AAAA,sBACpC;AACA,6BAAO,EAAE,GAAM,EAAK;AAAA,oBACxB;AAEA,oBAAAA,MAAK,UAAU,mBAAmB,SAAU,aAAa,kBAAkB;AACvE,0BAAI,qBAAqB,QAAQ;AAAE,2CAAmB;AAAA,sBAAM;AAC5D,0BAAI,YAAY,MAAM,QAAQ,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;AAC9D,0BAAI,aAAc,KAAK,OAAO,WAAW,cAAe;AACxD,0BAAI,SAAS,YAAY,aAAa,KAAK,QAAQ,UAAU;AAC7D,6BAAO,KAAK,IAAI,QAAQ,KAAK,OAAO,aAAa;AAAA,oBACrD;AACA,oBAAAA,MAAK,UAAU,UAAU,SAAU,MAAM;AACrC,0BAAI,WAAW,GAAG,SAAS,cAAc,KAAK,OAAO,aAAa,CAAC;AACnE,0BAAI,SAAS,YAAY;AACrB,+BAAO,QAAQ,MAAM,QAAQ;AAAA,sBACjC,WACS,SAAS,cAAc;AAC5B,+BAAO,QAAQ,OAAO,QAAQ;AAAA,sBAClC,OACK;AACD,+BAAO,QAAQ,IAAI;AAAA,sBACvB;AAAA,oBACJ;AACA,2BAAOA;AAAA,kBACX,EAAE;AAAA;AACF,gBAAAjB,SAAQ,OAAO;AACf,oBAAI;AAAA;AAAA,kBAAwB,WAAY;AACpC,6BAASkB,QAAO,SAAS,KAAK,OAAO;AACjC,2BAAK,eAAe;AACpB,2BAAK,mBAAmB;AACxB,2BAAK,WAAW;AAChB,2BAAK,QAAQ;AACb,2BAAK,UAAU;AACf,2BAAK,MAAM;AACX,2BAAK,QAAQ;AAAA,oBACjB;AACA,oBAAAA,QAAO,UAAU,wBAAwB,SAAU,OAAO;AACtD,0BAAI,MAAM;AACV,+BAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,GAAG,KAAK,GAAG,QAAQ,MAAM;AACzD,4BAAI,MAAM,GAAG,EAAE;AACf,4BAAI,OAAO,IAAI,MAAM,KAAK,KAAK;AAC/B,4BAAI,QAAQ,OAAO,KAAK,OAAO,cAAc,UAAU;AACnD,gCAAM,KAAK,IAAI,KAAK,KAAK,OAAO,SAAS;AAAA,wBAC7C;AAAA,sBACJ;AACA,6BAAO;AAAA,oBACX;AACA,2BAAOA;AAAA,kBACX,EAAE;AAAA;AACF,gBAAAlB,SAAQ,SAAS;AAAA,cAGX;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAAS;AAIlD,uBAAO,eAAeA,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,SAAS;AAEjB,yBAAS,OAAO,QAAQ,GAAG,IAAI,IAAI,IAAI;AACnC,sBAAI,UAAU,MAAM;AAChB,0BAAM,IAAI,UAAU,4CAA4C;AAAA,kBACpE;AACA,sBAAI,KAAK,OAAO,MAAM;AACtB,2BAAS,QAAQ,GAAG,QAAQ,UAAU,QAAQ,SAAS;AAEnD,wBAAI,aAAa,UAAU,KAAK;AAChC,wBAAI,cAAc,MAAM;AAEpB,+BAAS,WAAW,YAAY;AAE5B,4BAAI,OAAO,UAAU,eAAe,KAAK,YAAY,OAAO,GAAG;AAC3D,6BAAG,OAAO,IAAI,WAAW,OAAO;AAAA,wBACpC;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ;AACA,yBAAO;AAAA,gBACX;AACA,gBAAAA,SAAQ,SAAS;AAAA,cAGX;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAASK,sBAAqB;AAGvE,uBAAO,eAAeL,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,cAAc;AACtB,oBAAI,oBAAoBK,qBAAoB,GAAG;AAC/C,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI,cAAcA,qBAAoB,GAAG;AACzC,yBAAS,YAAY,UAAU,OAAO;AAClC,sBAAI,MAAM,IAAI,kBAAkB,WAAW,QAAQ;AACnD,sBAAI,UAAU,aAAa,OAAO,IAAI,YAAY,CAAC;AACnD,sBAAI,QAAQ,IAAI,SAAS,MAAM,OAAO,OAAO;AAC7C,mBAAC,GAAG,kBAAkB,iBAAiB,KAAK,KAAK;AACjD,sBAAI,YAAY,IAAI,UAAU;AAC9B,yBAAO;AAAA,gBACX;AACA,gBAAAL,SAAQ,cAAc;AACtB,yBAAS,aAAa,OAAO,IAAI;AAC7B,sBAAI,UAAU,MAAM;AACpB,sBAAI,UAAU,cAAc,QAAQ,OAAO;AAE3C,sBAAI,QAAQ,KAAK,WAAW,GAAG;AAC3B,wBAAI,aAAa,mBAAmB,SAAS,MAAM;AACnD,wBAAI;AACA,8BAAQ,KAAK,KAAK,UAAU;AAAA,kBACpC;AACA,sBAAI,QAAQ,KAAK,WAAW,GAAG;AAC3B,wBAAI,aAAa,mBAAmB,SAAS,MAAM;AACnD,wBAAI;AACA,8BAAQ,KAAK,KAAK,UAAU;AAAA,kBACpC;AACA,sBAAI,QAAQ,MAAM,SAAS;AAC3B,sBAAI,SAAS,MAAM;AACnB,yBAAO;AAAA,oBACH;AAAA,oBACA,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,oBACnE,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,oBACnE,MAAM,aAAa,QAAQ,QAAQ,MAAM,SAAS,QAAQ,OAAO,EAAE;AAAA,kBACvE;AAAA,gBACJ;AACA,yBAAS,aAAa,aAAa,aAAa,SAAS,YAAY,OAAO,aAAa;AACrF,sBAAI,wBAAwB,CAAC;AAC7B,sBAAI,SAAS,YAAY,IAAI,SAAU,QAAQ,UAAU;AACrD,wBAAI,wBAAwB;AAC5B,wBAAI,QAAQ,CAAC;AACb,wBAAI,gBAAgB;AACpB,wBAAI,kBAAkB;AACtB,6BAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,0BAAI,SAAS,UAAU,EAAE;AACzB,0BAAI,sBAAsB,OAAO,KAAK,KAAK,QACvC,sBAAsB,OAAO,KAAK,EAAE,SAAS,GAAG;AAChD,4BAAI,oBAAoB,GAAG;AACvB,8BAAI,UAAU;AACd,8BAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,sCACI,OAAO,OAAO,QAAQ,gBAAgB,qBAAqB;AAAA,0BACnE,OACK;AACD,sCAAU,OAAO,OAAO,OAAO;AAAA,0BACnC;AACA,8BAAI,kBAAkB,CAAC;AACvB,8BAAI,OAAO,YAAY,YAAY,CAAC,MAAM,QAAQ,OAAO,GAAG;AACxD,+CAAmB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,CAAC;AAAA,0BAC7F;AACA,8BAAI,SAAS,WAAW,aAAa,QAAQ,UAAU,OAAO,YAAY,aAAa,eAAe;AACtG,8BAAI,OAAO,IAAI,SAAS,KAAK,SAAS,QAAQ,WAAW;AAGzD,gCAAM,OAAO,OAAO,IAAI;AACxB,gCAAM,OAAO,KAAK,IAAI;AACtB,4CAAkB,KAAK,UAAU;AACjC,gDAAsB,OAAO,KAAK,IAAI;AAAA,4BAClC,MAAM,KAAK,UAAU;AAAA,4BACrB,OAAO;AAAA,0BACX;AAAA,wBACJ,OACK;AACD;AACA;AAAA,wBACJ;AAAA,sBACJ,OACK;AACD,8CAAsB,OAAO,KAAK,EAAE;AACpC,0CAAkB,sBAAsB,OAAO,KAAK,EAAE;AACtD;AAAA,sBACJ;AAAA,oBACJ;AACA,2BAAO,IAAI,SAAS,IAAI,QAAQ,UAAU,aAAa,KAAK;AAAA,kBAChE,CAAC;AACD,yBAAO;AAAA,gBACX;AACA,yBAAS,mBAAmB,SAAS,SAAS;AAC1C,sBAAI,aAAa,CAAC;AAClB,0BAAQ,QAAQ,SAAU,KAAK;AAC3B,wBAAI,IAAI,OAAO,MAAM;AACjB,0BAAI,QAAQ,gBAAgB,SAAS,IAAI,GAAG;AAC5C,0BAAI,SAAS;AACT,mCAAW,IAAI,OAAO,IAAI;AAAA,oBAClC;AAAA,kBACJ,CAAC;AACD,yBAAO,OAAO,KAAK,UAAU,EAAE,SAAS,IAAI,aAAa;AAAA,gBAC7D;AACA,yBAAS,gBAAgB,SAAS,QAAQ;AACtC,sBAAI,YAAY,QAAQ;AACpB,wBAAI,OAAO,WAAW,UAAU;AAC5B,6BAAO,OAAO,UAAU,OAAO,SAAS;AAAA,oBAC5C,WACS,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;AAC/D,6BAAO;AAAA,oBACX;AAAA,kBACJ,WACS,YAAY,UAAU,OAAO,WAAW,UAAU;AACvD,2BAAO,OAAO;AAAA,kBAClB;AACA,yBAAO;AAAA,gBACX;AACA,yBAAS,cAAc,SAAS;AAC5B,yBAAO,QAAQ,IAAI,SAAU,OAAO,OAAO;AACvC,wBAAI,IAAI;AACR,wBAAI;AACJ,wBAAI,OAAO,UAAU,UAAU;AAC3B,6BAAO,MAAM,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,KAAK;AAAA,oBAClH,OACK;AACD,4BAAM;AAAA,oBACV;AACA,2BAAO,IAAI,SAAS,OAAO,KAAK,OAAO,KAAK;AAAA,kBAChD,CAAC;AAAA,gBACL;AACA,yBAAS,WAAW,aAAa,QAAQ,UAAU,WAAW,QAAQ,aAAa,iBAAiB;AAChG,sBAAI,SAAS,GAAG,SAAS,UAAU,SAAS;AAC5C,sBAAI;AACJ,sBAAI,gBAAgB,QAAQ;AACxB,oCAAgB,OAAO;AAAA,kBAC3B,WACS,gBAAgB,QAAQ;AAC7B,oCAAgB,OAAO;AAAA,kBAC3B,WACS,gBAAgB,QAAQ;AAC7B,oCAAgB,OAAO;AAAA,kBAC3B;AACA,sBAAI,eAAe,GAAG,YAAY,QAAQ,CAAC,GAAG,MAAM,OAAO,MAAM,WAAW,GAAG,OAAO,QAAQ,aAAa;AAC3G,sBAAI,eAAe,OAAO,aAAa,OAAO,OAAO,KACjD,OAAO,aAAa,OAAO,KAAK,KAChC,CAAC;AACL,sBAAI,YAAY,gBAAgB,SAAS,eAAe,CAAC;AACzD,sBAAI,YAAY,gBAAgB,UAAU,WAAW,MAAM,KACpD,GAAG,YAAY,QAAQ,CAAC,GAAG,MAAM,cAAc,OAAO,kBAAkB,IACzE,CAAC;AACP,sBAAI,gBAAgB,GAAG,SAAS,eAAe,WAAW;AAC1D,sBAAI,eAAe,GAAG,YAAY,QAAQ,CAAC,GAAG,cAAc,aAAa,WAAW,SAAS;AAC7F,0BAAQ,GAAG,YAAY,QAAQ,aAAa,eAAe;AAAA,gBAC/D;AAAA,cAGM;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAASK,sBAAqB;AAGvE,uBAAO,eAAeL,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,UAAUA,SAAQ,YAAY;AACtC,oBAAI,WAAWK,qBAAoB,GAAG;AACtC,oBAAI,WAAWA,qBAAoB,GAAG;AACtC,oBAAI,oBAAoBA,qBAAoB,GAAG;AAC/C,oBAAI,cAAcA,qBAAoB,GAAG;AACzC,oBAAI,kBAAkBA,qBAAoB,GAAG;AAC7C,oBAAI,iBAAiBA,qBAAoB,GAAG;AAC5C,yBAAS,UAAU,UAAU,OAAO;AAChC,sBAAI,WAAW,MAAM;AACrB,sBAAI,SAAS,SAAS;AACtB,sBAAI,SAAS,SAAS;AACtB,sBAAI,SAAS;AAAA,oBACT,GAAG,OAAO;AAAA,oBACV,GAAG;AAAA,kBACP;AACA,sBAAI,iBAAiB,MAAM,cAAc,MAAM,OAAO,IAAI,MAAM,cAAc,MAAM,OAAO;AAC3F,sBAAI,oBAAoB,SAAS,OAAO,SAAS;AACjD,sBAAI,SAAS,cAAc,SAAS;AAChC,wBAAI,OAAO,MAAM;AACjB,wBAAI,cAAc,KAAK,OAAO,SAAU,KAAK,KAAK;AAAE,6BAAO,MAAM,IAAI;AAAA,oBAAQ,GAAG,CAAC;AACjF,yCAAqB;AAAA,kBACzB;AACA,sBAAI,MAAM,IAAI,kBAAkB,WAAW,QAAQ;AACnD,sBAAI,SAAS,cAAc,YACtB,SAAS,UAAU,QAAQ,oBAAoB,IAAI,SAAS,EAAE,QAAS;AACxE,6BAAS,GAAG;AACZ,2BAAO,IAAI,OAAO;AAAA,kBACtB;AACA,wBAAM,sBAAsB,KAAK,MAAM;AACvC,sBAAI,YAAY,GAAG,YAAY,QAAQ,CAAC,GAAG,MAAM;AACjD,wBAAM,kBAAkB,IAAI,WAAW;AACvC,sBAAI,SAAS,qBAAqB;AAE9B,sDAAkC,KAAK,OAAO,UAAU,MAAM;AAAA,kBAClE,OACK;AAED,wBAAI,YAAY,IAAI,UAAU;AAC9B,wBAAI,SAAS,aAAa,eACtB,SAAS,aAAa,aAAa;AACnC,4BAAM,KAAK,QAAQ,SAAU,KAAK;AAC9B,+BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,sBAC1D,CAAC;AAAA,oBACL;AACA,wBAAI,YAAY,IAAI,UAAU;AAC9B,0BAAM,KAAK,QAAQ,SAAU,KAAK,OAAO;AACrC,0BAAI,YAAY,UAAU,MAAM,KAAK,SAAS;AAC9C,mCAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,MAAM,OAAO;AAAA,oBAC5E,CAAC;AACD,wBAAI,YAAY,IAAI,UAAU;AAC9B,wBAAI,SAAS,aAAa,cAAc,SAAS,aAAa,aAAa;AACvE,4BAAM,KAAK,QAAQ,SAAU,KAAK;AAC9B,+BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;AAAA,sBAC1D,CAAC;AAAA,oBACL;AAAA,kBACJ;AACA,mBAAC,GAAG,SAAS,gBAAgB,KAAK,OAAO,UAAU,MAAM;AACzD,wBAAM,iBAAiB,KAAK,MAAM;AAClC,wBAAM,SAAS,OAAO;AACtB,2BAAS,gBAAgB;AACzB,2BAAS,oBAAoB;AAC7B,sBAAI,SAAS;AACT,6BAAS,UAAU,WAAW;AAClC,sBAAI,YAAY,IAAI,UAAU;AAAA,gBAClC;AACA,gBAAAL,SAAQ,YAAY;AACpB,yBAAS,kCAAkC,KAAK,OAAO,UAAU,QAAQ;AAErE,sBAAI,0BAA0B,GAAG,eAAe,iCAAiC,KAAK,KAAK;AAC3F,sBAAI,WAAW,MAAM;AACrB,sBAAI,SAAS,iCAAiC,gBAAgB;AAC1D,2CAAuB,QAAQ,SAAU,gBAAgB,OAAO;AAC5D,0BAAI,YAAY,IAAI,UAAU;AAE9B,0BAAI,QAAQ,GAAG;AAGX,gCAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI;AAAA,sBACtE,OACK;AAED,kCAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,sBACxD;AAEA,gCAAU,KAAK,OAAO,UAAU,QAAQ,eAAe,OAAO;AAC9D,gCAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,oBACxD,CAAC;AAAA,kBACL,OACK;AACD,wBAAI,2BAA2B;AAC/B,wBAAI,0BAA0B,uBAAuB,CAAC;AACtD,wBAAI,UAAU,WAAY;AAEtB,0BAAI,sBAAsB;AAC1B,0BAAI,yBAAyB;AACzB,4BAAI,YAAY,IAAI,UAAU;AAC9B,4BAAI,oBAAoB,wBAAwB;AAChD,4BAAI,4BAA4B,GAAG;AAG/B,kCAAQ,KAAK,OAAO,UAAU,QAAQ,mBAAmB,IAAI;AAAA,wBACjE,OACK;AACD,oCAAU,KAAK,OAAO,QAAQ,iBAAiB;AAAA,wBACnD;AACA,8CAAsB,2BAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ,iBAAiB;AACpH,kCAAU,KAAK,OAAO,QAAQ,iBAAiB;AAAA,sBACnD;AAEA,0BAAI,kBAAkB,sBAAsB;AAE5C,6CAAuB,MAAM,CAAC,EAAE,QAAQ,SAAU,gBAAgB;AAC9D,4BAAI,YAAY,IAAI,UAAU;AAG9B,gCAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,SAAS,IAAI;AAClE,mDAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ,eAAe,SAAS,eAAe;AACpH,kCAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;AAAA,sBACxD,CAAC;AACD,iDAA2B;AAAA,oBAC/B;AACA,2BAAO,2BAA2B,MAAM,KAAK,SAAS,GAAG;AACrD,8BAAQ;AAAA,oBACZ;AAAA,kBACJ;AAAA,gBACJ;AACA,yBAAS,UAAU,KAAK,OAAO,QAAQ,SAAS;AAC5C,sBAAI,WAAW,MAAM;AACrB,sBAAI,YAAY,IAAI,UAAU;AAC9B,sBAAI,SAAS,aAAa,eAAe,SAAS,aAAa,aAAa;AACxE,0BAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,6BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,oBAAG,CAAC;AAAA,kBAC5F;AAAA,gBACJ;AACA,yBAAS,UAAU,KAAK,OAAO,UAAU,QAAQ,SAAS;AACtD,sBAAI,YAAY,IAAI,UAAU;AAC9B,wBAAM,KAAK,QAAQ,SAAU,KAAK,OAAO;AACrC,wBAAI,YAAY,UAAU,MAAM,KAAK,SAAS;AAC9C,iCAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,OAAO;AAAA,kBACtE,CAAC;AAAA,gBACL;AACA,yBAAS,2BAA2B,KAAK,OAAO,eAAe,QAAQ,SAAS,iBAAiB;AAC7F,sBAAI,YAAY,IAAI,UAAU;AAC9B,oCAAkB,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,MAAM,KAAK;AACxG,sBAAI,cAAc,KAAK,IAAI,gBAAgB,iBAAiB,MAAM,KAAK,MAAM;AAC7E,sBAAI,sBAAsB;AAC1B,wBAAM,KAAK,MAAM,eAAe,WAAW,EAAE,QAAQ,SAAU,KAAK,OAAO;AACvE,wBAAI,YAAY,gBAAgB,UAAU,MAAM,KAAK,SAAS;AAC9D,wBAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW,MAAM;AACxE,wBAAI,IAAI,gBAAgB,gBAAgB,OAAO,GAAG;AAC9C,+BAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AACzC,4CAAsB,gBAAgB;AAAA,oBAC1C;AAAA,kBACJ,CAAC;AACD,yBAAO;AAAA,gBACX;AACA,yBAAS,UAAU,KAAK,OAAO,QAAQ,SAAS;AAC5C,sBAAI,WAAW,MAAM;AACrB,sBAAI,YAAY,IAAI,UAAU;AAC9B,sBAAI,SAAS,aAAa,cAAc,SAAS,aAAa,aAAa;AACvE,0BAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,6BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,oBAAG,CAAC;AAAA,kBAC5F;AAAA,gBACJ;AACA,yBAAS,sBAAsB,MAAM,oBAAoB,KAAK;AAC1D,sBAAI,aAAa,IAAI,cAAc,KAAK,OAAO,QAAQ;AACvD,sBAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,sBAAI,iBAAiB,KAAK,OAAO,qBAAqB,YAAY,UAAU;AAC5E,yBAAO,KAAK,IAAI,GAAG,cAAc;AAAA,gBACrC;AACA,yBAAS,eAAe,KAAK,oBAAoB,OAAO,KAAK;AACzD,sBAAI,QAAQ,CAAC;AACb,sBAAI,qBAAqB;AACzB,sBAAI,SAAS;AACb,sBAAI,YAAY;AAChB,2BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,wBAAI,SAAS,GAAG,EAAE;AAClB,wBAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,wBAAI,CAAC;AACD;AACJ,wBAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC3B,2BAAK,OAAO,CAAC,KAAK,IAAI;AAAA,oBAC1B;AACA,wBAAI,gBAAgB,IAAI,SAAS,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,OAAO;AACzE,qCAAiB,GAAG,YAAY,QAAQ,eAAe,IAAI;AAC3D,kCAAc,OAAO,CAAC;AACtB,wBAAI,qBAAqB,sBAAsB,MAAM,oBAAoB,GAAG;AAC5E,wBAAI,KAAK,KAAK,SAAS,oBAAoB;AACvC,oCAAc,OAAO,KAAK,KAAK,OAAO,oBAAoB,KAAK,KAAK,MAAM;AAAA,oBAC9E;AACA,wBAAI,cAAc,IAAI,YAAY;AAClC,wBAAI,mBAAmB,IAAI,oBAAoB;AAC/C,yBAAK,gBAAgB,KAAK,iBAAiB,aAAa,gBAAgB;AACxE,wBAAI,KAAK,iBAAiB,oBAAoB;AAC1C,2BAAK,gBAAgB;AACrB,oCAAc,OAAO,iBAAiB;AAAA,oBAC1C;AACA,wBAAI,KAAK,gBAAgB,IAAI,QAAQ;AACjC,0BAAI,SAAS,KAAK;AAAA,oBACtB;AACA,kCAAc,gBAAgB,cAAc,iBAAiB,aAAa,gBAAgB;AAC1F,wBAAI,cAAc,gBAAgB,WAAW;AACzC,kCAAY,cAAc;AAAA,oBAC9B;AACA,0BAAM,OAAO,KAAK,IAAI;AAAA,kBAC1B;AACA,sBAAI,eAAe,IAAI,SAAS,IAAI,IAAI,KAAK,IAAI,IAAI,SAAS,OAAO,IAAI;AACzE,+BAAa,SAAS;AACtB,2BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,wBAAI,SAAS,GAAG,EAAE;AAClB,wBAAI,gBAAgB,aAAa,MAAM,OAAO,KAAK;AACnD,wBAAI,eAAe;AACf,oCAAc,SAAS,aAAa;AAAA,oBACxC;AACA,wBAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,wBAAI,MAAM;AACN,2BAAK,SAAS,IAAI;AAAA,oBACtB;AAAA,kBACJ;AACA,yBAAO;AAAA,gBACX;AACA,yBAAS,yBAAyB,KAAK,KAAK,oBAAoB,OAAO;AACnE,sBAAI,aAAa,IAAI,SAAS,EAAE;AAChC,sBAAI,SAAS,MAAM,SAAS;AAC5B,sBAAI,eAAe,OAAO,MAAM,OAAO;AACvC,sBAAI,eAAe,aAAa;AAChC,sBAAI,IAAI,YAAY,QAAQ;AAGxB,oCACI,MAAM,cAAc,MAAM,OAAO,IAAI,MAAM,cAAc,MAAM,OAAO;AAAA,kBAC9E;AACA,sBAAI,eAAe,IAAI,oBAAoB,MAAM,SAAS,GAAG;AAC7D,sBAAI,aAAa,eAAe;AAChC,sBAAI,eAAe,cAAc;AAC7B,4BAAQ,MAAM,iCAAiC,OAAO,IAAI,OAAO,iEAAiE,CAAC;AACnI,2BAAO;AAAA,kBACX;AACA,sBAAI,CAAC,YAAY;AACb,2BAAO;AAAA,kBACX;AACA,sBAAI,oBAAoB,IAAI,WAAW,MAAM,OAAO;AACpD,sBAAI,oBAAoB,IAAI,iBAAiB,MAAM,OAAO,IAAI;AAC9D,sBAAI,mBAAmB;AACnB,wBAAI,mBAAmB;AACnB,8BAAQ,MAAM,sBAAsB,OAAO,IAAI,OAAO,yIAAyI,CAAC;AAAA,oBACpM;AACA,2BAAO;AAAA,kBACX;AACA,sBAAI,mBAAmB;AAEnB,2BAAO;AAAA,kBACX;AACA,sBAAI,MAAM,SAAS,iBAAiB,SAAS;AACzC,2BAAO;AAAA,kBACX;AAEA,yBAAO;AAAA,gBACX;AACA,yBAAS,aAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,SAAS;AACzE,sBAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW,MAAM;AACxE,sBAAI,IAAI,gBAAgB,gBAAgB,OAAO,GAAG;AAE9C,6BAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,kBAC7C,WACS,yBAAyB,KAAK,KAAK,gBAAgB,KAAK,GAAG;AAEhE,wBAAI,eAAe,eAAe,KAAK,gBAAgB,OAAO,GAAG;AACjE,6BAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AACzC,4BAAQ,KAAK,OAAO,UAAU,QAAQ,OAAO;AAC7C,iCAAa,KAAK,OAAO,cAAc,WAAW,UAAU,QAAQ,OAAO;AAAA,kBAC/E,OACK;AAED,4BAAQ,KAAK,OAAO,UAAU,QAAQ,OAAO;AAC7C,iCAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,OAAO;AAAA,kBACtE;AAAA,gBACJ;AACA,yBAAS,SAAS,KAAK,OAAO,KAAK,QAAQ,SAAS;AAChD,yBAAO,IAAI,MAAM,SAAS,OAAO;AACjC,2BAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,wBAAI,SAAS,UAAU,EAAE;AACzB,wBAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,wBAAI,CAAC,MAAM;AACP,6BAAO,KAAK,OAAO;AACnB;AAAA,oBACJ;AACA,wBAAI,YAAY,KAAK,MAAM;AAC3B,yBAAK,IAAI,OAAO;AAChB,yBAAK,IAAI,OAAO;AAChB,wBAAI,SAAS,MAAM,cAAc,KAAK,MAAM,MAAM,cAAc,MAAM,KAAK,QAAQ,MAAM;AACzF,wBAAI,WAAW,OAAO;AAClB,6BAAO,KAAK,OAAO;AACnB;AAAA,oBACJ;AACA,iCAAa,KAAK,MAAM,MAAM;AAC9B,wBAAI,UAAU,KAAK,WAAW;AAC9B,qBAAC,GAAG,gBAAgB,SAAS,KAAK,MAAM,QAAQ,GAAG,QAAQ,GAAG;AAAA,sBAC1D,QAAQ,KAAK,OAAO;AAAA,sBACpB,QAAQ,KAAK,OAAO;AAAA,sBACpB,UAAU,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,OAAO,CAAC;AAAA,oBACjF,GAAG,IAAI,YAAY,CAAC;AACpB,0BAAM,cAAc,KAAK,MAAM,MAAM,aAAa,MAAM,KAAK,QAAQ,MAAM;AAC3E,2BAAO,KAAK,OAAO;AAAA,kBACvB;AACA,yBAAO,KAAK,IAAI;AAAA,gBACpB;AACA,yBAAS,aAAa,KAAK,MAAM,QAAQ;AACrC,sBAAI,aAAa,KAAK;AAGtB,sBAAI,YAAY,EAAE,aAAa,IAAI,YAAY,EAAE,aAAa,CAAC;AAC/D,sBAAI,OAAO,WAAW,cAAc,UAAU;AAE1C,wBAAI,aAAa,GAAG,SAAS,cAAc,WAAW,WAAW,WAAW,SAAS;AACrF,wBAAI,WAAW;AACX,0BAAI,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,SAAS;AAAA,oBACjE;AAAA,kBACJ,WACS,OAAO,WAAW,cAAc,UAAU;AAE/C,wBAAI,WAAW,WAAW;AACtB,0BAAI,KAAK,KAAK,GAAG,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,GAAG;AAAA,oBAC3D;AAEA,oCAAgB,KAAK,MAAM,QAAQ,WAAW,SAAS;AAAA,kBAC3D;AAAA,gBACJ;AAUA,yBAAS,gBAAgB,KAAK,MAAM,QAAQ,WAAW;AACnD,sBAAI,IAAI,IAAI,IAAI;AAChB,sBAAI,UAAU,KAAK;AACf,yBAAK,OAAO;AACZ,yBAAK,OAAO;AACZ,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO;AACZ,wBAAI,UAAU,OAAO;AACjB,4BAAM,MAAM,UAAU;AAAA,oBAC1B;AACA,wBAAI,UAAU,MAAM;AAChB,4BAAM,MAAM,UAAU;AAAA,oBAC1B;AACA,6BAAS,UAAU,KAAK,IAAI,IAAI,IAAI,EAAE;AAAA,kBAC1C;AACA,sBAAI,UAAU,QAAQ;AAClB,yBAAK,OAAO;AACZ,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO,IAAI,KAAK;AACrB,wBAAI,UAAU,OAAO;AACjB,4BAAM,MAAM,UAAU;AAAA,oBAC1B;AACA,wBAAI,UAAU,MAAM;AAChB,4BAAM,MAAM,UAAU;AAAA,oBAC1B;AACA,6BAAS,UAAU,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,kBAC7C;AACA,sBAAI,UAAU,MAAM;AAChB,yBAAK,OAAO;AACZ,yBAAK,OAAO;AACZ,yBAAK,OAAO;AACZ,yBAAK,OAAO,IAAI,KAAK;AACrB,wBAAI,UAAU,KAAK;AACf,4BAAM,MAAM,UAAU;AAAA,oBAC1B;AACA,wBAAI,UAAU,QAAQ;AAClB,4BAAM,MAAM,UAAU;AAAA,oBAC1B;AACA,6BAAS,UAAU,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,kBAC3C;AACA,sBAAI,UAAU,OAAO;AACjB,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO;AACZ,yBAAK,OAAO,IAAI,KAAK;AACrB,yBAAK,OAAO,IAAI,KAAK;AACrB,wBAAI,UAAU,KAAK;AACf,4BAAM,MAAM,UAAU;AAAA,oBAC1B;AACA,wBAAI,UAAU,QAAQ;AAClB,4BAAM,MAAM,UAAU;AAAA,oBAC1B;AACA,6BAAS,UAAU,OAAO,IAAI,IAAI,IAAI,EAAE;AAAA,kBAC5C;AACA,2BAAS,SAAS,OAAOmB,KAAIC,KAAIC,KAAIC,KAAI;AACrC,wBAAI,YAAY,EAAE,aAAa,KAAK;AACpC,wBAAI,YAAY,EAAE,KAAKH,KAAIC,KAAIC,KAAIC,KAAI,GAAG;AAAA,kBAC9C;AAAA,gBACJ;AACA,yBAAS,sBAAsB,KAAK,OAAO,WAAW,QAAQ;AAC1D,sBAAI,sBAAsB,MAAM,SAAS,OAAO;AAChD,sBAAI,WAAW,MAAM,SAAS;AAC9B,sBAAI,aAAa,eAAgB,aAAa,cAAc,WAAY;AACpE,2CAAuB,MAAM,cAAc,MAAM,OAAO;AAAA,kBAC5D;AACA,yBAAO,IAAI,SAAS,EAAE,SAAS,OAAO,IAAI;AAAA,gBAC9C;AACA,yBAAS,QAAQ,KAAK,OAAO,UAAU,QAAQ,SAAS,gBAAgB;AACpE,sBAAI,YAAY,QAAQ;AAAE,8BAAU,CAAC;AAAA,kBAAG;AACxC,sBAAI,mBAAmB,QAAQ;AAAE,qCAAiB;AAAA,kBAAO;AACzD,sBAAI,YAAY,IAAI,UAAU;AAC9B,sBAAI,MAAM,SAAS,aAAa,eAAe,CAAC,gBAAgB;AAC5D,0BAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,6BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,oBAAG,CAAC;AAAA,kBAC5F;AAGA,wBAAM,iBAAiB,KAAK,MAAM;AAClC,sBAAI,SAAS,MAAM,SAAS;AAC5B,mBAAC,GAAG,SAAS,gBAAgB,KAAK,OAAO,UAAU,MAAM;AACzD,2BAAS,GAAG;AACZ,wBAAM;AACN,wBAAM;AACN,yBAAO,IAAI,OAAO;AAClB,yBAAO,IAAI,OAAO;AAClB,2BAAS,IAAI,OAAO;AAEpB,wBAAM,sBAAsB,KAAK,MAAM;AACvC,sBAAI,MAAM,SAAS,aAAa,aAAa;AACzC,0BAAM,KAAK,QAAQ,SAAU,KAAK;AAAE,6BAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,OAAO;AAAA,oBAAG,CAAC;AACxF,wBAAI,YAAY,IAAI,UAAU;AAAA,kBAClC;AAAA,gBACJ;AACA,gBAAAtB,SAAQ,UAAU;AAClB,yBAAS,SAAS,KAAK;AACnB,sBAAI,UAAU,IAAI,WAAW;AAC7B,sBAAI,QAAQ,UAAU,CAAC;AACvB,sBAAI,aAAa,IAAI,WAAW;AAChC,sBAAI,eAAe,SAAS;AACxB,wBAAI,QAAQ;AACZ,2BAAO;AAAA,kBACX;AACA,yBAAO;AAAA,gBACX;AAAA,cAGM;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAASK,sBAAqB;AAGvE,uBAAO,eAAeL,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,kCAAkC;AAC1C,oBAAI,WAAWK,qBAAoB,GAAG;AAEtC,yBAAS,uBAAuB,KAAK,OAAO,QAAQ;AAChD,sBAAI;AACJ,sBAAI,WAAW,QAAQ;AAAE,6BAAS,CAAC;AAAA,kBAAG;AAEtC,sBAAI,kBAAkB,GAAG,SAAS,uBAAuB,KAAK,KAAK;AAEnE,sBAAI,mBAAmB,oBAAI,IAAI;AAC/B,sBAAI,aAAa,CAAC;AAClB,sBAAI,UAAU,CAAC;AACf,sBAAI,4BAA4B,CAAC;AACjC,wBAAM,SAAS;AACf,sBAAI,MAAM,QAAQ,MAAM,SAAS,yBAAyB,GAAG;AACzD,gDAA4B,MAAM,SAAS;AAAA,kBAE/C,WACS,OAAO,MAAM,SAAS,8BAA8B,YACzD,OAAO,MAAM,SAAS,8BAA8B,UAAU;AAC9D,gDAA4B,CAAC,MAAM,SAAS,yBAAyB;AAAA,kBACzE;AAEA,4CAA0B,QAAQ,SAAU,OAAO;AAC/C,wBAAI,MAAM,MAAM,QAAQ,KAAK,SAAU,MAAM;AAAE,6BAAO,KAAK,YAAY,SAAS,KAAK,UAAU;AAAA,oBAAO,CAAC;AACvG,wBAAI,OAAO,CAAC,iBAAiB,IAAI,IAAI,KAAK,GAAG;AACzC,uCAAiB,IAAI,IAAI,OAAO,IAAI;AACpC,iCAAW,KAAK,IAAI,KAAK;AACzB,8BAAQ,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC;AACrC,wCAAkB,IAAI;AAAA,oBAC1B;AAAA,kBACJ,CAAC;AACD,sBAAI,QAAQ;AACZ,sBAAI,KAAK,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,WAAW,QAAQ,OAAO,SAAS,KAAK;AAC7G,yBAAO,IAAI,MAAM,QAAQ,QAAQ;AAE7B,wBAAI,iBAAiB,IAAI,CAAC,GAAG;AACzB;AACA;AAAA,oBACJ;AACA,wBAAI,WAAW,MAAM,QAAQ,CAAC,EAAE;AAEhC,wBAAI,SAAS,kBAAkB,UAAU;AACrC,8BAAQ;AACR,iCAAW,KAAK,CAAC;AACjB,8BAAQ,KAAK,MAAM,QAAQ,CAAC,CAAC;AAC7B,wCAAkB;AAAA,oBACtB,OACK;AACD;AAAA,oBACJ;AACA;AAAA,kBACJ;AACA,yBAAO,EAAE,YAAwB,SAAkB,WAAW,IAAI,EAAE;AAAA,gBACxE;AACA,yBAAS,gCAAgC,KAAK,OAAO;AACjD,sBAAI,aAAa,CAAC;AAClB,2BAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,QAAQ,KAAK;AAC3C,wBAAI,SAAS,uBAAuB,KAAK,OAAO,EAAE,OAAO,EAAE,CAAC;AAC5D,wBAAI,OAAO,QAAQ,QAAQ;AACvB,iCAAW,KAAK,MAAM;AACtB,0BAAI,OAAO;AAAA,oBACf;AAAA,kBACJ;AACA,yBAAO;AAAA,gBACX;AACA,gBAAAL,SAAQ,kCAAkC;AAAA,cAGpC;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAAS,yBAAyBA,UAASK,sBAAqB;AAGvE,uBAAO,eAAeL,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,gBAAAA,SAAQ,YAAYA,SAAQ,gBAAgBA,SAAQ,kBAAkB;AACtE,oBAAI,WAAWK,qBAAoB,GAAG;AAItC,yBAAS,gBAAgB,KAAK,OAAO;AACjC,4BAAU,KAAK,KAAK;AACpB,sBAAI,mBAAmB,CAAC;AACxB,sBAAI,oBAAoB;AACxB,wBAAM,QAAQ,QAAQ,SAAU,QAAQ;AACpC,wBAAI,cAAc,OAAO,sBAAsB,KAAK;AACpD,wBAAI,aAAa;AAEb,6BAAO,QAAQ;AAAA,oBACnB,OACK;AAED,6BAAO,QAAQ,OAAO;AACtB,uCAAiB,KAAK,MAAM;AAAA,oBAChC;AACA,yCAAqB,OAAO;AAAA,kBAChC,CAAC;AAED,sBAAI,cAAc,MAAM,SAAS,IAAI,SAAS,EAAE,KAAK,IAAI;AAEzD,sBAAI,aAAa;AACb,kCAAc,cAAc,kBAAkB,aAAa,SAAU,QAAQ;AACzE,6BAAO,KAAK,IAAI,OAAO,kBAAkB,OAAO,QAAQ;AAAA,oBAC5D,CAAC;AAAA,kBACL;AAEA,sBAAI,aAAa;AACb,kCAAc,cAAc,kBAAkB,aAAa,SAAU,QAAQ;AAAE,6BAAO,OAAO;AAAA,oBAAU,CAAC;AAAA,kBAC5G;AACA,gCAAc,KAAK,IAAI,WAAW;AAClC,sBAAI,CAAC,MAAM,SAAS,uBAChB,cAAc,MAAM,IAAI,YAAY,GAAG;AAKvC,kCAAc,cAAc,IAAI,cAAc,KAAK,MAAM,WAAW;AACpE,4BAAQ,KAAK,yBAAyB,OAAO,aAAa,iCAAiC,CAAC;AAAA,kBAChG;AACA,gCAAc,KAAK;AACnB,6BAAW,OAAO,GAAG;AACrB,gCAAc,KAAK;AAAA,gBACvB;AACA,gBAAAL,SAAQ,kBAAkB;AAC1B,yBAAS,UAAU,KAAK,OAAO;AAC3B,sBAAI,KAAK,IAAI,YAAY;AACzB,sBAAI,sBAAsB,MAAM,SAAS;AACzC,sBAAI,sBAAsB,GAAG,SAAS,uBAAuB,KAAK,KAAK;AACvE,wBAAM,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACnC,6BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,0BAAI,SAAS,GAAG,EAAE;AAClB,0BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,0BAAI,CAAC;AACD;AACJ,0BAAI,QAAQ,MAAM,MAAM;AACxB,4BAAM,cAAc,KAAK,OAAO,MAAM,KAAK,QAAQ,IAAI;AACvD,0BAAI,UAAU,KAAK,QAAQ,YAAY;AACvC,2BAAK,gBAAgB,GAAG,SAAS,gBAAgB,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI;AAKhF,0BAAI,oBAAoB,GAAG,SAAS,gBAAgB,KAAK,KAAK,KAAK,GAAG,EAAE,MAAM,cAAc,GAAG,KAAK,QAAQ,GAAG;AAC/G,2BAAK,mBAAmB,mBAAmB,KAAK,QAAQ,YAAY;AACpE,0BAAI,OAAO,KAAK,OAAO,cAAc,UAAU;AAC3C,6BAAK,WAAW,KAAK,OAAO;AAC5B,6BAAK,eAAe,KAAK,OAAO;AAAA,sBACpC,WACS,KAAK,OAAO,cAAc,UAC/B,wBAAwB,MAAM;AAE9B,4BAAI,KAAK,eAAe,oBAAoB;AACxC,+BAAK,WAAW;AAChB,+BAAK,eAAe;AAAA,wBACxB,OACK;AACD,+BAAK,WAAW,KAAK;AACrB,+BAAK,eAAe,KAAK;AAAA,wBAC7B;AAAA,sBACJ,OACK;AAED,4BAAI,kBAAkB,KAAK;AAC3B,6BAAK,WAAW,KAAK,OAAO,gBAAgB;AAC5C,6BAAK,eAAe,KAAK;AACzB,4BAAI,KAAK,WAAW,KAAK,cAAc;AACnC,+BAAK,eAAe,KAAK;AAAA,wBAC7B;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ,CAAC;AACD,wBAAM,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACnC,6BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,0BAAI,SAAS,GAAG,EAAE;AAClB,0BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AAGjC,0BAAI,QAAQ,KAAK,YAAY,GAAG;AAC5B,+BAAO,eAAe,KAAK,IAAI,OAAO,cAAc,KAAK,YAAY;AACrE,+BAAO,WAAW,KAAK,IAAI,OAAO,UAAU,KAAK,QAAQ;AACzD,+BAAO,mBAAmB,KAAK,IAAI,OAAO,kBAAkB,KAAK,gBAAgB;AAAA,sBACrF,OACK;AAOD,4BAAI,eAAe,MAAM,OAAO,aAAa,OAAO,OAAO,KACvD,MAAM,OAAO,aAAa,OAAO,KAAK,KACtC,CAAC;AACL,4BAAI,YAAY,aAAa,aAAa,aAAa;AACvD,4BAAI,aAAa,OAAO,cAAc,UAAU;AAC5C,iCAAO,WAAW;AAClB,iCAAO,eAAe;AAAA,wBAC1B;AAAA,sBACJ;AACA,0BAAI,MAAM;AAEN,4BAAI,KAAK,UAAU,KAAK,CAAC,OAAO,UAAU;AACtC,iCAAO,WAAW,KAAK;AAAA,wBAC3B;AACA,4BAAI,KAAK,UAAU,KAAK,CAAC,OAAO,cAAc;AAC1C,iCAAO,eAAe,KAAK;AAAA,wBAC/B;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ,CAAC;AAAA,gBACL;AAIA,yBAAS,cAAc,SAAS,aAAa,aAAa;AACtD,sBAAI,qBAAqB;AACzB,sBAAI,kBAAkB,QAAQ,OAAO,SAAU,KAAKuB,SAAQ;AAAE,2BAAO,MAAMA,QAAO;AAAA,kBAAc,GAAG,CAAC;AACpG,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,SAAS,QAAQ,CAAC;AACtB,wBAAI,QAAQ,OAAO,eAAe;AAClC,wBAAI,kBAAkB,qBAAqB;AAC3C,wBAAI,iBAAiB,OAAO,QAAQ;AACpC,wBAAI,WAAW,YAAY,MAAM;AACjC,wBAAI,WAAW,iBAAiB,WAAW,WAAW;AACtD,mCAAe,WAAW,OAAO;AACjC,2BAAO,QAAQ;AAAA,kBACnB;AACA,gCAAc,KAAK,MAAM,cAAc,IAAI,IAAI;AAG/C,sBAAI,aAAa;AACb,wBAAI,mBAAmB,QAAQ,OAAO,SAAUA,SAAQ;AACpD,6BAAO,cAAc,IACfA,QAAO,QAAQ,YAAYA,OAAM,IACjC;AAAA,oBACV,CAAC;AACD,wBAAI,iBAAiB,QAAQ;AACzB,oCAAc,cAAc,kBAAkB,aAAa,WAAW;AAAA,oBAC1E;AAAA,kBACJ;AACA,yBAAO;AAAA,gBACX;AACA,gBAAAvB,SAAQ,gBAAgB;AACxB,yBAAS,cAAc,OAAO;AAC1B,sBAAI,eAAe,CAAC;AACpB,sBAAI,kBAAkB;AACtB,sBAAI,MAAM,MAAM,QAAQ;AACxB,2BAAS,WAAW,GAAG,WAAW,IAAI,QAAQ,YAAY;AACtD,wBAAI,MAAM,IAAI,QAAQ;AACtB,6BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,0BAAI,SAAS,GAAG,EAAE;AAClB,0BAAI,OAAO,aAAa,OAAO,KAAK;AACpC,0BAAI,kBAAkB,GAAG;AACrB;AACA,+BAAO,IAAI,MAAM,OAAO,KAAK;AAAA,sBACjC,WACS,MAAM;AACX,6BAAK,KAAK,UAAU,IAAI;AACxB,0CAAkB,KAAK,KAAK;AAC5B,+BAAO,IAAI,MAAM,OAAO,KAAK;AAC7B,6BAAK;AACL,4BAAI,KAAK,QAAQ,GAAG;AAChB,iCAAO,aAAa,OAAO,KAAK;AAAA,wBACpC;AAAA,sBACJ,OACK;AACD,4BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,4BAAI,CAAC,MAAM;AACP;AAAA,wBACJ;AACA,6BAAK,SAAS,IAAI;AAClB,4BAAI,KAAK,UAAU,GAAG;AAClB,8BAAI,YAAY,IAAI,SAAS;AAC7B,8BAAI,OAAO,KAAK,UAAU,YAAY,YAAY,KAAK;AACvD,uCAAa,OAAO,KAAK,IAAI,EAAE,MAAY,MAAY,IAAS;AAAA,wBACpE;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ;AAAA,gBACJ;AACA,yBAAS,cAAc,OAAO;AAC1B,sBAAI,MAAM,MAAM,QAAQ;AACxB,2BAAS,WAAW,GAAG,WAAW,IAAI,QAAQ,YAAY;AACtD,wBAAI,MAAM,IAAI,QAAQ;AACtB,wBAAI,cAAc;AAClB,wBAAI,uBAAuB;AAC3B,wBAAI,eAAe;AACnB,6BAAS,cAAc,GAAG,cAAc,MAAM,QAAQ,QAAQ,eAAe;AACzE,0BAAI,SAAS,MAAM,QAAQ,WAAW;AAEtC,sCAAgB;AAChB,0BAAI,eAAe,KAAK,MAAM,QAAQ,cAAc,CAAC,GAAG;AACpD,gDAAwB,OAAO;AAC/B,+BAAO,IAAI,MAAM,OAAO,KAAK;AAAA,sBACjC,WACS,aAAa;AAClB,4BAAI,OAAO;AACX,+BAAO,IAAI,MAAM,OAAO,KAAK;AAC7B,sCAAc;AACd,6BAAK,QAAQ,OAAO,QAAQ;AAAA,sBAChC,OACK;AACD,4BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,4BAAI,CAAC;AACD;AACJ,uCAAe,KAAK;AACpB,+CAAuB;AACvB,4BAAI,KAAK,UAAU,GAAG;AAClB,wCAAc;AACd,kDAAwB,OAAO;AAC/B;AAAA,wBACJ;AACA,6BAAK,QAAQ,OAAO,QAAQ;AAAA,sBAChC;AAAA,oBACJ;AAAA,kBACJ;AAAA,gBACJ;AACA,yBAAS,WAAW,OAAO,KAAK;AAC5B,sBAAI,gBAAgB,EAAE,OAAO,GAAG,QAAQ,EAAE;AAC1C,2BAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,GAAG,KAAK,GAAG,QAAQ,MAAM;AACzD,wBAAI,MAAM,GAAG,EAAE;AACf,6BAAS,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK,GAAG,QAAQ,MAAM;AACvD,0BAAI,SAAS,GAAG,EAAE;AAClB,0BAAI,OAAO,IAAI,MAAM,OAAO,KAAK;AACjC,0BAAI,CAAC;AACD;AACJ,0BAAI,YAAY,KAAK,QAAQ,IAAI;AACjC,0BAAI,YAAY,KAAK,QAAQ,KAAK,QAAQ,YAAY;AACtD,0BAAI,KAAK,OAAO,aAAa,aAAa;AAEtC,6BAAK,OAAO,IAAI,gBAAgB,KAAK,MAAM,YAAY,IAAI,IAAI,YAAY,GAAG,EAAE,UAAU,KAAK,OAAO,SAAS,CAAC;AAAA,sBACpH,WACS,KAAK,OAAO,aAAa,aAAa;AAC3C,6BAAK,OAAO,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,KAAK;AAAA,sBACvE,WACS,KAAK,OAAO,aAAa,UAAU;AACxC,6BAAK,OAAO,UAAU,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,EAAE;AAAA,sBACpE,WACS,OAAO,KAAK,OAAO,aAAa,YAAY;AACjD,4BAAI,SAAS,KAAK,OAAO,SAAS,KAAK,MAAM,SAAS;AACtD,4BAAI,OAAO,WAAW,UAAU;AAC5B,+BAAK,OAAO,CAAC,MAAM;AAAA,wBACvB,OACK;AACD,+BAAK,OAAO;AAAA,wBAChB;AAAA,sBACJ;AACA,2BAAK,gBAAgB,KAAK,iBAAiB,IAAI,YAAY,GAAG,IAAI,oBAAoB,CAAC;AACvF,0BAAI,oBAAoB,KAAK,gBAAgB,KAAK;AAClD,0BAAI,KAAK,UAAU,KACf,cAAc,QAAQ,cAAc,SAChC,oBAAoB,KAAK,SAAS;AACtC,wCAAgB,EAAE,QAAQ,mBAAmB,OAAO,KAAK,QAAQ;AAAA,sBACrE,WACS,iBAAiB,cAAc,QAAQ,GAAG;AAC/C,4BAAI,cAAc,SAAS,mBAAmB;AAC1C,8CAAoB,cAAc;AAAA,wBACtC;AAAA,sBACJ;AACA,0BAAI,oBAAoB,IAAI,QAAQ;AAChC,4BAAI,SAAS;AAAA,sBACjB;AAAA,oBACJ;AACA,kCAAc;AAAA,kBAClB;AAAA,gBACJ;AACA,yBAAS,UAAU,MAAM,OAAO,QAAQ,KAAK,UAAU;AACnD,yBAAO,KAAK,IAAI,SAAU,KAAK;AAAE,2BAAO,aAAa,KAAK,OAAO,QAAQ,KAAK,QAAQ;AAAA,kBAAG,CAAC;AAAA,gBAC9F;AACA,gBAAAA,SAAQ,YAAY;AACpB,yBAAS,aAAa,MAAM,OAAO,QAAQ,KAAK,UAAU;AACtD,sBAAI,YAAY,MAAQ,IAAI,YAAY;AACxC,0BAAQ,KAAK,KAAK,QAAQ,SAAS,IAAI;AACvC,sBAAI,UAAU,GAAG,SAAS,gBAAgB,MAAM,QAAQ,GAAG,GAAG;AAC1D,2BAAO;AAAA,kBACX;AACA,yBAAO,SAAS,GAAG,SAAS,gBAAgB,OAAO,UAAU,QAAQ,GAAG,GAAG;AACvE,wBAAI,KAAK,UAAU,GAAG;AAClB;AAAA,oBACJ;AACA,2BAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,kBAC5C;AACA,yBAAO,KAAK,KAAK,IAAI;AAAA,gBACzB;AAAA,cAGM;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA,cACC,SAASwB,SAAQ;AAExB,oBAAG,OAAO,qCAAqC,aAAa;AAAE,sBAAI,IAAI,IAAI,MAAM,gCAAgC;AAAG,oBAAE,OAAO;AAAoB,wBAAM;AAAA,gBAAG;AAEzJ,gBAAAA,QAAO,UAAU;AAAA,cAEX;AAAA;AAAA;AAAA,UAEI;AAGA,cAAI,2BAA2B,CAAC;AAGhC,mBAAS,oBAAoB,UAAU;AAEtC,gBAAI,eAAe,yBAAyB,QAAQ;AACpD,gBAAI,iBAAiB,QAAW;AAC/B,qBAAO,aAAa;AAAA,YACrB;AAEA,gBAAIA,UAAS,yBAAyB,QAAQ,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAGjD,SAAS,CAAC;AAAA;AAAA,YACX;AAGA,gCAAoB,QAAQ,EAAE,KAAKA,QAAO,SAASA,SAAQA,QAAO,SAAS,mBAAmB;AAG9F,mBAAOA,QAAO;AAAA,UACf;AAGV,cAAI,sBAAsB,CAAC;AAE3B,WAAC,WAAW;AACZ,gBAAIxB,WAAU;AAEd,mBAAO,eAAeA,UAAS,cAAe,EAAE,OAAO,KAAK,CAAE;AAC9D,YAAAA,SAAQ,OAAOA,SAAQ,SAASA,SAAQ,MAAMA,SAAQ,QAAQA,SAAQ,eAAeA,SAAQ,cAAcA,SAAQ,gBAAgBA,SAAQ,cAAc;AACzJ,gBAAI,gBAAgB,oBAAoB,GAAG;AAC3C,gBAAI,gBAAgB,oBAAoB,GAAG;AAC3C,gBAAI,gBAAgB,oBAAoB,GAAG;AAC3C,gBAAI,oBAAoB,oBAAoB,GAAG;AAC/C,gBAAI,WAAW,oBAAoB,GAAG;AACtC,mBAAO,eAAeA,UAAS,SAAU,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,qBAAO,SAAS;AAAA,YAAO,EAAE,CAAE;AAC3G,gBAAI,aAAa,oBAAoB,GAAG;AACxC,mBAAO,eAAeA,UAAS,gBAAiB,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,qBAAO,WAAW;AAAA,YAAc,EAAE,CAAE;AAC3H,gBAAI,WAAW,oBAAoB,GAAG;AACtC,mBAAO,eAAeA,UAAS,QAAS,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,qBAAO,SAAS;AAAA,YAAM,EAAE,CAAE;AACzG,mBAAO,eAAeA,UAAS,UAAW,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,qBAAO,SAAS;AAAA,YAAQ,EAAE,CAAE;AAC7G,mBAAO,eAAeA,UAAS,OAAQ,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,qBAAO,SAAS;AAAA,YAAK,EAAE,CAAE;AAGvG,qBAAS,YAAYyB,QAAO;AACxB,eAAC,GAAG,cAAc,SAASA,MAAK;AAAA,YACpC;AACA,YAAAzB,SAAQ,cAAc;AACtB,qBAAS,UAAU,GAAG,SAAS;AAC3B,kBAAI,SAAS,GAAG,cAAc,YAAY,GAAG,OAAO;AACpD,kBAAI,SAAS,GAAG,kBAAkB,aAAa,GAAG,KAAK;AACvD,eAAC,GAAG,cAAc,WAAW,GAAG,KAAK;AAAA,YACzC;AAEA,qBAAS,cAAc,GAAG,SAAS;AAC/B,kBAAI,SAAS,GAAG,cAAc,YAAY,GAAG,OAAO;AACpD,sBAAQ,GAAG,kBAAkB,aAAa,GAAG,KAAK;AAAA,YACtD;AACA,YAAAA,SAAQ,gBAAgB;AACxB,qBAAS,YAAY,GAAG,OAAO;AAC3B,eAAC,GAAG,cAAc,WAAW,GAAG,KAAK;AAAA,YACzC;AACA,YAAAA,SAAQ,cAAc;AACtB,gBAAI;AAEA,kBAAI,QAAQ,oBAAoB,GAAG;AAGnC,kBAAI,MAAM;AACN,wBAAQ,MAAM;AAClB,0BAAY,KAAK;AAAA,YACrB,SACO,OAAO;AAAA,YAId;AACA,YAAAA,SAAQ,SAAS,IAAI;AAAA,UAErB,EAAE;AACQ,iBAAO;AAAA,QACR,EAAG;AAAA;AAAA,IAEZ,CAAC;AAAA;AAAA;", "names": ["exports", "d", "b", "HookData", "CellHookData", "__webpack_require__", "text", "HtmlRowInput", "window", "<PERSON><PERSON><PERSON><PERSON>", "global", "prop", "options", "_a", "current", "Table", "Row", "Cell", "Column", "x1", "y1", "x2", "y2", "column", "module", "jsPDF"]}