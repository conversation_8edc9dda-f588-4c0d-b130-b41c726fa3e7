{"name": "@babel/compat-data", "version": "7.26.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "The compat-data to determine required Babel plugins", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-compat-data"}, "publishConfig": {"access": "public"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js", "./overlapping-plugins": "./overlapping-plugins.js", "./plugin-bugfixes": "./plugin-bugfixes.js"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.js && node ./scripts/build-modules-support.js && node ./scripts/build-bugfixes-targets.js"}, "keywords": ["babel", "compat-table", "compat-data"], "devDependencies": {"@mdn/browser-compat-data": "^5.5.36", "core-js-compat": "^3.40.0", "electron-to-chromium": "^1.4.816"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}