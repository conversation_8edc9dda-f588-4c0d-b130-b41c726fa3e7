{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@date-io/date-fns": "^3.2.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.3.0", "@mui/material": "^6.3.0", "@mui/styled-engine": "^6.3.0", "@mui/x-date-pickers": "^7.25.0", "axios": "^1.7.9", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "file-saver": "^2.0.5", "framer-motion": "^12.0.5", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lucide-react": "^0.469.0", "node-cron": "^3.0.3", "react": "^18.3.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-router-dom": "^7.1.1", "recharts": "^2.15.0", "socket.io-client": "^4.8.1", "twilio": "^5.4.2", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "vite": "^6.0.5"}}