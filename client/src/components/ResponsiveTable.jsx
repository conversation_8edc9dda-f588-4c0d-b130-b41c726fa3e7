import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Card,
  CardContent,
  Typography,
  Stack,
  Chip,
  IconButton,
  useTheme,
  alpha
} from '@mui/material';
import { useResponsive } from '../hooks/useResponsive';

/**
 * ResponsiveTable component that switches between table and card layout
 * based on screen size for better mobile experience
 */
const ResponsiveTable = ({
  columns,
  data,
  renderActions,
  getRowColor,
  emptyMessage = "No data available",
  ...tableProps
}) => {
  const theme = useTheme();
  const { isMobile, isTablet } = useResponsive();

  // Mobile card layout
  const renderMobileCards = () => (
    <Stack spacing={2}>
      {data.length === 0 ? (
        <Card>
          <CardContent>
            <Typography variant="body2" color="text.secondary" align="center">
              {emptyMessage}
            </Typography>
          </CardContent>
        </Card>
      ) : (
        data.map((row, index) => (
          <Card
            key={index}
            sx={{
              borderRadius: 2,
              border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
              '&:hover': {
                boxShadow: theme.shadows[4],
                borderColor: theme.palette.primary.main
              }
            }}
          >
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Stack spacing={1.5}>
                {columns.map((column, colIndex) => {
                  const value = row[column.field];
                  const displayValue = column.renderCell 
                    ? column.renderCell(row, value) 
                    : value;

                  if (column.field === 'actions') {
                    return (
                      <Box key={colIndex} sx={{ pt: 1 }}>
                        {renderActions && renderActions(row)}
                      </Box>
                    );
                  }

                  return (
                    <Box key={colIndex}>
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{ fontWeight: 600, textTransform: 'uppercase' }}
                      >
                        {column.headerName}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ 
                          mt: 0.5,
                          fontWeight: column.primary ? 600 : 400,
                          color: column.primary ? 'text.primary' : 'text.secondary'
                        }}
                      >
                        {displayValue}
                      </Typography>
                    </Box>
                  );
                })}
              </Stack>
            </CardContent>
          </Card>
        ))
      )}
    </Stack>
  );

  // Tablet layout - simplified table
  const renderTabletTable = () => (
    <TableContainer 
      component={Paper} 
      sx={{ 
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
        overflow: 'auto'
      }}
    >
      <Table size="small" {...tableProps}>
        <TableHead>
          <TableRow>
            {columns
              .filter(col => !col.hideOnTablet)
              .map((column, index) => (
                <TableCell
                  key={index}
                  sx={{
                    fontWeight: 600,
                    bgcolor: alpha(theme.palette.primary.main, 0.04),
                    fontSize: '0.8125rem',
                    py: 1.5
                  }}
                >
                  {column.headerName}
                </TableCell>
              ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell 
                colSpan={columns.filter(col => !col.hideOnTablet).length} 
                align="center"
                sx={{ py: 4 }}
              >
                <Typography variant="body2" color="text.secondary">
                  {emptyMessage}
                </Typography>
              </TableCell>
            </TableRow>
          ) : (
            data.map((row, index) => (
              <TableRow
                key={index}
                sx={{
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.04)
                  },
                  bgcolor: getRowColor ? getRowColor(row) : 'transparent'
                }}
              >
                {columns
                  .filter(col => !col.hideOnTablet)
                  .map((column, colIndex) => {
                    const value = row[column.field];
                    const displayValue = column.renderCell 
                      ? column.renderCell(row, value) 
                      : value;

                    if (column.field === 'actions') {
                      return (
                        <TableCell key={colIndex} sx={{ py: 1 }}>
                          {renderActions && renderActions(row)}
                        </TableCell>
                      );
                    }

                    return (
                      <TableCell 
                        key={colIndex}
                        sx={{ 
                          py: 1,
                          fontSize: '0.8125rem'
                        }}
                      >
                        {displayValue}
                      </TableCell>
                    );
                  })}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Desktop layout - full table
  const renderDesktopTable = () => (
    <TableContainer 
      component={Paper} 
      sx={{ 
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
        overflow: 'auto'
      }}
    >
      <Table {...tableProps}>
        <TableHead>
          <TableRow>
            {columns.map((column, index) => (
              <TableCell
                key={index}
                sx={{
                  fontWeight: 600,
                  bgcolor: alpha(theme.palette.primary.main, 0.04),
                  py: 2
                }}
              >
                {column.headerName}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell 
                colSpan={columns.length} 
                align="center"
                sx={{ py: 6 }}
              >
                <Typography variant="body1" color="text.secondary">
                  {emptyMessage}
                </Typography>
              </TableCell>
            </TableRow>
          ) : (
            data.map((row, index) => (
              <TableRow
                key={index}
                sx={{
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.04)
                  },
                  bgcolor: getRowColor ? getRowColor(row) : 'transparent'
                }}
              >
                {columns.map((column, colIndex) => {
                  const value = row[column.field];
                  const displayValue = column.renderCell 
                    ? column.renderCell(row, value) 
                    : value;

                  if (column.field === 'actions') {
                    return (
                      <TableCell key={colIndex}>
                        {renderActions && renderActions(row)}
                      </TableCell>
                    );
                  }

                  return (
                    <TableCell key={colIndex}>
                      {displayValue}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render appropriate layout based on screen size
  if (isMobile) {
    return renderMobileCards();
  } else if (isTablet) {
    return renderTabletTable();
  } else {
    return renderDesktopTable();
  }
};

export default ResponsiveTable;
