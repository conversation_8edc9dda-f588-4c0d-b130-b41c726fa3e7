/* eslint-disable react/prop-types */
import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  IconButton,
  Fade,
  useTheme,
  Snackbar,
  Alert,
  Grid,
  Stack,
  Avatar,
  Chip,
  Divider,
  alpha,
  Zoom,
  Tooltip
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Inventory as InventoryIcon,
  Category as CategoryIcon,
  AttachMoney as MoneyIcon,
  DateRange as DateIcon,
  QrCode as BarcodeIcon
} from '@mui/icons-material';
import { getApiUrl } from '../config';
import { useTranslation } from 'react-i18next';

const ProductDialog = ({ open, onClose, product, onSave }) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    sku: '',
    stock: '',
    lowStockThreshold: '', 
    price: '',
    category: '',
    manufacturing_date: '',
    expiry_date: '',
    store_id: ''
  });
  const [stores, setStores] = useState([]); // Add state for stores
  const [selectedStoreId, setSelectedStoreId] = useState(''); // Add state for selected store
  const [showSuccess, setShowSuccess] = useState(false);

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        sku: product.sku || '',
        stock: product.stock || '',
        lowStockThreshold: product.lowStockThreshold || '',
        price: product.price || '',
        category: product.category || '',
        manufacturing_date: product.manufacturing_date || '',
        expiry_date: product.expiry_date || '',
        store_id: product.store_id || '',
      });
       // Set selected store if product has one
      if (product.storeId) {
        setSelectedStoreId(product.storeId);
      }
    }
  }, [product]);

  // Fetch stores when dialog opens
  useEffect(() => {
    if (open) {
      const fetchStores = async () => {
        try {
          const response = await fetch(getApiUrl('api/store'));
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          setStores(data);
        } catch (error) {
          console.error('Error fetching stores:', error);
        }
      };
      fetchStores();
    }
  }, [open]); // Fetch stores when dialog open state changes

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStoreChange = (e) => {
    setSelectedStoreId(e.target.value);
  };

  const handleSubmit = async () => {
    if (!product) {
      try {
        const response = await fetch(getApiUrl('api/product/add'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            expiry_date: formData.expiry_date, // Ensure expiry_date is sent correctly
            store_id: selectedStoreId // Include selected store ID
          })
        });

        if (response.ok) {
          const data = await response.json();
          onSave(data.product);
          setShowSuccess(true);
          setTimeout(() => {
            onClose();
          }, 1500);
        } else {
          console.error('Failed to add product');
        }
      } catch (error) {
        console.error('Error adding product:', error);
      }
    } else {
      onSave(formData);
      onClose();
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        TransitionComponent={Zoom}
        transitionDuration={400}
        PaperProps={{
          sx: {
            borderRadius: 4,
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 20px 60px rgba(25, 118, 210, 0.3)',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle
          sx={{
            background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
            borderRadius: '16px 16px 0 0',
            p: 0,
            m: 0
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" p={3}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                  width: 48,
                  height: 48,
                  boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                }}
              >
                {product ? <EditIcon /> : <AddIcon />}
              </Avatar>
              <Box>
                <Typography variant="h5" fontWeight={700} color="#1976d2">
                  {product ? 'Edit Product' : 'Add New Product'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {product ? 'Update product information' : 'Create a new product in your inventory'}
                </Typography>
              </Box>
            </Stack>
            <Tooltip title="Close">
              <IconButton
                onClick={onClose}
                sx={{
                  color: 'text.secondary',
                  '&:hover': {
                    backgroundColor: 'rgba(25, 118, 210, 0.1)',
                    color: '#1976d2'
                  }
                }}
              >
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        </DialogTitle>

        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Grid container spacing={3}>
            {/* Product Name */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <InventoryIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                    Product Information
                  </Typography>
                </Stack>
                <TextField
                  name="name"
                  label="Product Name"
                  value={formData.name}
                  onChange={handleChange}
                  fullWidth
                  required
                  variant="outlined"
                  placeholder="Enter product name"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* SKU */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <BarcodeIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                    Product Code
                  </Typography>
                </Stack>
                <TextField
                  name="sku"
                  label="SKU / Product Code"
                  value={formData.sku}
                  onChange={handleChange}
                  fullWidth
                  required
                  variant="outlined"
                  placeholder="Enter SKU or product code"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>
            {/* Store Selection */}
            <Grid item xs={12}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <CategoryIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                    Store Assignment
                  </Typography>
                </Stack>
                <FormControl
                  fullWidth
                  required
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                >
                  <InputLabel>Store</InputLabel>
                  <Select
                    name="storeId"
                    value={selectedStoreId}
                    onChange={handleStoreChange}
                    label="Store"
                  >
                    <MenuItem value=""><em>Select a Store</em></MenuItem>
                    {stores.map((store) => (
                      <MenuItem key={store._id} value={store._id}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <CategoryIcon fontSize="small" />
                          <span>{store.storeName} ({store.storeLocation})</span>
                        </Stack>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Stack>
            </Grid>

            {/* Stock Information */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                  Stock Information
                </Typography>
                <TextField
                  name="stock"
                  label="Current Stock"
                  type="number"
                  value={formData.stock}
                  onChange={handleChange}
                  required
                  variant="outlined"
                  placeholder="0"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                  Low Stock Alert
                </Typography>
                <TextField
                  name="lowStockThreshold"
                  label="Low Stock Alert"
                  type="number"
                  value={formData.lowStockThreshold}
                  onChange={handleChange}
                  required
                  variant="outlined"
                  placeholder="0"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* Price */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <MoneyIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                    Pricing
                  </Typography>
                </Stack>
                <TextField
                  name="price"
                  label="Price"
                  type="number"
                  value={Math.floor(formData.price)}
                  onChange={handleChange}
                  required
                  variant="outlined"
                  placeholder="0.00"
                  InputProps={{
                    startAdornment: (
                      <Typography sx={{ mr: 1, color: '#1976d2', fontWeight: 600 }}>
                        ₹
                      </Typography>
                    )
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* Category */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                  Product Category
                </Typography>
                <FormControl
                  fullWidth
                  required
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                >
                  <InputLabel>Category</InputLabel>
                  <Select
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    label="Category"
                  >
                    <MenuItem value="Laying">
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <CategoryIcon fontSize="small" />
                        <span>Laying</span>
                      </Stack>
                    </MenuItem>
                    <MenuItem value="ElectricalMaterial">
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <CategoryIcon fontSize="small" />
                        <span>Electrical Material</span>
                      </Stack>
                    </MenuItem>
                    <MenuItem value="StructureMaterial">
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <CategoryIcon fontSize="small" />
                        <span>Structure Material</span>
                      </Stack>
                    </MenuItem>
                    <MenuItem value="PlumbingMaterials">
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <CategoryIcon fontSize="small" />
                        <span>Plumbing Materials</span>
                      </Stack>
                    </MenuItem>
                    <MenuItem value="ToolsAndEquipment">
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <CategoryIcon fontSize="small" />
                        <span>Tools And Equipment</span>
                      </Stack>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Stack>
            </Grid>

            {/* Manufacturing Date */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <DateIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                    Manufacturing Date
                  </Typography>
                </Stack>
                <TextField
                  name="manufacturing_date"
                  label="Manufacturing Date"
                  type="date"
                  value={formData.manufacturing_date}
                  onChange={handleChange}
                  required
                  variant="outlined"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Stack>
            </Grid>

            {/* Expiry Date */}
            <Grid item xs={12} sm={6}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <DateIcon sx={{ color: '#f44336', fontSize: 20 }} />
                  <Typography variant="subtitle2" fontWeight={600} color="#f44336">
                    Expiry Date
                  </Typography>
                </Stack>
                <TextField
                  name="expiry_date"
                  label="Expiry Date"
                  type="date"
                  value={formData.expiry_date}
                  onChange={handleChange}
                  required
                  variant="outlined"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#f44336',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#f44336',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#f44336',
                    },
                  }}
                />
              </Stack>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions
          sx={{
            p: 3,
            background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(21, 101, 192, 0.01) 100%)',
            borderTop: '1px solid rgba(25, 118, 210, 0.1)',
            gap: 2
          }}
        >
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{
              borderRadius: 3,
              textTransform: 'none',
              px: 4,
              py: 1.5,
              fontWeight: 600,
              borderColor: '#bdbdbd',
              color: '#757575',
              '&:hover': {
                borderColor: '#9e9e9e',
                backgroundColor: 'rgba(189, 189, 189, 0.1)',
              }
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            type="button"
            disabled={!formData.name || !formData.sku || !formData.stock || !formData.price || !formData.category || !formData.expiry_date}
            sx={{
              borderRadius: 3,
              textTransform: 'none',
              px: 4,
              py: 1.5,
              fontWeight: 700,
              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                boxShadow: '0 6px 25px rgba(25, 118, 210, 0.4)',
                transform: 'translateY(-1px)',
              },
              '&:disabled': {
                background: 'rgba(0, 0, 0, 0.12)',
                color: 'rgba(0, 0, 0, 0.26)',
                boxShadow: 'none',
              },
              transition: 'all 0.3s ease',
            }}
          >
            {product ? 'Save Changes' : 'Add Product'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={showSuccess}
        autoHideDuration={3000}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          severity="success"
          sx={{
            width: '100%',
            borderRadius: 3,
            backdropFilter: 'blur(20px)',
            background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.95) 0%, rgba(56, 142, 60, 0.95) 100%)',
            color: 'white',
            fontWeight: 600,
            boxShadow: '0 8px 32px rgba(76, 175, 80, 0.3)',
            '& .MuiAlert-icon': {
              color: 'white'
            }
          }}
        >
          Product added successfully!
        </Alert>
      </Snackbar>
    </>
  );
};

export default ProductDialog;
