import React from 'react';
import { useTranslation } from 'react-i18next';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import InputBase from '@mui/material/InputBase';
import { styled } from '@mui/material/styles';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import './PremiumLanguageSwitcher.css';

const StyledSelect = styled(Select)(({ theme }) => ({
  background: 'rgba(255,255,255,0.7)',
  borderRadius: 12,
  fontSize: '1.1rem',
  fontFamily: 'Inter, Segoe UI, Arial, sans-serif',
  color: '#222',
  padding: '0 32px 0 12px',
  minHeight: 40,
  height: 40,
  lineHeight: '40px',
  boxShadow: '0 2px 8px rgba(31,38,135,0.07)',
  '& .MuiSelect-select': {
    padding: 0,
    display: 'flex',
    alignItems: 'center',
    height: 40,
    minHeight: 40,
    lineHeight: '40px',
  },
  '& .MuiSelect-icon': {
    color: '#222',
    right: 12,
    top: 'calc(50% - 12px)',
  },
  '&:focus': {
    background: 'rgba(255,255,255,0.95)',
    color: '#007bff',
    boxShadow: '0 0 0 2px #b3d4fc',
  },
}));

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  fontSize: '1rem',
  borderRadius: 8,
  padding: '8px 12px',
  fontFamily: 'Inter, Segoe UI, Arial, sans-serif',
  minHeight: 40,
  height: 40,
  lineHeight: '40px',
}));

const PremiumLanguageSwitcher = ({ className = '' }) => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className={`premium-language-switcher ${className}`}>
      <StyledSelect
        value={i18n.language}
        onChange={(e) => changeLanguage(e.target.value)}
        input={<InputBase disableUnderline />}
        variant="standard"
        IconComponent={ArrowDropDownIcon}
        MenuProps={{
          PaperProps: {
            style: {
              borderRadius: 16,
              boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
              minWidth: 120,
            },
          },
        }}
      >
        <StyledMenuItem value="en">English</StyledMenuItem>
        <StyledMenuItem value="hi">हिंदी</StyledMenuItem>
        <StyledMenuItem value="te">తెలుగు</StyledMenuItem>
      </StyledSelect>
    </div>
  );
};

export default PremiumLanguageSwitcher; 