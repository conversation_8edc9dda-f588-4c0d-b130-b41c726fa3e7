/* eslint-disable react/prop-types */
import {
  Card,
  Typography,
  Box,
  Stack,
  LinearProgress,
  Chip,
  Button,
  useTheme,
  alpha
} from '@mui/material';
import { Receipt } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import ResponsiveTable from './ResponsiveTable';
import { useResponsive } from '../hooks/useResponsive';

const InventoryTable = ({
  paginatedInventory,
  tablePage,
  totalTablePages,
  handleTablePrevPage,
  handleTableNextPage,
  setSelectedProduct,
  setStockUpdateDialog,
  setBillDialog,
  getStockPercentage
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { isMobile, isTouchDevice } = useResponsive();

  const getStockColor = (stock, lowStockThreshold) =>
    stock < lowStockThreshold ? theme.palette.error.main : theme.palette.success.main;

  // Define table columns for responsive table
  const columns = [
    {
      field: 'name',
      headerName: t('table.productName'),
      primary: true,
      renderCell: (row) => (
        <Typography variant="body2" fontWeight={500}>
          {row.name}
        </Typography>
      )
    },
    {
      field: 'sku',
      headerName: t('table.sku'),
      hideOnTablet: isMobile,
      renderCell: (row) => (
        <Typography variant="body2" color="text.secondary">
          {row.sku}
        </Typography>
      )
    },
    {
      field: 'category',
      headerName: t('table.category'),
      renderCell: (row) => (
        <Chip
          label={row.category}
          size="small"
          sx={{
            borderRadius: 2,
            bgcolor: alpha(theme.palette.primary.main, 0.08),
            color: 'primary.main',
            fontWeight: 600,
            px: 1,
            fontSize: isMobile ? '0.75rem' : '0.8125rem'
          }}
        />
      )
    },
    {
      field: 'stock',
      headerName: t('table.stockLevel'),
      renderCell: (row) => (
        <Stack direction={isMobile ? "column" : "row"} alignItems="center" spacing={isMobile ? 0.5 : 2}>
          <Typography variant="body2" fontWeight={500}>
            {row.stock}
          </Typography>
          <Box sx={{ width: isMobile ? '100%' : 100 }}>
            <LinearProgress
              variant="determinate"
              value={getStockPercentage(row.stock, row.lowStockThreshold)}
              sx={{
                height: 6,
                borderRadius: 3,
                bgcolor: alpha(
                  getStockColor(row.stock, row.lowStockThreshold),
                  0.12
                )
              }}
              color={row.stock < row.lowStockThreshold ? "error" : "success"}
            />
          </Box>
        </Stack>
      )
    },
    {
      field: 'price',
      headerName: t('table.price'),
      hideOnTablet: isMobile,
      renderCell: (row) => (
        <Typography variant="body2" fontWeight={600}>
          ₹{Math.floor(row.price)}
        </Typography>
      )
    },
    {
      field: 'status',
      headerName: t('table.status'),
      renderCell: (row) => (
        <Chip
          label={row.stock < row.lowStockThreshold ? t('status.lowStock') : t('status.inStock')}
          color={row.stock < row.lowStockThreshold ? "error" : "success"}
          size="small"
          sx={{
            borderRadius: 2,
            fontWeight: 600,
            px: 1,
            fontSize: isMobile ? '0.75rem' : '0.8125rem'
          }}
        />
      )
    },
    {
      field: 'actions',
      headerName: t('table.actions'),
      renderCell: () => null // Will be handled by renderActions
    }
  ];

  // Render action buttons for each row
  const renderActions = (row) => (
    <Stack direction={isMobile ? "column" : "row"} spacing={1}>
      <Button
        variant="soft"
        size="small"
        onClick={() => {
          setSelectedProduct(row);
          setStockUpdateDialog(true);
        }}
        fullWidth={isMobile}
        sx={{
          borderRadius: 2,
          textTransform: 'none',
          bgcolor: alpha(theme.palette.primary.main, 0.08),
          color: 'primary.main',
          minHeight: isTouchDevice ? '40px' : '32px',
          fontSize: isMobile ? '0.8125rem' : '0.875rem',
          '&:hover': {
            bgcolor: alpha(theme.palette.primary.main, 0.16)
          }
        }}
      >
        {t('actions.updateStock')}
      </Button>
    </Stack>
  );

  return (
    <Box>
      <ResponsiveTable
        columns={columns}
        data={paginatedInventory}
        renderActions={renderActions}
        emptyMessage={t('table.noData')}
      />

      {/* Pagination */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          py: isMobile ? 2 : 3,
          px: 2,
          mt: 2
        }}
      >
        <Stack
          direction={isMobile ? "column" : "row"}
          spacing={isMobile ? 1 : 2}
          alignItems="center"
          sx={{ width: isMobile ? '100%' : 'auto' }}
        >
          <Button
            variant="soft"
            onClick={handleTablePrevPage}
            disabled={tablePage === 0}
            fullWidth={isMobile}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              px: 3,
              py: 1,
              minHeight: isTouchDevice ? '44px' : '40px',
              bgcolor: alpha(theme.palette.primary.main, 0.08),
              color: 'primary.main',
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.16)
              }
            }}
          >
            {t('actions.previous')}
          </Button>

          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              textAlign: 'center',
              minWidth: isMobile ? 'auto' : '120px'
            }}
          >
            {t('table.pageInfo', { page: tablePage + 1, total: totalTablePages })}
          </Typography>

          <Button
            variant="soft"
            onClick={handleTableNextPage}
            disabled={tablePage >= totalTablePages - 1}
            fullWidth={isMobile}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              px: 3,
              py: 1,
              minHeight: isTouchDevice ? '44px' : '40px',
              bgcolor: alpha(theme.palette.primary.main, 0.08),
              color: 'primary.main',
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.16)
              }
            }}
          >
            {t('actions.next')}
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default InventoryTable;