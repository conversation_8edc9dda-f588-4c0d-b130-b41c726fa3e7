import React from 'react';
import {
  Grid,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  Button,
  Stack,
  Box,
  useTheme
} from '@mui/material';
import { useResponsive } from '../hooks/useResponsive';

/**
 * ResponsiveForm component that adapts form layouts for different screen sizes
 */
const ResponsiveForm = ({
  fields,
  values,
  errors,
  onChange,
  onSubmit,
  submitLabel = "Submit",
  cancelLabel = "Cancel",
  onCancel,
  loading = false,
  children,
  ...formProps
}) => {
  const theme = useTheme();
  const { isMobile, isTablet, isTouchDevice } = useResponsive();

  const getGridSize = (field) => {
    if (isMobile) {
      return { xs: 12 };
    }
    if (isTablet) {
      return field.tabletSize || { xs: 12, sm: field.fullWidth ? 12 : 6 };
    }
    return field.desktopSize || { xs: 12, sm: field.fullWidth ? 12 : 6, md: field.fullWidth ? 12 : 4 };
  };

  const renderField = (field, index) => {
    const gridSize = getGridSize(field);
    const fieldValue = values[field.name] || '';
    const fieldError = errors[field.name];
    const hasError = Boolean(fieldError);

    const commonProps = {
      fullWidth: true,
      error: hasError,
      helperText: fieldError,
      size: isMobile ? 'medium' : 'small',
      sx: {
        '& .MuiInputBase-root': {
          minHeight: isTouchDevice ? '48px' : '40px'
        }
      }
    };

    let fieldComponent;

    switch (field.type) {
      case 'select':
        fieldComponent = (
          <FormControl {...commonProps}>
            <InputLabel>{field.label}</InputLabel>
            <Select
              value={fieldValue}
              label={field.label}
              onChange={(e) => onChange(field.name, e.target.value)}
              disabled={field.disabled || loading}
            >
              {field.options?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {fieldError && <FormHelperText error>{fieldError}</FormHelperText>}
          </FormControl>
        );
        break;

      case 'textarea':
        fieldComponent = (
          <TextField
            {...commonProps}
            label={field.label}
            value={fieldValue}
            onChange={(e) => onChange(field.name, e.target.value)}
            multiline
            rows={isMobile ? 3 : field.rows || 4}
            placeholder={field.placeholder}
            disabled={field.disabled || loading}
          />
        );
        break;

      case 'number':
        fieldComponent = (
          <TextField
            {...commonProps}
            type="number"
            label={field.label}
            value={fieldValue}
            onChange={(e) => onChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled || loading}
            inputProps={{
              min: field.min,
              max: field.max,
              step: field.step
            }}
          />
        );
        break;

      default:
        fieldComponent = (
          <TextField
            {...commonProps}
            type={field.type || 'text'}
            label={field.label}
            value={fieldValue}
            onChange={(e) => onChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled || loading}
          />
        );
    }

    return (
      <Grid item {...gridSize} key={field.name || index}>
        {fieldComponent}
      </Grid>
    );
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(e);
  };

  return (
    <Box component="form" onSubmit={handleSubmit} {...formProps}>
      <Grid container spacing={isMobile ? 2 : 3}>
        {fields.map((field, index) => renderField(field, index))}
        
        {children && (
          <Grid item xs={12}>
            {children}
          </Grid>
        )}
        
        <Grid item xs={12}>
          <Stack
            direction={isMobile ? 'column-reverse' : 'row'}
            spacing={isMobile ? 1 : 2}
            justifyContent="flex-end"
            sx={{ mt: isMobile ? 2 : 1 }}
          >
            {onCancel && (
              <Button
                variant="outlined"
                onClick={onCancel}
                disabled={loading}
                fullWidth={isMobile}
                sx={{
                  minHeight: isTouchDevice ? '48px' : '40px',
                  fontSize: isMobile ? '0.875rem' : '1rem'
                }}
              >
                {cancelLabel}
              </Button>
            )}
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
              fullWidth={isMobile}
              sx={{
                minHeight: isTouchDevice ? '48px' : '40px',
                fontSize: isMobile ? '0.875rem' : '1rem'
              }}
            >
              {loading ? 'Loading...' : submitLabel}
            </Button>
          </Stack>
        </Grid>
      </Grid>
    </Box>
  );
};

/**
 * ResponsiveFormField component for individual field rendering
 */
export const ResponsiveFormField = ({
  field,
  value,
  error,
  onChange,
  disabled = false,
  ...props
}) => {
  const { isMobile, isTouchDevice } = useResponsive();
  
  const commonProps = {
    fullWidth: true,
    error: Boolean(error),
    helperText: error,
    size: isMobile ? 'medium' : 'small',
    disabled,
    sx: {
      '& .MuiInputBase-root': {
        minHeight: isTouchDevice ? '48px' : '40px'
      }
    },
    ...props
  };

  switch (field.type) {
    case 'select':
      return (
        <FormControl {...commonProps}>
          <InputLabel>{field.label}</InputLabel>
          <Select
            value={value || ''}
            label={field.label}
            onChange={(e) => onChange(e.target.value)}
          >
            {field.options?.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
          {error && <FormHelperText error>{error}</FormHelperText>}
        </FormControl>
      );

    case 'textarea':
      return (
        <TextField
          {...commonProps}
          label={field.label}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          multiline
          rows={isMobile ? 3 : field.rows || 4}
          placeholder={field.placeholder}
        />
      );

    case 'number':
      return (
        <TextField
          {...commonProps}
          type="number"
          label={field.label}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={field.placeholder}
          inputProps={{
            min: field.min,
            max: field.max,
            step: field.step
          }}
        />
      );

    default:
      return (
        <TextField
          {...commonProps}
          type={field.type || 'text'}
          label={field.label}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={field.placeholder}
        />
      );
  }
};

export default ResponsiveForm;
