/* eslint-disable react/prop-types */

import {
  Grid,
  Card,
  Stack,
  Typography,
  Avatar,
  useTheme,
  alpha,
  Box,
  useMediaQuery
} from '@mui/material';
import {
  Inventory2,
  LocalShipping,
  AttachMoney,
  BarChart,
  TrendingUp,
  TrendingDown
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useResponsive } from '../hooks/useResponsive';

const DashboardStatsCards = ({
  inventory,
  lowStockItems,
  totalValue,
  stockTrend
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { isMobile, isTablet, getGridColumns } = useResponsive();

  const formatValue = (value) => {
    if (value >= 1e7) {
      return `${(value / 1e7).toFixed(2)}Cr`;
    } else if (value >= 1e5) {
      return `${(value / 1e5).toFixed(2)}L`;
    }
    return value.toLocaleString('en-IN', { style: 'currency', currency: 'INR' });
  };

  const statsConfig = [
    {
      icon: <Inventory2 />,
      title: t('dashboard.totalProducts'),
      value: inventory.length,
      subtitle: `Across ${new Set(inventory.map(item => item.category)).size} categories`,
      color: 'primary',
      bgColor: alpha(theme.palette.primary.main, 0.12)
    },
    {
      icon: <LocalShipping />,
      title: t('dashboard.lowStockItems'),
      value: lowStockItems.length,
      subtitle: 'Require immediate attention',
      color: 'warning',
      bgColor: alpha(theme.palette.warning.light, 0.12)
    },
    {
      icon: <AttachMoney />,
      title: t('dashboard.totalStockValue'),
      value: formatValue(totalValue),
      subtitle: `${Math.abs(stockTrend)}% from last month`,
      color: 'success',
      bgColor: alpha(theme.palette.success.light, 0.12),
      trend: stockTrend
    },
    {
      icon: <BarChart />,
      title: t('dashboard.averageStockLevel'),
      value: Math.round(inventory.reduce((acc, item) => acc + item.stock, 0) / inventory.length),
      subtitle: 'Units per product',
      color: 'info',
      bgColor: alpha(theme.palette.info.light, 0.12)
    }
  ];

  // Responsive grid configuration
  const getGridItemSize = () => {
    if (isMobile) return { xs: 12, sm: 6 };
    if (isTablet) return { xs: 12, sm: 6, md: 6 };
    return { xs: 12, sm: 6, md: 3 };
  };

  const gridItemSize = getGridItemSize();

  return (
    <Grid container spacing={isMobile ? 1.5 : 2}>
      {statsConfig.map((stat, index) => (
        <Grid item {...gridItemSize} key={index}>
          <Card
            elevation={0}
            sx={{
              p: isMobile ? 1.5 : 2,
              borderRadius: isMobile ? 3 : 4,
              bgcolor: stat.bgColor,
              border: `1px solid ${alpha(theme.palette[stat.color].main, 0.24)}`,
              transition: 'all 0.3s',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              '&:hover': {
                transform: isMobile ? 'none' : 'translateY(-4px)',
                boxShadow: theme.shadows[8]
              }
            }}
          >
            <Stack spacing={isMobile ? 1.5 : 2} sx={{ height: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: isMobile ? 1 : 2 }}>
                <Avatar
                  sx={{
                    bgcolor: alpha(theme.palette[stat.color].main, 0.24),
                    color: `${stat.color}.main`,
                    width: isMobile ? 36 : 40,
                    height: isMobile ? 36 : 40,
                    fontSize: isMobile ? '1rem' : '1.25rem'
                  }}
                >
                  {stat.icon}
                </Avatar>

                {isMobile && (
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      color={`${stat.color}.darker`}
                      variant="caption"
                      sx={{ fontSize: '0.75rem', fontWeight: 600 }}
                    >
                      {stat.title}
                    </Typography>
                  </Box>
                )}
              </Box>

              <Stack spacing={0.5} sx={{ flex: 1 }}>
                {!isMobile && (
                  <Typography color={`${stat.color}.darker`} variant="overline">
                    {stat.title}
                  </Typography>
                )}
                <Typography
                  variant={isMobile ? "h5" : "h4"}
                  fontWeight={700}
                  color={`${stat.color}.darker`}
                  sx={{
                    fontSize: isMobile ? '1.5rem' : '2rem',
                    lineHeight: 1.2
                  }}
                >
                  {stat.value}
                </Typography>
                {stat.trend !== undefined ? (
                  <Stack
                    direction="row"
                    alignItems="center"
                    spacing={0.5}
                    sx={{ flexWrap: 'wrap' }}
                  >
                    {stat.trend >= 0 ?
                      <TrendingUp color="success" sx={{ fontSize: isMobile ? 14 : 16 }} /> :
                      <TrendingDown color="error" sx={{ fontSize: isMobile ? 14 : 16 }} />
                    }
                    <Typography
                      variant={isMobile ? "caption" : "subtitle2"}
                      color={`${stat.color}.darker`}
                      sx={{ fontSize: isMobile ? '0.75rem' : '0.875rem' }}
                    >
                      {stat.subtitle}
                    </Typography>
                  </Stack>
                ) : (
                  <Typography
                    variant={isMobile ? "caption" : "subtitle2"}
                    color={`${stat.color}.darker`}
                    sx={{ fontSize: isMobile ? '0.75rem' : '0.875rem' }}
                  >
                    {stat.subtitle}
                  </Typography>
                )}
              </Stack>
            </Stack>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default DashboardStatsCards;