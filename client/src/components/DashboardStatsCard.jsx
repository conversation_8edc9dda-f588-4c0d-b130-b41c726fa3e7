/* eslint-disable react/prop-types */

import {
  Grid,
  Card,
  Stack,
  Typography,
  Avatar,
  useTheme,
  alpha,
  Zoom,
  Chip
} from '@mui/material';
import { 
  Inventory2, 
  LocalShipping, 
  AttachMoney, 
  BarChart, 
  TrendingUp, 
  TrendingDown 
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const DashboardStatsCards = ({ 
  inventory, 
  lowStockItems, 
  totalValue, 
  stockTrend 
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  const formatValue = (value) => {
    if (value >= 1e7) {
      return `${(value / 1e7).toFixed(2)}Cr`;
    } else if (value >= 1e5) {
      return `${(value / 1e5).toFixed(2)}L`;
    }
    return value.toLocaleString('en-IN', { style: 'currency', currency: 'INR' });
  };

  const statsConfig = [
    {
      icon: <Inventory2 />,
      title: t('dashboard.totalProducts'),
      value: inventory.length,
      subtitle: `Across ${new Set(inventory.map(item => item.category)).size} categories`,
      color: 'primary',
      gradient: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
      change: '+12%'
    },
    {
      icon: <LocalShipping />,
      title: t('dashboard.lowStockItems'),
      value: lowStockItems.length,
      subtitle: 'Require immediate attention',
      color: 'warning',
      gradient: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
      change: '-8%'
    },
    {
      icon: <AttachMoney />,
      title: t('dashboard.totalStockValue'),
      value: formatValue(totalValue),
      subtitle: `${Math.abs(stockTrend)}% from last month`,
      color: 'success',
      gradient: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
      trend: stockTrend,
      change: `+${stockTrend}%`
    },
    {
      icon: <BarChart />,
      title: t('dashboard.averageStockLevel'),
      value: inventory.length > 0 ? Math.round(inventory.reduce((acc, item) => acc + item.stock, 0) / inventory.length) : 0,
      subtitle: 'Units per product',
      color: 'info',
      gradient: 'linear-gradient(135deg, #00bcd4 0%, #0097a7 100%)',
      change: '+15%'
    }
  ];

  return (
    <Grid container spacing={3}>
      {statsConfig.map((stat, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <Zoom in timeout={300 + index * 100}>
            <Card
              elevation={0}
              sx={{
                background: 'rgba(255, 255, 255, 0.98)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: 4,
                overflow: 'hidden',
                position: 'relative',
                boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 40px rgba(25, 118, 210, 0.2)',
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: 4,
                  background: stat.gradient,
                },
              }}
            >
              <Stack spacing={2} sx={{ p: 3 }}>
                <Stack direction="row" alignItems="center" justifyContent="space-between">
                  <Avatar
                    sx={{
                      background: stat.gradient,
                      color: 'white',
                      width: 48,
                      height: 48,
                      boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                  <Chip
                    label={stat.change}
                    size="small"
                    sx={{
                      backgroundColor: stat.change.startsWith('+') ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
                      color: stat.change.startsWith('+') ? '#4caf50' : '#f44336',
                      fontWeight: 600,
                      fontSize: '0.75rem',
                    }}
                  />
                </Stack>

                <Stack spacing={0.5}>
                  <Typography
                    variant="overline"
                    color="text.secondary"
                    fontWeight={600}
                    sx={{ fontSize: '0.75rem' }}
                  >
                    {stat.title}
                  </Typography>
                  <Typography
                    variant="h4"
                    fontWeight={700}
                    color="#1976d2"
                    sx={{ fontSize: '1.75rem' }}
                  >
                    {stat.value}
                  </Typography>
                  {stat.trend !== undefined ? (
                    <Stack direction="row" alignItems="center" spacing={1}>
                      {stat.trend >= 0 ?
                        <TrendingUp color="success" sx={{ fontSize: 16 }} /> :
                        <TrendingDown color="error" sx={{ fontSize: 16 }} />
                      }
                      <Typography variant="caption" color="text.secondary" fontWeight={500}>
                        {stat.subtitle}
                      </Typography>
                    </Stack>
                  ) : (
                    <Typography variant="caption" color="text.secondary" fontWeight={500}>
                      {stat.subtitle}
                    </Typography>
                  )}
                </Stack>
              </Stack>
            </Card>
          </Zoom>
        </Grid>
      ))}
    </Grid>
  );
};

export default DashboardStatsCards;