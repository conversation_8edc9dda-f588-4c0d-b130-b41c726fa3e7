import React from 'react';
import { Box, Typography, Paper, Chip } from '@mui/material';
import { useLocation } from 'react-router-dom';
import Cookies from 'js-cookie';

const DebugInfo = () => {
  const location = useLocation();
  const userRole = Cookies.get('userRole');
  const jwtToken = Cookies.get('jwtToken');
  const email = Cookies.get('email');

  if (process.env.NODE_ENV === 'production') {
    return null; // Don't show in production
  }

  return (
    <Paper 
      sx={{ 
        position: 'fixed', 
        top: 10, 
        right: 10, 
        p: 2, 
        zIndex: 9999,
        maxWidth: 300,
        backgroundColor: 'rgba(0,0,0,0.8)',
        color: 'white'
      }}
    >
      <Typography variant="h6" gutterBottom>Debug Info</Typography>
      
      <Box sx={{ mb: 1 }}>
        <Typography variant="caption">Current Path:</Typography>
        <Chip label={location.pathname} size="small" sx={{ ml: 1 }} />
      </Box>
      
      <Box sx={{ mb: 1 }}>
        <Typography variant="caption">User Role:</Typography>
        <Chip 
          label={userRole || 'Not set'} 
          size="small" 
          color={userRole ? 'success' : 'error'}
          sx={{ ml: 1 }} 
        />
      </Box>
      
      <Box sx={{ mb: 1 }}>
        <Typography variant="caption">JWT Token:</Typography>
        <Chip 
          label={jwtToken ? 'Present' : 'Missing'} 
          size="small" 
          color={jwtToken ? 'success' : 'error'}
          sx={{ ml: 1 }} 
        />
      </Box>
      
      <Box sx={{ mb: 1 }}>
        <Typography variant="caption">Email:</Typography>
        <Chip 
          label={email || 'Not set'} 
          size="small" 
          color={email ? 'success' : 'error'}
          sx={{ ml: 1 }} 
        />
      </Box>
      
      <Box sx={{ mb: 1 }}>
        <Typography variant="caption">Expected Path:</Typography>
        <Chip 
          label={userRole === 'superadmin' || userRole === 'admin' ? '/manager' : '/user'} 
          size="small" 
          sx={{ ml: 1 }} 
        />
      </Box>
    </Paper>
  );
};

export default DebugInfo;
