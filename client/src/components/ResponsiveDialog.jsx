import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  useTheme,
  useMediaQuery,
  Slide,
  Paper,
  Box
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { useResponsive } from '../hooks/useResponsive';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

/**
 * ResponsiveDialog component that adapts to different screen sizes
 * - Mobile: Full screen dialog
 * - Tablet: Large dialog with margins
 * - Desktop: Standard dialog
 */
const ResponsiveDialog = ({
  open,
  onClose,
  title,
  children,
  actions,
  maxWidth = 'sm',
  fullWidth = true,
  disableEscapeKeyDown = false,
  disableBackdropClick = false,
  ...dialogProps
}) => {
  const theme = useTheme();
  const { isMobile, isTablet } = useResponsive();

  const handleClose = (event, reason) => {
    if (disableBackdropClick && reason === 'backdropClick') {
      return;
    }
    if (disableEscapeKeyDown && reason === 'escapeKeyDown') {
      return;
    }
    onClose(event, reason);
  };

  // Mobile: Full screen dialog
  if (isMobile) {
    return (
      <Dialog
        open={open}
        onClose={handleClose}
        fullScreen
        TransitionComponent={Transition}
        sx={{
          '& .MuiDialog-paper': {
            margin: 0,
            borderRadius: 0,
            maxHeight: '100vh',
            height: '100vh'
          }
        }}
        {...dialogProps}
      >
        {title && (
          <DialogTitle
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              py: 2,
              px: 2,
              borderBottom: `1px solid ${theme.palette.divider}`,
              bgcolor: theme.palette.background.paper,
              position: 'sticky',
              top: 0,
              zIndex: 1
            }}
          >
            <Box sx={{ fontSize: '1.125rem', fontWeight: 600 }}>
              {title}
            </Box>
            <IconButton
              edge="end"
              color="inherit"
              onClick={onClose}
              aria-label="close"
              sx={{
                minWidth: '48px',
                minHeight: '48px'
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
        )}
        
        <DialogContent
          sx={{
            flex: 1,
            p: 2,
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px'
            },
            '&::-webkit-scrollbar-track': {
              background: theme.palette.grey[100]
            },
            '&::-webkit-scrollbar-thumb': {
              background: theme.palette.grey[400],
              borderRadius: '4px'
            }
          }}
        >
          {children}
        </DialogContent>
        
        {actions && (
          <DialogActions
            sx={{
              p: 2,
              borderTop: `1px solid ${theme.palette.divider}`,
              bgcolor: theme.palette.background.paper,
              position: 'sticky',
              bottom: 0,
              gap: 1,
              flexDirection: 'column',
              '& > :not(:first-of-type)': {
                ml: 0
              }
            }}
          >
            {actions}
          </DialogActions>
        )}
      </Dialog>
    );
  }

  // Tablet and Desktop: Standard dialog with responsive sizing
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 3,
          margin: isTablet ? 2 : 4,
          maxHeight: isTablet ? 'calc(100vh - 32px)' : 'calc(100vh - 64px)',
          width: isTablet ? 'calc(100vw - 32px)' : 'auto'
        }
      }}
      {...dialogProps}
    >
      {title && (
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            py: isTablet ? 2 : 3,
            px: isTablet ? 2 : 3,
            borderBottom: `1px solid ${theme.palette.divider}`,
            fontSize: isTablet ? '1.125rem' : '1.25rem',
            fontWeight: 600
          }}
        >
          <Box>{title}</Box>
          <IconButton
            edge="end"
            color="inherit"
            onClick={onClose}
            aria-label="close"
            size={isTablet ? 'medium' : 'large'}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
      )}
      
      <DialogContent
        sx={{
          p: isTablet ? 2 : 3,
          overflow: 'auto'
        }}
      >
        {children}
      </DialogContent>
      
      {actions && (
        <DialogActions
          sx={{
            p: isTablet ? 2 : 3,
            borderTop: `1px solid ${theme.palette.divider}`,
            gap: 1,
            flexDirection: isTablet ? 'column-reverse' : 'row',
            '& > :not(:first-of-type)': {
              ml: isTablet ? 0 : 1
            }
          }}
        >
          {actions}
        </DialogActions>
      )}
    </Dialog>
  );
};

export default ResponsiveDialog;
