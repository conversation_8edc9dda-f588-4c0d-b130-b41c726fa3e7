import React from 'react';
import {
  Box,
  Container,
  Stack,
  Grid,
  useTheme
} from '@mui/material';
import { useResponsive, useResponsiveContainer } from '../hooks/useResponsive';

/**
 * ResponsiveLayout component that provides consistent responsive layouts
 */
export const ResponsiveContainer = ({ 
  children, 
  maxWidth, 
  padding,
  disableGutters = false,
  ...props 
}) => {
  const { maxWidth: defaultMaxWidth, padding: defaultPadding } = useResponsiveContainer();
  
  return (
    <Container
      maxWidth={maxWidth || defaultMaxWidth}
      disableGutters={disableGutters}
      sx={{
        py: padding || defaultPadding,
        px: disableGutters ? 0 : undefined,
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Container>
  );
};

/**
 * ResponsiveStack component with responsive spacing and direction
 */
export const ResponsiveStack = ({ 
  children, 
  spacing,
  direction,
  mobileDirection = 'column',
  tabletDirection,
  desktopDirection = 'row',
  ...props 
}) => {
  const { isMobile, isTablet, getSpacing } = useResponsive();
  
  const getDirection = () => {
    if (isMobile) return mobileDirection;
    if (isTablet) return tabletDirection || mobileDirection;
    return desktopDirection;
  };
  
  const getStackSpacing = () => {
    if (typeof spacing === 'object') {
      return spacing;
    }
    return getSpacing(spacing || 1, spacing || 2, spacing || 3);
  };
  
  return (
    <Stack
      direction={direction || getDirection()}
      spacing={getStackSpacing()}
      {...props}
    >
      {children}
    </Stack>
  );
};

/**
 * ResponsiveGrid component with responsive breakpoints
 */
export const ResponsiveGrid = ({ 
  children, 
  spacing,
  columns = { mobile: 1, tablet: 2, desktop: 3 },
  ...props 
}) => {
  const { isMobile, isTablet, getSpacing } = useResponsive();
  
  const getGridSpacing = () => {
    if (typeof spacing === 'object') {
      return spacing;
    }
    return getSpacing(spacing || 1, spacing || 2, spacing || 3);
  };
  
  return (
    <Grid
      container
      spacing={getGridSpacing()}
      {...props}
    >
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return child;
        
        // Calculate grid size based on columns configuration
        const getGridSize = () => {
          if (isMobile) return { xs: 12 / columns.mobile };
          if (isTablet) return { xs: 12, sm: 12 / columns.tablet };
          return { xs: 12, sm: 6, md: 12 / columns.desktop };
        };
        
        return (
          <Grid item {...getGridSize()} key={index}>
            {child}
          </Grid>
        );
      })}
    </Grid>
  );
};

/**
 * ResponsiveBox component with responsive padding and margins
 */
export const ResponsiveBox = ({ 
  children, 
  p, 
  m, 
  px, 
  py, 
  mx, 
  my,
  ...props 
}) => {
  const { getSpacing } = useResponsive();
  
  const getResponsiveSpacing = (value) => {
    if (typeof value === 'object') return value;
    if (typeof value === 'number') {
      return {
        xs: getSpacing(value),
        sm: getSpacing(value + 0.5),
        md: getSpacing(value + 1)
      };
    }
    return value;
  };
  
  return (
    <Box
      sx={{
        p: p ? getResponsiveSpacing(p) : undefined,
        m: m ? getResponsiveSpacing(m) : undefined,
        px: px ? getResponsiveSpacing(px) : undefined,
        py: py ? getResponsiveSpacing(py) : undefined,
        mx: mx ? getResponsiveSpacing(mx) : undefined,
        my: my ? getResponsiveSpacing(my) : undefined,
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

/**
 * ResponsiveSection component for page sections
 */
export const ResponsiveSection = ({ 
  children, 
  title, 
  subtitle,
  spacing = 3,
  ...props 
}) => {
  const theme = useTheme();
  const { isMobile, getSpacing } = useResponsive();
  
  return (
    <Box
      component="section"
      sx={{
        py: getSpacing(spacing),
        ...props.sx
      }}
      {...props}
    >
      {(title || subtitle) && (
        <Box sx={{ mb: getSpacing(spacing - 1) }}>
          {title && (
            <Box
              component="h2"
              sx={{
                fontSize: isMobile ? '1.5rem' : '2rem',
                fontWeight: 700,
                color: 'primary.main',
                mb: subtitle ? 1 : 0,
                mt: 0
              }}
            >
              {title}
            </Box>
          )}
          {subtitle && (
            <Box
              component="p"
              sx={{
                fontSize: isMobile ? '0.875rem' : '1rem',
                color: 'text.secondary',
                mt: 0,
                mb: 0
              }}
            >
              {subtitle}
            </Box>
          )}
        </Box>
      )}
      {children}
    </Box>
  );
};

/**
 * ResponsiveCard component with responsive padding and spacing
 */
export const ResponsiveCard = ({ 
  children, 
  padding,
  elevation = 0,
  ...props 
}) => {
  const theme = useTheme();
  const { isMobile, getSpacing } = useResponsive();
  
  return (
    <Box
      sx={{
        bgcolor: 'background.paper',
        borderRadius: isMobile ? 2 : 3,
        border: `1px solid ${theme.palette.divider}`,
        p: padding ? getSpacing(padding) : getSpacing(isMobile ? 1.5 : 2),
        boxShadow: elevation > 0 ? theme.shadows[elevation] : 'none',
        ...props.sx
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

/**
 * ResponsiveButtonGroup component for action buttons
 */
export const ResponsiveButtonGroup = ({ 
  children, 
  spacing = 1,
  fullWidthOnMobile = true,
  ...props 
}) => {
  const { isMobile } = useResponsive();
  
  return (
    <Stack
      direction={isMobile ? 'column' : 'row'}
      spacing={spacing}
      sx={{
        width: isMobile && fullWidthOnMobile ? '100%' : 'auto',
        ...props.sx
      }}
      {...props}
    >
      {React.Children.map(children, (child) => {
        if (!React.isValidElement(child)) return child;
        
        // Add fullWidth prop to buttons on mobile if enabled
        if (isMobile && fullWidthOnMobile && child.type?.name?.includes('Button')) {
          return React.cloneElement(child, { fullWidth: true });
        }
        
        return child;
      })}
    </Stack>
  );
};

export default {
  Container: ResponsiveContainer,
  Stack: ResponsiveStack,
  Grid: ResponsiveGrid,
  Box: ResponsiveBox,
  Section: ResponsiveSection,
  Card: ResponsiveCard,
  ButtonGroup: ResponsiveButtonGroup
};
