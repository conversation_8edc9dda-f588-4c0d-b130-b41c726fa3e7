/* Reset and base styles */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
  /* Prevent zoom on iOS when focusing inputs */
  -webkit-text-size-adjust: 100%;
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  /* Better font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Prevent text selection on UI elements */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection in content areas */
p, span, div[contenteditable], input, textarea {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
}

/* Better touch targets for mobile */
@media (max-width: 768px) {
  button,
  a,
  [role="button"],
  .MuiIconButton-root,
  .MuiButton-root {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Extra small devices (phones, 480px and down) */
@media (max-width: 480px) {
  button,
  a,
  [role="button"],
  .MuiIconButton-root,
  .MuiButton-root {
    min-height: 48px;
    min-width: 48px;
  }
}

/* Improve scrolling on mobile */
@media (max-width: 768px) {
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* Hide scrollbars but keep functionality */
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Focus styles for accessibility */
button:focus-visible,
a:focus-visible,
[role="button"]:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Loading states */
.loading-skeleton {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive utilities */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(env(safe-area-inset-top), 16px);
  }

  .safe-area-inset-bottom {
    padding-bottom: max(env(safe-area-inset-bottom), 16px);
  }

  .safe-area-inset-left {
    padding-left: max(env(safe-area-inset-left), 16px);
  }

  .safe-area-inset-right {
    padding-right: max(env(safe-area-inset-right), 16px);
  }
}
