import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ThemeProvider, CssBaseline } from '@mui/material'
import './index.css'
import App from './App.jsx'
import { logEnvironmentInfo } from './utils/environmentUtils'
import { config } from './config'
import { createResponsiveTheme } from './theme/responsiveTheme'
import './i18n'

// Log environment information on app startup
logEnvironmentInfo();

// Dynamically set preconnect based on environment
const setPreconnect = () => {
  const apiUrl = config.apiUrl;
  if (apiUrl) {
    const linkElement = document.createElement('link');
    linkElement.rel = 'preconnect';
    linkElement.href = apiUrl;
    document.head.appendChild(linkElement);
    console.log(`Preconnect set to: ${apiUrl}`);
  }
};

// Setup preconnect
setPreconnect();

// Create responsive theme
const theme = createResponsiveTheme('light');

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <App />
    </ThemeProvider>
  </StrictMode>,
)
