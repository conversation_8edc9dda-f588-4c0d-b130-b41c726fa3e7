/* eslint-disable react/prop-types */
import { Navigate, useLocation } from 'react-router-dom';
import NotFoundPage from '../pages/NotFoundPage';
import Cookies from 'js-cookie';

const ProtectedRoute = ({ children, allowedRoles }) => {
  const userRole = Cookies.get('userRole');
  const location = useLocation();

  // Debug logging
  console.log('ProtectedRoute Debug:', {
    userRole,
    pathname: location.pathname,
    allowedRoles
  });

  // If not authenticated, redirect to login
  if (!userRole) {
    console.log('No userRole found, redirecting to login');
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Determine the appropriate path prefix based on role
  const getPathPrefix = (role) => {
    if (role === 'superadmin' || role === 'admin') {
      return '/manager';
    } else {
      return '/user';
    }
  };

  // Check if the current path prefix matches the user's role
  const expectedPrefix = getPathPrefix(userRole);
  const currentPrefix = '/' + location.pathname.split('/')[1];

  console.log('Path check:', {
    expectedPrefix,
    currentPrefix,
    matches: currentPrefix === expectedPrefix
  });

  // If they're trying to access a path that doesn't match their role, show not found
  if (currentPrefix !== expectedPrefix) {
    console.log('Path prefix mismatch, showing 404');
    return <NotFoundPage />;
  }

  // Check if the user's role is allowed for this route
  if (!allowedRoles.includes(userRole)) {
    console.log('Role not allowed, showing 404');
    return <NotFoundPage />;
  }

  console.log('Access granted');
  return children;
};

export default ProtectedRoute;