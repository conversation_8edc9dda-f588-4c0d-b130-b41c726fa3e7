/* eslint-disable react/prop-types */
import { Navigate, useLocation } from 'react-router-dom';
import NotFoundPage from '../pages/NotFoundPage';
import Cookies from 'js-cookie';

const ProtectedRoute = ({ children, allowedRoles }) => {
  const userRole = Cookies.get('userRole');
  const location = useLocation();

  // If not authenticated, redirect to login
  if (!userRole) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if the user's role is allowed for this route
  if (!allowedRoles.includes(userRole)) {
    return <NotFoundPage />;
  }

  return children;
};

export default ProtectedRoute;