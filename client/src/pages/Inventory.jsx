/* eslint-disable no-unused-vars */
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Card,
  CardContent,
  useTheme,
  alpha,
  LinearProgress,
  Paper,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  NotificationsActive,
  Download as DownloadIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import ProductDialog from '../components/ProductDialog';
import ResponsiveTable from '../components/ResponsiveTable';
import { ResponsiveContainer, ResponsiveStack, ResponsiveButtonGroup } from '../components/ResponsiveLayout';
import { useResponsive, useResponsiveContainer } from '../hooks/useResponsive';
import { getApiUrl } from '../config';
import { useTranslation } from 'react-i18next';

const Inventory = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { isMobile, isTablet, isTouchDevice } = useResponsive();
  const { maxWidth, padding } = useResponsiveContainer();

  // State management
  const [inventory, setInventory] = useState([]);
  const [filteredInventory, setFilteredInventory] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState('all');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = isMobile ? 3 : 5;
  const userRole = localStorage.getItem('userRole');
  const isAdmin = userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper';
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [stores, setStores] = useState([]);

  useEffect(() => {
    fetchInventory();
    fetchStores();
  }, []);

  const fetchStores = async () => {
    try {
      const response = await fetch(getApiUrl('api/store'));
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setStores(data);
    } catch (error) {
      console.error('Error fetching stores:', error);
    }
  };

  const fetchInventory = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('api/product/'));
      const result = await response.json();
      
      if (result.success) {
        const products = result.data.map(product => ({
          id: product._id,
          name: product.name,
          sku: product.sku,
          stock: product.stock,
          lowStockThreshold: product.lowStockThreshold,
          price: product.price,
          category: product.category,
          manufacturing_date: new Date(product.manufacturing_date),
          expiry_date: new Date(product.expiry_date),
          store_id: product.store_id
        }));
        setInventory(products);
        setFilteredInventory(products);
      } else {
        console.error('Error fetching inventory:', result.message);
      }
    } catch (error) {
      console.error('Error fetching inventory:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const filtered = inventory.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          item.sku.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = category === 'all' || item.category === category;
      return matchesSearch && matchesCategory;
    });
    setFilteredInventory(filtered);
  }, [searchTerm, category, inventory]);

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setOpenDialog(true);
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setOpenDialog(true);
  };

  const handleDeleteProduct = async (product) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        const response = await fetch(getApiUrl(`api/product/delete/${product.sku}`), {
          method: 'DELETE'
        });
        
        if (response.ok) {
          const updatedInventory = inventory.filter(item => item.sku !== product.sku);
          setInventory(updatedInventory);
          setSnackbarMessage('Product deleted successfully!');
          setSnackbarSeverity('success');
        } else {
          console.error('Failed to delete product');
          setSnackbarMessage('Failed to delete product.');
          setSnackbarSeverity('error');
        }
      } catch (error) {
        console.error('Error deleting product:', error);
        setSnackbarMessage('Error deleting product.');
        setSnackbarSeverity('error');
      }
      setSnackbarOpen(true);
    }
  };

  const handleSaveProduct = async (formData) => {
    if (selectedProduct) {
      try {
        const response = await fetch(getApiUrl(`api/product/update/${selectedProduct.sku}`), {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData)
        });

        if (response.ok) {
          const result = await response.json();
          const updatedProduct = result.product;
          
          const updatedInventory = inventory.map(item => 
            item.sku === selectedProduct.sku ? {
              id: updatedProduct._id,
              name: updatedProduct.name,
              sku: updatedProduct.sku,
              stock: updatedProduct.stock,
              lowStockThreshold: updatedProduct.lowStockThreshold,
              price: updatedProduct.price,
              category: updatedProduct.category,
              manufacturing_date: new Date(updatedProduct.manufacturing_date),
              expiry_date: new Date(updatedProduct.expiry_date)
            } : item
          );
          setInventory(updatedInventory);
          setSnackbarMessage('Product updated successfully!');
          setSnackbarSeverity('success');
        }
      } catch (error) {
        console.error('Error updating product:', error);
        setSnackbarMessage('Error updating product.');
        setSnackbarSeverity('error');
      }
    } else {
      try {
        const response = await fetch(getApiUrl('api/product/add'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData)
        });

        if (response.ok) {
          const result = await response.json();
          const newProduct = {
            id: result.product._id,
            ...formData,
            manufacturing_date: new Date(formData.manufacturing_date),
            expiry_date: new Date(formData.expiry_date)
          };
          setInventory([...inventory, newProduct]);
          setSnackbarMessage('Product added successfully!');
          setSnackbarSeverity('success');
        }
      } catch (error) {
        console.error('Error adding product:', error);
        setSnackbarMessage('Error adding product.');
        setSnackbarSeverity('error');
      }
    }
    setOpenDialog(false);
    setSnackbarOpen(true);
  };

  const downloadProductList = () => {
    // Convert inventory data to CSV format
    const headers = ['Product Name', 'SKU', 'Category', 'Stock Level', 'Price', 'Manufacturing Date', 'Expiry Date'];
    const csvData = [
      headers,
      ...filteredInventory.map(product => [
        product.name,
        product.sku,
        product.category,
        product.stock,
        product.price,
        product.manufacturing_date.toLocaleDateString(),
        product.expiry_date.toLocaleDateString()
      ])
    ];
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'product_inventory.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleNextPage = () => {
    setCurrentPage(prevPage => prevPage + 1);
  };

  const handlePrevPage = () => {
    setCurrentPage(prevPage => Math.max(prevPage - 1, 0));
  };

  const totalPages = Math.ceil(filteredInventory.length / itemsPerPage);
  const paginatedInventory = filteredInventory.slice(currentPage * itemsPerPage, (currentPage + 1) * itemsPerPage);

  return (
    <ResponsiveContainer maxWidth={maxWidth} padding={padding}>
      <Box sx={{
        bgcolor: alpha(theme.palette.primary.main, 0.02),
        minHeight: '100vh',
        borderRadius: isMobile ? 0 : 2
      }}>
        <Paper
          elevation={0}
          sx={{
            p: isMobile ? 2 : 4,
            borderRadius: isMobile ? 0 : 4,
            bgcolor: 'white',
            minHeight: '100vh'
          }}
        >
          <Box sx={{
            mb: isMobile ? 3 : 4,
            display: 'flex',
            flexDirection: isMobile ? 'column' : 'row',
            justifyContent: 'space-between',
            alignItems: isMobile ? 'flex-start' : 'center',
            gap: isMobile ? 2 : 0
          }}>
            <Typography
              variant={isMobile ? "h5" : "h4"}
              fontWeight="bold"
              color="primary"
              sx={{ fontSize: isMobile ? '1.5rem' : '2rem' }}
            >
              {t('inventory.title')}
            </Typography>

            <ResponsiveButtonGroup spacing={isMobile ? 1 : 2} fullWidthOnMobile={isMobile}>
              <Button
                variant="outlined"
                startIcon={!isMobile && <DownloadIcon />}
                onClick={downloadProductList}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  px: isMobile ? 2 : 3,
                  py: isMobile ? 1.5 : 1,
                  minHeight: isTouchDevice ? '44px' : '40px',
                  fontSize: isMobile ? '0.875rem' : '1rem'
                }}
              >
                {isMobile && <DownloadIcon sx={{ mr: 1, fontSize: '1rem' }} />}
                {t('actions.exportList')}
              </Button>

              {isAdmin && (
                <Button
                  variant="contained"
                  startIcon={!isMobile && <AddIcon />}
                  onClick={handleAddProduct}
                  sx={{
                    borderRadius: 2,
                    textTransform: 'none',
                    px: isMobile ? 2 : 3,
                    py: isMobile ? 1.5 : 1,
                    minHeight: isTouchDevice ? '44px' : '40px',
                    fontSize: isMobile ? '0.875rem' : '1rem',
                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
                  }}
                >
                  {isMobile && <AddIcon sx={{ mr: 1, fontSize: '1rem' }} />}
                  {t('actions.addProduct')}
                </Button>
              )}
            </ResponsiveButtonGroup>
          </Box>

        <ResponsiveStack
          spacing={isMobile ? 2 : 2}
          direction={isMobile ? 'column' : 'row'}
          sx={{ mb: isMobile ? 3 : 4, alignItems: isMobile ? 'stretch' : 'center' }}
        >
          <TextField
            size={isMobile ? "medium" : "small"}
            placeholder={t('search.placeholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
            sx={{
              width: isMobile ? '100%' : 300,
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                bgcolor: 'background.paper',
                minHeight: isTouchDevice ? '48px' : '40px',
                '&:hover': {
                  '& > fieldset': {
                    borderColor: theme.palette.primary.main,
                  }
                }
              }
            }}
          />
          <FormControl
            size={isMobile ? "medium" : "small"}
            sx={{ width: isMobile ? '100%' : 200 }}
          >
            <InputLabel>{t('filter.category')}</InputLabel>
            <Select
              value={category}
              label={t('filter.category')}
              onChange={(e) => setCategory(e.target.value)}
              sx={{
                borderRadius: 2,
                bgcolor: 'background.paper',
                minHeight: isTouchDevice ? '48px' : '40px',
                '&:hover': {
                  '& > fieldset': {
                    borderColor: theme.palette.primary.main,
                  }
                }
              }}
            >
              <MenuItem value="all">{t('filter.allCategories')}</MenuItem>
              <MenuItem value="Laying">{t('categories.laying')}</MenuItem>
              <MenuItem value="ElectricalMaterial">{t('categories.electricalMaterial')}</MenuItem>
              <MenuItem value="StructureMaterial">{t('categories.structureMaterial')}</MenuItem>
              <MenuItem value="PlumbingMaterials">{t('categories.plumbingMaterials')}</MenuItem>
              <MenuItem value="ToolsAndEquipment">{t('categories.toolsAndEquipment')}</MenuItem>
            </Select>
          </FormControl>
          <IconButton
            onClick={fetchInventory}
            sx={{
              bgcolor: alpha(theme.palette.primary.main, 0.1),
              minWidth: isTouchDevice ? '48px' : '40px',
              minHeight: isTouchDevice ? '48px' : '40px',
              alignSelf: isMobile ? 'center' : 'auto',
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.2)
              }
            }}
          >
            <RefreshIcon color="primary" />
          </IconButton>
        </ResponsiveStack>

        {/* Define table columns for responsive table */}
        {(() => {
          const columns = [
            {
              field: 'name',
              headerName: t('table.productName'),
              primary: true,
              renderCell: (row) => (
                <Typography variant="body2" fontWeight={500}>
                  {row.name}
                </Typography>
              )
            },
            {
              field: 'store',
              headerName: t('table.store'),
              hideOnTablet: isMobile,
              renderCell: (row) => {
                const productStore = stores.find(store => store._id === row.store_id);
                return (
                  <Typography variant="body2" color="text.secondary">
                    {productStore ? `${productStore.storeName} (${productStore.storeLocation})` : 'N/A'}
                  </Typography>
                );
              }
            },
            {
              field: 'sku',
              headerName: t('table.sku'),
              hideOnTablet: isMobile,
              renderCell: (row) => (
                <Typography variant="body2" color="text.secondary">
                  {row.sku}
                </Typography>
              )
            },
            {
              field: 'category',
              headerName: t('table.category'),
              renderCell: (row) => (
                <Chip
                  label={row.category}
                  size="small"
                  sx={{
                    bgcolor: row.category === 'Electronics' ?
                      alpha(theme.palette.primary.main, 0.1) :
                      alpha(theme.palette.secondary.main, 0.1),
                    color: row.category === 'Electronics' ?
                      theme.palette.primary.main :
                      theme.palette.secondary.main,
                    fontWeight: 500,
                    fontSize: isMobile ? '0.75rem' : '0.8125rem'
                  }}
                />
              )
            },
            {
              field: 'stock',
              headerName: t('table.stockLevel'),
              renderCell: (row) => (
                <Chip
                  label={`${row.stock} ${row.stock < row.lowStockThreshold ? `(${t('status.lowStock')})` : `(${t('status.inStock')})`}`}
                  size="small"
                  sx={{
                    bgcolor: row.stock < row.lowStockThreshold ?
                      alpha(theme.palette.error.main, 0.1) :
                      alpha(theme.palette.success.main, 0.1),
                    color: row.stock < row.lowStockThreshold ?
                      theme.palette.error.main :
                      theme.palette.success.main,
                    fontWeight: 500,
                    fontSize: isMobile ? '0.75rem' : '0.8125rem'
                  }}
                />
              )
            },
            {
              field: 'price',
              headerName: t('table.price'),
              hideOnTablet: isMobile,
              renderCell: (row) => (
                <Typography variant="body2" fontWeight={600}>
                  ₹{Math.floor(row.price)}
                </Typography>
              )
            },
            {
              field: 'manufacturing_date',
              headerName: t('table.mfgDate'),
              hideOnTablet: true,
              renderCell: (row) => (
                <Typography variant="body2" color="text.secondary">
                  {row.manufacturing_date.toLocaleDateString()}
                </Typography>
              )
            },
            {
              field: 'expiry_date',
              headerName: t('table.expiryDate'),
              renderCell: (row) => (
                <Chip
                  label={row.expiry_date.toLocaleDateString()}
                  size="small"
                  sx={{
                    bgcolor: row.expiry_date < new Date() ?
                      alpha(theme.palette.error.main, 0.1) :
                      alpha(theme.palette.success.main, 0.1),
                    color: row.expiry_date < new Date() ?
                      theme.palette.error.main :
                      theme.palette.success.main,
                    fontWeight: 500,
                    fontSize: isMobile ? '0.75rem' : '0.8125rem'
                  }}
                />
              )
            }
          ];

          // Add actions column if admin
          if (isAdmin) {
            columns.push({
              field: 'actions',
              headerName: t('table.actions'),
              renderCell: () => null // Will be handled by renderActions
            });
          }

          return columns;
        })()}

        <ResponsiveTable
          columns={(() => {
            const columns = [
              {
                field: 'name',
                headerName: t('table.productName'),
                primary: true,
                renderCell: (row) => (
                  <Typography variant="body2" fontWeight={500}>
                    {row.name}
                  </Typography>
                )
              },
              {
                field: 'store',
                headerName: t('table.store'),
                hideOnTablet: isMobile,
                renderCell: (row) => {
                  const productStore = stores.find(store => store._id === row.store_id);
                  return (
                    <Typography variant="body2" color="text.secondary">
                      {productStore ? `${productStore.storeName} (${productStore.storeLocation})` : 'N/A'}
                    </Typography>
                  );
                }
              },
              {
                field: 'sku',
                headerName: t('table.sku'),
                hideOnTablet: isMobile,
                renderCell: (row) => (
                  <Typography variant="body2" color="text.secondary">
                    {row.sku}
                  </Typography>
                )
              },
              {
                field: 'category',
                headerName: t('table.category'),
                renderCell: (row) => (
                  <Chip
                    label={row.category}
                    size="small"
                    sx={{
                      bgcolor: row.category === 'Electronics' ?
                        alpha(theme.palette.primary.main, 0.1) :
                        alpha(theme.palette.secondary.main, 0.1),
                      color: row.category === 'Electronics' ?
                        theme.palette.primary.main :
                        theme.palette.secondary.main,
                      fontWeight: 500,
                      fontSize: isMobile ? '0.75rem' : '0.8125rem'
                    }}
                  />
                )
              },
              {
                field: 'stock',
                headerName: t('table.stockLevel'),
                renderCell: (row) => (
                  <Chip
                    label={`${row.stock} ${row.stock < row.lowStockThreshold ? `(${t('status.lowStock')})` : `(${t('status.inStock')})`}`}
                    size="small"
                    sx={{
                      bgcolor: row.stock < row.lowStockThreshold ?
                        alpha(theme.palette.error.main, 0.1) :
                        alpha(theme.palette.success.main, 0.1),
                      color: row.stock < row.lowStockThreshold ?
                        theme.palette.error.main :
                        theme.palette.success.main,
                      fontWeight: 500,
                      fontSize: isMobile ? '0.75rem' : '0.8125rem'
                    }}
                  />
                )
              },
              {
                field: 'price',
                headerName: t('table.price'),
                hideOnTablet: isMobile,
                renderCell: (row) => (
                  <Typography variant="body2" fontWeight={600}>
                    ₹{Math.floor(row.price)}
                  </Typography>
                )
              },
              {
                field: 'manufacturing_date',
                headerName: t('table.mfgDate'),
                hideOnTablet: true,
                renderCell: (row) => (
                  <Typography variant="body2" color="text.secondary">
                    {row.manufacturing_date.toLocaleDateString()}
                  </Typography>
                )
              },
              {
                field: 'expiry_date',
                headerName: t('table.expiryDate'),
                renderCell: (row) => (
                  <Chip
                    label={row.expiry_date.toLocaleDateString()}
                    size="small"
                    sx={{
                      bgcolor: row.expiry_date < new Date() ?
                        alpha(theme.palette.error.main, 0.1) :
                        alpha(theme.palette.success.main, 0.1),
                      color: row.expiry_date < new Date() ?
                        theme.palette.error.main :
                        theme.palette.success.main,
                      fontWeight: 500,
                      fontSize: isMobile ? '0.75rem' : '0.8125rem'
                    }}
                  />
                )
              },
              ...(isAdmin ? [{
                field: 'actions',
                headerName: t('table.actions'),
                renderCell: () => null
              }] : [])
            ];
            return columns;
          })()}
          data={paginatedInventory}
          renderActions={isAdmin ? (row) => (
            <ResponsiveStack direction={isMobile ? "column" : "row"} spacing={1}>
              <Button
                variant="outlined"
                size="small"
                startIcon={!isMobile && <EditIcon />}
                onClick={() => handleEditProduct(row)}
                fullWidth={isMobile}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  minHeight: isTouchDevice ? '40px' : '32px',
                  fontSize: isMobile ? '0.8125rem' : '0.875rem',
                  color: theme.palette.primary.main,
                  borderColor: theme.palette.primary.main,
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    borderColor: theme.palette.primary.main
                  }
                }}
              >
                {isMobile && <EditIcon sx={{ mr: 1, fontSize: '1rem' }} />}
                {t('actions.edit')}
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={!isMobile && <DeleteIcon />}
                onClick={() => handleDeleteProduct(row)}
                fullWidth={isMobile}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  minHeight: isTouchDevice ? '40px' : '32px',
                  fontSize: isMobile ? '0.8125rem' : '0.875rem',
                  color: theme.palette.error.main,
                  borderColor: theme.palette.error.main,
                  '&:hover': {
                    bgcolor: alpha(theme.palette.error.main, 0.1),
                    borderColor: theme.palette.error.main
                  }
                }}
              >
                {isMobile && <DeleteIcon sx={{ mr: 1, fontSize: '1rem' }} />}
                {t('actions.delete')}
              </Button>
            </ResponsiveStack>
          ) : undefined}
          emptyMessage={t('inventory.noProducts')}
        />

        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          mt: isMobile ? 3 : 4,
          gap: isMobile ? 1 : 2,
          flexDirection: isMobile ? 'column' : 'row'
        }}>
          <ResponsiveStack
            direction={isMobile ? "column" : "row"}
            spacing={isMobile ? 1 : 2}
            alignItems="center"
            sx={{ width: isMobile ? '100%' : 'auto' }}
          >
            <Button
              variant="contained"
              onClick={handlePrevPage}
              disabled={currentPage === 0}
              fullWidth={isMobile}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                px: isMobile ? 2 : 3,
                py: isMobile ? 1.5 : 1,
                minHeight: isTouchDevice ? '44px' : '40px',
                fontSize: isMobile ? '0.875rem' : '1rem',
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
              }}
            >
              {t('actions.previous')}
            </Button>

            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                textAlign: 'center',
                minWidth: isMobile ? 'auto' : '120px',
                fontSize: isMobile ? '0.875rem' : '1rem'
              }}
            >
              {t('table.pageInfo', { page: currentPage + 1, total: totalPages })}
            </Typography>

            <Button
              variant="contained"
              onClick={handleNextPage}
              disabled={currentPage >= totalPages - 1}
              fullWidth={isMobile}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                px: isMobile ? 2 : 3,
                py: isMobile ? 1.5 : 1,
                minHeight: isTouchDevice ? '44px' : '40px',
                fontSize: isMobile ? '0.875rem' : '1rem',
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
              }}
            >
              {t('actions.next')}
            </Button>
          </ResponsiveStack>
        </Box>

        {isAdmin && (
          <ProductDialog
            open={openDialog}
            onClose={() => setOpenDialog(false)}
            product={selectedProduct}
            onSave={handleSaveProduct}
          />
        )}
        </Paper>
      </Box>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        sx={{
          '& .MuiAlert-root': {
            width: '100%',
            alignItems: 'center'
          }
        }}
      >
        <Alert
          elevation={6}
          variant="filled"
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          icon={snackbarSeverity === 'success' ? <CheckCircleIcon /> : 
                snackbarSeverity === 'error' ? <ErrorIcon /> : 
                snackbarSeverity === 'info' ? <InfoIcon /> : 
                <WarningIcon />}
          sx={{
            width: '100%',
            borderRadius: 2,
            '& .MuiAlert-icon': {
              fontSize: 24
            },
            '& .MuiAlert-message': {
              fontSize: '0.95rem',
              fontWeight: 500
            }
          }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </ResponsiveContainer>
  );
};

export default Inventory;