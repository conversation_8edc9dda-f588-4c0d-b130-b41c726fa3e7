/* eslint-disable no-unused-vars */
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Card,
  CardContent,
  useTheme,
  alpha,
  LinearProgress,
  Paper,
  Snackbar,
  Alert,
  Container,
  Stack,
  Avatar,
  Fade,
  Zoom,
  Tooltip,
  Badge,
  Divider,
  CircularProgress,
  Pagination
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  NotificationsActive,
  Download as DownloadIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Inventory as InventoryIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Store as StoreIcon,
  Category as CategoryIcon,
  CurrencyRupee as MoneyIcon,
  Schedule as ScheduleIcon,
  FilterList as FilterIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon
} from '@mui/icons-material';
import ProductDialog from '../components/ProductDialog';
import { getApiUrl } from '../config';
import { useTranslation } from 'react-i18next';

const Inventory = () => {
  const theme = useTheme();
  const [inventory, setInventory] = useState([]);
  const [filteredInventory, setFilteredInventory] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState('all');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(8);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'cards'
  const userRole = localStorage.getItem('userRole');
  const isAdmin = userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper';
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [stores, setStores] = useState([]);
  const { t } = useTranslation();

  useEffect(() => {
    fetchInventory();
    fetchStores();
  }, []);

  const fetchStores = async () => {
    try {
      const response = await fetch(getApiUrl('api/store'));
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setStores(data);
    } catch (error) {
      console.error('Error fetching stores:', error);
    }
  };

  const fetchInventory = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('api/product/'));
      const result = await response.json();
      
      if (result.success) {
        const products = result.data.map(product => ({
          id: product._id,
          name: product.name,
          sku: product.sku,
          stock: product.stock,
          lowStockThreshold: product.lowStockThreshold,
          price: product.price,
          category: product.category,
          manufacturing_date: new Date(product.manufacturing_date),
          expiry_date: new Date(product.expiry_date),
          store_id: product.store_id
        }));
        setInventory(products);
        setFilteredInventory(products);
      } else {
        console.error('Error fetching inventory:', result.message);
      }
    } catch (error) {
      console.error('Error fetching inventory:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const filtered = inventory.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          item.sku.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = category === 'all' || item.category === category;
      return matchesSearch && matchesCategory;
    });
    setFilteredInventory(filtered);
  }, [searchTerm, category, inventory]);

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setOpenDialog(true);
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setOpenDialog(true);
  };

  const handleDeleteProduct = async (product) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        const response = await fetch(getApiUrl(`api/product/delete/${product.sku}`), {
          method: 'DELETE'
        });
        
        if (response.ok) {
          const updatedInventory = inventory.filter(item => item.sku !== product.sku);
          setInventory(updatedInventory);
          setSnackbarMessage('Product deleted successfully!');
          setSnackbarSeverity('success');
        } else {
          console.error('Failed to delete product');
          setSnackbarMessage('Failed to delete product.');
          setSnackbarSeverity('error');
        }
      } catch (error) {
        console.error('Error deleting product:', error);
        setSnackbarMessage('Error deleting product.');
        setSnackbarSeverity('error');
      }
      setSnackbarOpen(true);
    }
  };

  const handleSaveProduct = async (formData) => {
    if (selectedProduct) {
      try {
        const response = await fetch(getApiUrl(`api/product/update/${selectedProduct.sku}`), {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData)
        });

        if (response.ok) {
          const result = await response.json();
          const updatedProduct = result.product;
          
          const updatedInventory = inventory.map(item => 
            item.sku === selectedProduct.sku ? {
              id: updatedProduct._id,
              name: updatedProduct.name,
              sku: updatedProduct.sku,
              stock: updatedProduct.stock,
              lowStockThreshold: updatedProduct.lowStockThreshold,
              price: updatedProduct.price,
              category: updatedProduct.category,
              manufacturing_date: new Date(updatedProduct.manufacturing_date),
              expiry_date: new Date(updatedProduct.expiry_date)
            } : item
          );
          setInventory(updatedInventory);
          setSnackbarMessage('Product updated successfully!');
          setSnackbarSeverity('success');
        }
      } catch (error) {
        console.error('Error updating product:', error);
        setSnackbarMessage('Error updating product.');
        setSnackbarSeverity('error');
      }
    } else {
      try {
        const response = await fetch(getApiUrl('api/product/add'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData)
        });

        if (response.ok) {
          const result = await response.json();
          const newProduct = {
            id: result.product._id,
            ...formData,
            manufacturing_date: new Date(formData.manufacturing_date),
            expiry_date: new Date(formData.expiry_date)
          };
          setInventory([...inventory, newProduct]);
          setSnackbarMessage('Product added successfully!');
          setSnackbarSeverity('success');
        }
      } catch (error) {
        console.error('Error adding product:', error);
        setSnackbarMessage('Error adding product.');
        setSnackbarSeverity('error');
      }
    }
    setOpenDialog(false);
    setSnackbarOpen(true);
  };

  const downloadProductList = () => {
    // Convert inventory data to CSV format
    const headers = ['Product Name', 'SKU', 'Category', 'Stock Level', 'Price', 'Manufacturing Date', 'Expiry Date'];
    const csvData = [
      headers,
      ...filteredInventory.map(product => [
        product.name,
        product.sku,
        product.category,
        product.stock,
        product.price,
        product.manufacturing_date.toLocaleDateString(),
        product.expiry_date.toLocaleDateString()
      ])
    ];
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'product_inventory.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };

  const totalPages = Math.ceil(filteredInventory.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedInventory = filteredInventory.slice(startIndex, endIndex);

  // Calculate statistics
  const stats = {
    total: inventory.length,
    lowStock: inventory.filter(item => item.stock <= item.lowStockThreshold).length,
    outOfStock: inventory.filter(item => item.stock === 0).length,
    expired: inventory.filter(item => item.expiry_date < new Date()).length,
    totalValue: inventory.reduce((sum, item) => sum + (item.price * item.stock), 0),
  };

  return (
    <Box
      sx={{
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        height: '100%',
        padding: 3,
        overflow: 'auto',
      }}
    >
      <Container maxWidth="xl">
        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 4,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight={700}
                sx={{
                  background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                  fontSize: { xs: '1.8rem', md: '2.5rem' },
                }}
              >
                📦 {t('inventory.title')}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage your product inventory with real-time tracking and analytics
              </Typography>
            </Box>

            <Stack direction="row" spacing={2}>
              <Tooltip title="Export Inventory">
                <IconButton
                  onClick={downloadProductList}
                  sx={{
                    background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1e88e5 0%, #1565c0 100%)',
                      transform: 'scale(1.05)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <DownloadIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={fetchInventory}
                  sx={{
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                      transform: 'scale(1.05)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>

              {isAdmin && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddProduct}
                  sx={{
                    borderRadius: 3,
                    textTransform: 'none',
                    px: 3,
                    py: 1.5,
                    background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                    boxShadow: '0 4px 20px rgba(76, 175, 80, 0.3)',
                    fontWeight: 600,
                    '&:hover': {
                      background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 6px 25px rgba(76, 175, 80, 0.4)',
                    },
                    transition: 'all 0.3s ease',
                  }}
                >
                  {t('actions.addProduct')}
                </Button>
              )}
            </Stack>
          </Stack>
        </Paper>

        {/* Statistics Dashboard */}
        <Grid container spacing={3} mb={3}>
          {[
            {
              label: 'Total Products',
              value: stats.total,
              icon: <InventoryIcon />,
              gradient: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              change: '+12%'
            },
            {
              label: 'Low Stock Items',
              value: stats.lowStock,
              icon: <TrendingDownIcon />,
              gradient: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
              change: '-5%'
            },
            {
              label: 'Out of Stock',
              value: stats.outOfStock,
              icon: <WarningIcon />,
              gradient: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
              change: '-8%'
            },
            {
              label: 'Total Value',
              value: `₹${(stats.totalValue / 1000).toFixed(1)}K`,
              icon: <MoneyIcon />,
              gradient: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
              change: '+15%'
            },
          ].map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={stat.label}>
              <Zoom in timeout={300 + index * 100}>
                <Card
                  sx={{
                    background: 'rgba(255, 255, 255, 0.98)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    borderRadius: 4,
                    overflow: 'hidden',
                    position: 'relative',
                    boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 40px rgba(25, 118, 210, 0.2)',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 4,
                      background: stat.gradient,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="h4" fontWeight={700} color="#1976d2">
                          {stat.value}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" fontWeight={500}>
                          {stat.label}
                        </Typography>
                        <Chip
                          label={stat.change}
                          size="small"
                          sx={{
                            mt: 1,
                            backgroundColor: stat.change.startsWith('+') ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
                            color: stat.change.startsWith('+') ? '#4caf50' : '#f44336',
                            fontWeight: 600,
                          }}
                        />
                      </Box>
                      <Avatar
                        sx={{
                          background: stat.gradient,
                          color: 'white',
                          width: 64,
                          height: 64,
                          boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                        }}
                      >
                        {stat.icon}
                      </Avatar>
                    </Stack>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>

        {/* Filters and Controls */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 3,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search products, SKU, category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    },
                  },
                }}
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Category Filter</InputLabel>
                <Select
                  value={category}
                  label="Category Filter"
                  onChange={(e) => setCategory(e.target.value)}
                  sx={{
                    borderRadius: 3,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    },
                  }}
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  <MenuItem value="Laying">Laying</MenuItem>
                  <MenuItem value="ElectricalMaterial">Electrical Material</MenuItem>
                  <MenuItem value="StructureMaterial">Structure Material</MenuItem>
                  <MenuItem value="PlumbingMaterials">Plumbing Materials</MenuItem>
                  <MenuItem value="ToolsAndEquipment">Tools & Equipment</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={3}>
              <Stack direction="row" spacing={1}>
                <Tooltip title="Table View">
                  <IconButton
                    onClick={() => setViewMode('table')}
                    sx={{
                      backgroundColor: viewMode === 'table' ? '#1976d2' : 'rgba(25, 118, 210, 0.1)',
                      color: viewMode === 'table' ? 'white' : '#1976d2',
                      '&:hover': {
                        backgroundColor: viewMode === 'table' ? '#1565c0' : 'rgba(25, 118, 210, 0.2)',
                      },
                    }}
                  >
                    <ViewListIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Card View">
                  <IconButton
                    onClick={() => setViewMode('cards')}
                    sx={{
                      backgroundColor: viewMode === 'cards' ? '#1976d2' : 'rgba(25, 118, 210, 0.1)',
                      color: viewMode === 'cards' ? 'white' : '#1976d2',
                      '&:hover': {
                        backgroundColor: viewMode === 'cards' ? '#1565c0' : 'rgba(25, 118, 210, 0.2)',
                      },
                    }}
                  >
                    <ViewModuleIcon />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Grid>

            <Grid item xs={12} md={2}>
              <Typography variant="body2" color="text.secondary" textAlign="center">
                Showing {startIndex + 1}-{Math.min(endIndex, filteredInventory.length)} of {filteredInventory.length}
              </Typography>
            </Grid>
          </Grid>
        </Paper>

        {/* Content Area */}
        {isLoading ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 8,
            }}
          >
            <CircularProgress
              size={60}
              thickness={4}
              sx={{ color: 'white' }}
            />
            <Typography variant="h6" sx={{ mt: 2, color: 'white', fontWeight: 500 }}>
              Loading inventory...
            </Typography>
          </Box>
        ) : viewMode === 'table' ? (
          <Paper
            elevation={0}
            sx={{
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              border: '1px solid rgba(255, 255, 255, 0.3)',
              overflow: 'hidden',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
            }}
          >

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow
                    sx={{
                      background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
                    }}
                  >
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Product Name</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Store</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>SKU</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Category</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Stock</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Price</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Mfg Date</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Expiry</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Status</TableCell>
                    {isAdmin && <TableCell align="right" sx={{ fontWeight: 700, color: '#1976d2' }}>Actions</TableCell>}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedInventory.map((product, index) => {
                    const productStore = stores.find(store => store._id === product.store_id);
                    const isLowStock = product.stock <= product.lowStockThreshold;
                    const isExpired = product.expiry_date < new Date();

                    return (
                      <Fade in timeout={300 + index * 50} key={product.id}>
                        <TableRow
                          hover
                          sx={{
                            '&:hover': {
                              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(21, 101, 192, 0.02) 100%)',
                              transform: 'scale(1.01)',
                            },
                            transition: 'all 0.2s ease',
                            cursor: 'pointer',
                          }}
                        >
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Avatar
                                sx={{
                                  width: 32,
                                  height: 32,
                                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                                  fontSize: '0.8rem',
                                }}
                              >
                                {product.name.charAt(0)}
                              </Avatar>
                              <Typography variant="body2" fontWeight={600}>
                                {product.name}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <StoreIcon fontSize="small" color="primary" />
                              <Box>
                                <Typography variant="body2" fontWeight={500}>
                                  {productStore?.storeName || 'N/A'}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {productStore?.storeLocation || ''}
                                </Typography>
                              </Box>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={product.sku}
                              size="small"
                              variant="outlined"
                              sx={{
                                borderColor: '#1976d2',
                                color: '#1976d2',
                                fontWeight: 600,
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={product.category}
                              size="small"
                              sx={{
                                backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                color: '#1976d2',
                                fontWeight: 600,
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Typography variant="body2" fontWeight={600}>
                                {product.stock}
                              </Typography>
                              <Chip
                                label={isLowStock ? 'Low Stock' : 'In Stock'}
                                size="small"
                                color={isLowStock ? 'error' : 'success'}
                                variant="outlined"
                              />
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={0.5}>
                              <MoneyIcon fontSize="small" color="success" />
                              <Typography variant="body2" fontWeight={600} color="success.main">
                                ₹{Math.floor(product.price)}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {product.manufacturing_date.toLocaleDateString()}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={product.expiry_date.toLocaleDateString()}
                              size="small"
                              color={isExpired ? 'error' : 'success'}
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={0.5}>
                              {isExpired && (
                                <Chip label="Expired" size="small" color="error" />
                              )}
                              {isLowStock && (
                                <Chip label="Low" size="small" color="warning" />
                              )}
                              {!isExpired && !isLowStock && (
                                <Chip label="Good" size="small" color="success" />
                              )}
                            </Stack>
                          </TableCell>
                          {isAdmin && (
                            <TableCell align="right">
                              <Stack direction="row" spacing={1}>
                                <Tooltip title="Edit Product">
                                  <IconButton
                                    onClick={() => handleEditProduct(product)}
                                    sx={{
                                      background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                                      color: 'white',
                                      '&:hover': {
                                        background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                                        transform: 'scale(1.1)'
                                      },
                                      transition: 'all 0.2s'
                                    }}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Delete Product">
                                  <IconButton
                                    onClick={() => handleDeleteProduct(product)}
                                    sx={{
                                      background: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
                                      color: 'white',
                                      '&:hover': {
                                        background: 'linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%)',
                                        transform: 'scale(1.1)'
                                      },
                                      transition: 'all 0.2s'
                                    }}
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Stack>
                            </TableCell>
                          )}
                        </TableRow>
                      </Fade>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        ) : (
          // Card View
          <Grid container spacing={3}>
            {paginatedInventory.map((product, index) => {
              const productStore = stores.find(store => store._id === product.store_id);
              const isLowStock = product.stock <= product.lowStockThreshold;
              const isExpired = product.expiry_date < new Date();

              return (
                <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
                  <Fade in timeout={300 + index * 100}>
                    <Card
                      sx={{
                        background: 'rgba(255, 255, 255, 0.98)',
                        backdropFilter: 'blur(20px)',
                        borderRadius: 4,
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                        boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: '0 20px 60px rgba(25, 118, 210, 0.25)',
                        },
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Stack spacing={2}>
                          {/* Header */}
                          <Stack direction="row" alignItems="center" justifyContent="space-between">
                            <Avatar
                              sx={{
                                width: 48,
                                height: 48,
                                background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                                fontSize: '1.2rem',
                                fontWeight: 700,
                              }}
                            >
                              {product.name.charAt(0)}
                            </Avatar>
                            <Stack direction="row" spacing={0.5}>
                              {isExpired && <Chip label="Expired" size="small" color="error" />}
                              {isLowStock && <Chip label="Low" size="small" color="warning" />}
                              {!isExpired && !isLowStock && <Chip label="Good" size="small" color="success" />}
                            </Stack>
                          </Stack>

                          {/* Product Info */}
                          <Box>
                            <Typography variant="h6" fontWeight={700} color="#1976d2" gutterBottom>
                              {product.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              SKU: {product.sku}
                            </Typography>
                            <Chip
                              label={product.category}
                              size="small"
                              sx={{
                                backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                color: '#1976d2',
                                fontWeight: 600,
                              }}
                            />
                          </Box>

                          {/* Store Info */}
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <StoreIcon fontSize="small" color="primary" />
                            <Box>
                              <Typography variant="body2" fontWeight={600}>
                                {productStore?.storeName || 'N/A'}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {productStore?.storeLocation || ''}
                              </Typography>
                            </Box>
                          </Stack>

                          {/* Stock and Price */}
                          <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Box>
                              <Typography variant="caption" color="text.secondary">
                                Stock
                              </Typography>
                              <Typography variant="h6" fontWeight={700} color={isLowStock ? 'error.main' : 'success.main'}>
                                {product.stock}
                              </Typography>
                            </Box>
                            <Box textAlign="right">
                              <Typography variant="caption" color="text.secondary">
                                Price
                              </Typography>
                              <Typography variant="h6" fontWeight={700} color="success.main">
                                ₹{Math.floor(product.price)}
                              </Typography>
                            </Box>
                          </Stack>

                          {/* Dates */}
                          <Stack direction="row" justifyContent="space-between">
                            <Box>
                              <Typography variant="caption" color="text.secondary">
                                Mfg Date
                              </Typography>
                              <Typography variant="body2">
                                {product.manufacturing_date.toLocaleDateString()}
                              </Typography>
                            </Box>
                            <Box textAlign="right">
                              <Typography variant="caption" color="text.secondary">
                                Expiry
                              </Typography>
                              <Typography variant="body2" color={isExpired ? 'error.main' : 'text.primary'}>
                                {product.expiry_date.toLocaleDateString()}
                              </Typography>
                            </Box>
                          </Stack>

                          {/* Actions */}
                          {isAdmin && (
                            <Stack direction="row" spacing={1} justifyContent="center">
                              <Button
                                variant="outlined"
                                startIcon={<EditIcon />}
                                onClick={() => handleEditProduct(product)}
                                sx={{
                                  borderRadius: 2,
                                  borderColor: '#1976d2',
                                  color: '#1976d2',
                                  '&:hover': {
                                    backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                    borderColor: '#1565c0',
                                  },
                                }}
                              >
                                Edit
                              </Button>
                              <Button
                                variant="outlined"
                                startIcon={<DeleteIcon />}
                                onClick={() => handleDeleteProduct(product)}
                                color="error"
                                sx={{
                                  borderRadius: 2,
                                  '&:hover': {
                                    backgroundColor: 'rgba(244, 67, 54, 0.1)',
                                  },
                                }}
                              >
                                Delete
                              </Button>
                            </Stack>
                          )}
                        </Stack>
                      </CardContent>
                    </Card>
                  </Fade>
                </Grid>
              );
            })}
          </Grid>
        )}

        {/* Pagination */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 3,
            mt: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
            size="large"
            sx={{
              '& .MuiPaginationItem-root': {
                borderRadius: 2,
                fontWeight: 600,
                '&.Mui-selected': {
                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                  color: 'white',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                  },
                },
                '&:hover': {
                  backgroundColor: 'rgba(25, 118, 210, 0.1)',
                },
              },
            }}
          />
        </Paper>

        {/* Product Dialog */}
        {isAdmin && (
          <ProductDialog
            open={openDialog}
            onClose={() => setOpenDialog(false)}
            product={selectedProduct}
            onSave={handleSaveProduct}
          />
        )}

        {/* Snackbar */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={() => setSnackbarOpen(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setSnackbarOpen(false)}
            severity={snackbarSeverity}
            sx={{
              width: '100%',
              borderRadius: 3,
              backdropFilter: 'blur(20px)',
              background: snackbarSeverity === 'success'
                ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.95) 0%, rgba(56, 142, 60, 0.95) 100%)'
                : snackbarSeverity === 'error'
                ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.95) 0%, rgba(211, 47, 47, 0.95) 100%)'
                : 'linear-gradient(135deg, rgba(25, 118, 210, 0.95) 0%, rgba(21, 101, 192, 0.95) 100%)',
              color: 'white',
              fontWeight: 600,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
            }}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

export default Inventory;