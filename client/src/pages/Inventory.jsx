import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Chip,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { usePermissions } from '../hooks/usePermissions';
import ProductDialog from '../components/ProductDialog';

const Inventory = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { isAdmin } = usePermissions();

  // State
  const [inventory, setInventory] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState('all');
  const [currentPage, setCurrentPage] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  const itemsPerPage = 5;

  // Mock data for testing
  useEffect(() => {
    const mockData = [
      {
        id: 1,
        name: 'Product 1',
        sku: 'SKU001',
        category: 'Electronics',
        stock: 50,
        price: 100,
        lowStockThreshold: 10,
        manufacturing_date: new Date('2023-01-01'),
        expiry_date: new Date('2025-01-01')
      },
      {
        id: 2,
        name: 'Product 2',
        sku: 'SKU002',
        category: 'Clothing',
        stock: 5,
        price: 50,
        lowStockThreshold: 10,
        manufacturing_date: new Date('2023-02-01'),
        expiry_date: new Date('2024-12-01')
      }
    ];
    setInventory(mockData);
  }, []);

  // Filter and paginate data
  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = category === 'all' || item.category === category;
    return matchesSearch && matchesCategory;
  });

  const totalPages = Math.ceil(filteredInventory.length / itemsPerPage);
  const paginatedInventory = filteredInventory.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  // Handlers
  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages - 1, prev + 1));
  };

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setOpenDialog(true);
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setOpenDialog(true);
  };

  const handleDeleteProduct = (product) => {
    setSnackbarMessage(`Product ${product.name} deleted`);
    setSnackbarSeverity('success');
    setSnackbarOpen(true);
  };

  const handleSaveProduct = (productData) => {
    setSnackbarMessage('Product saved successfully');
    setSnackbarSeverity('success');
    setSnackbarOpen(true);
    setOpenDialog(false);
  };

  const downloadProductList = () => {
    setSnackbarMessage('Product list downloaded');
    setSnackbarSeverity('info');
    setSnackbarOpen(true);
  };

  const fetchInventory = () => {
    setSnackbarMessage('Inventory refreshed');
    setSnackbarSeverity('info');
    setSnackbarOpen(true);
  };

  return (
    <>
      <Box sx={{ p: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          {/* Header */}
          <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h4" fontWeight="bold" color="primary">
              {t('inventory.title')}
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={downloadProductList}
              >
                {t('actions.exportList')}
              </Button>
              {isAdmin && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddProduct}
                >
                  {t('actions.addProduct')}
                </Button>
              )}
            </Box>
          </Box>

          {/* Filters */}
          <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              placeholder={t('search.placeholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
              sx={{ width: 300 }}
            />
            <FormControl sx={{ width: 200 }}>
              <InputLabel>{t('filter.category')}</InputLabel>
              <Select
                value={category}
                label={t('filter.category')}
                onChange={(e) => setCategory(e.target.value)}
              >
                <MenuItem value="all">{t('filter.allCategories')}</MenuItem>
                <MenuItem value="Electronics">Electronics</MenuItem>
                <MenuItem value="Clothing">Clothing</MenuItem>
              </Select>
            </FormControl>
            <IconButton onClick={fetchInventory}>
              <RefreshIcon color="primary" />
            </IconButton>
          </Box>

          {/* Table */}
          <TableContainer component={Paper} sx={{ mt: 3 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('table.productName')}</TableCell>
                  <TableCell>{t('table.sku')}</TableCell>
                  <TableCell>{t('table.category')}</TableCell>
                  <TableCell>{t('table.stockLevel')}</TableCell>
                  <TableCell>{t('table.price')}</TableCell>
                  {isAdmin && <TableCell>{t('table.actions')}</TableCell>}
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedInventory.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.name}</TableCell>
                    <TableCell>{item.sku}</TableCell>
                    <TableCell>
                      <Chip label={item.category} size="small" />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${item.stock}`}
                        size="small"
                        color={item.stock < item.lowStockThreshold ? 'error' : 'success'}
                      />
                    </TableCell>
                    <TableCell>₹{Math.floor(item.price)}</TableCell>
                    {isAdmin && (
                      <TableCell>
                        <IconButton onClick={() => handleEditProduct(item)} size="small">
                          <EditIcon />
                        </IconButton>
                        <IconButton onClick={() => handleDeleteProduct(item)} size="small">
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, gap: 2 }}>
            <Button
              variant="contained"
              onClick={handlePrevPage}
              disabled={currentPage === 0}
            >
              {t('actions.previous')}
            </Button>
            <Typography variant="body2" sx={{ alignSelf: 'center' }}>
              Page {currentPage + 1} of {totalPages}
            </Typography>
            <Button
              variant="contained"
              onClick={handleNextPage}
              disabled={currentPage >= totalPages - 1}
            >
              {t('actions.next')}
            </Button>
          </Box>

          {/* Product Dialog */}
          {isAdmin && (
            <ProductDialog
              open={openDialog}
              onClose={() => setOpenDialog(false)}
              product={selectedProduct}
              onSave={handleSaveProduct}
            />
          )}
        </Paper>
      </Box>

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          elevation={6}
          variant="filled"
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default Inventory;