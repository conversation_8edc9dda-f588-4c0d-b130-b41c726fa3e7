import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Chip,
  Snackbar,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { usePermissions } from '../hooks/usePermissions';
import { getApiUrl } from '../config';
import ProductDialog from '../components/ProductDialog';

const Inventory = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { isAdmin } = usePermissions();

  // State
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState('all');
  const [currentPage, setCurrentPage] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  const itemsPerPage = 5;

  // Fetch inventory data from API
  useEffect(() => {
    fetchInventoryData();
  }, []);

  const fetchInventoryData = async () => {
    try {
      setLoading(true);
      const response = await fetch(getApiUrl('api/product/'));
      const result = await response.json();
      
      if (result.success) {
        const products = result.data.map(({ _id, name, sku, stock, lowStockThreshold, price, category, manufacturing_date, expiry_date }) => ({
          id: _id,
          name,
          sku,
          stock,
          lowStockThreshold,
          price,
          category,
          manufacturing_date: manufacturing_date ? new Date(manufacturing_date) : null,
          expiry_date: expiry_date ? new Date(expiry_date) : null
        }));
        
        setInventory(products);
      } else {
        setSnackbarMessage('Failed to fetch inventory data');
        setSnackbarSeverity('error');
        setSnackbarOpen(true);
      }
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setSnackbarMessage('Error fetching inventory data');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };

  // Filter and paginate data
  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = category === 'all' || item.category === category;
    return matchesSearch && matchesCategory;
  });

  const totalPages = Math.ceil(filteredInventory.length / itemsPerPage);
  const paginatedInventory = filteredInventory.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  // Handlers
  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages - 1, prev + 1));
  };

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setOpenDialog(true);
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setOpenDialog(true);
  };

  const handleDeleteProduct = async (product) => {
    try {
      const response = await fetch(getApiUrl(`api/product/${product.id}`), {
        method: 'DELETE',
      });

      if (response.ok) {
        setInventory(prev => prev.filter(item => item.id !== product.id));
        setSnackbarMessage(`Product ${product.name} deleted successfully`);
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
      } else {
        setSnackbarMessage('Failed to delete product');
        setSnackbarSeverity('error');
        setSnackbarOpen(true);
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      setSnackbarMessage('Error deleting product');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const handleSaveProduct = async (productData) => {
    try {
      if (selectedProduct) {
        // Update existing product
        const response = await fetch(getApiUrl(`api/product/${selectedProduct.id}`), {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(productData),
        });

        if (response.ok) {
          const updatedProduct = await response.json();
          setInventory(prev => prev.map(item => 
            item.id === selectedProduct.id ? { ...item, ...productData } : item
          ));
          setSnackbarMessage('Product updated successfully');
        } else {
          setSnackbarMessage('Failed to update product');
          setSnackbarSeverity('error');
        }
      } else {
        // Add new product
        const response = await fetch(getApiUrl('api/product/add'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(productData),
        });

        if (response.ok) {
          const result = await response.json();
          const newProduct = {
            id: result.product._id,
            ...productData,
            manufacturing_date: productData.manufacturing_date ? new Date(productData.manufacturing_date) : null,
            expiry_date: productData.expiry_date ? new Date(productData.expiry_date) : null
          };
          setInventory(prev => [...prev, newProduct]);
          setSnackbarMessage('Product added successfully');
        } else {
          setSnackbarMessage('Failed to add product');
          setSnackbarSeverity('error');
        }
      }
      
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      setOpenDialog(false);
    } catch (error) {
      console.error('Error saving product:', error);
      setSnackbarMessage('Error saving product');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const downloadProductList = () => {
    try {
      const csvContent = [
        ['Name', 'SKU', 'Category', 'Stock', 'Price', 'Low Stock Threshold'],
        ...inventory.map(item => [
          item.name,
          item.sku,
          item.category,
          item.stock,
          item.price,
          item.lowStockThreshold
        ])
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'inventory.csv';
      a.click();
      window.URL.revokeObjectURL(url);

      setSnackbarMessage('Product list downloaded successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    } catch (error) {
      setSnackbarMessage('Failed to download product list');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  const refreshInventory = () => {
    fetchInventoryData();
  };

  return (
    <>
      <Box sx={{ p: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          {/* Header */}
          <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h4" fontWeight="bold" color="primary">
              {t('inventory.title')}
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={downloadProductList}
              >
                {t('actions.exportList')}
              </Button>
              {isAdmin && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddProduct}
                >
                  {t('actions.addProduct')}
                </Button>
              )}
            </Box>
          </Box>

          {/* Filters */}
          <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              placeholder={t('search.placeholder')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
              sx={{ width: 300 }}
            />
            <FormControl sx={{ width: 200 }}>
              <InputLabel>{t('filter.category')}</InputLabel>
              <Select
                value={category}
                label={t('filter.category')}
                onChange={(e) => setCategory(e.target.value)}
              >
                <MenuItem value="all">{t('filter.allCategories')}</MenuItem>
                <MenuItem value="Laying">Laying</MenuItem>
                <MenuItem value="ElectricalMaterial">Electrical Material</MenuItem>
                <MenuItem value="StructureMaterial">Structure Material</MenuItem>
                <MenuItem value="PlumbingMaterials">Plumbing Materials</MenuItem>
                <MenuItem value="ToolsAndEquipment">Tools & Equipment</MenuItem>
                <MenuItem value="Electronics">Electronics</MenuItem>
              </Select>
            </FormControl>
            <IconButton onClick={refreshInventory}>
              <RefreshIcon color="primary" />
            </IconButton>
          </Box>

          {/* Table */}
          <TableContainer component={Paper} sx={{ mt: 3 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('table.productName')}</TableCell>
                  <TableCell>{t('table.sku')}</TableCell>
                  <TableCell>{t('table.category')}</TableCell>
                  <TableCell>{t('table.stockLevel')}</TableCell>
                  <TableCell>{t('table.price')}</TableCell>
                  {isAdmin && <TableCell>{t('table.actions')}</TableCell>}
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={isAdmin ? 6 : 5} align="center" sx={{ py: 4 }}>
                      <CircularProgress />
                      <Typography variant="body2" sx={{ mt: 2 }}>
                        Loading inventory...
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : paginatedInventory.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={isAdmin ? 6 : 5} align="center" sx={{ py: 4 }}>
                      <Typography variant="body2" color="text.secondary">
                        No products found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedInventory.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.name}</TableCell>
                      <TableCell>{item.sku}</TableCell>
                      <TableCell>
                        <Chip label={item.category} size="small" />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={`${item.stock}`}
                          size="small"
                          color={item.stock < item.lowStockThreshold ? 'error' : 'success'}
                        />
                      </TableCell>
                      <TableCell>₹{Math.floor(item.price)}</TableCell>
                      {isAdmin && (
                        <TableCell>
                          <IconButton onClick={() => handleEditProduct(item)} size="small">
                            <EditIcon />
                          </IconButton>
                          <IconButton onClick={() => handleDeleteProduct(item)} size="small">
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      )}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={handlePrevPage}
              disabled={currentPage === 0}
            >
              {t('pagination.previous')}
            </Button>
            <Typography variant="body2">
              {t('pagination.pageInfo', { current: currentPage + 1, total: totalPages })}
            </Typography>
            <Button
              variant="outlined"
              onClick={handleNextPage}
              disabled={currentPage >= totalPages - 1}
            >
              {t('pagination.next')}
            </Button>
          </Box>
        </Paper>
      </Box>

      {/* Product Dialog */}
      {isAdmin && (
        <ProductDialog
          open={openDialog}
          onClose={() => setOpenDialog(false)}
          product={selectedProduct}
          onSave={handleSaveProduct}
        />
      )}

      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default Inventory;
