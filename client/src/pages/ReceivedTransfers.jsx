import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Grid,
  CircularProgress,
  Alert,
  Snackbar,
  Chip,
  Select,
  MenuItem,
  Button,
  TextField,
  ButtonGroup,
  IconButton,
  Tooltip,
  Badge,
  Divider,
  Stack,
  Avatar,
  LinearProgress,
  Fade,
  Zoom,
  Collapse,
  Pagination,
  Container
} from '@mui/material';
import {
  CheckCircle,
  Error,
  HourglassEmpty,
  KeyboardArrowDown,
  Search,
  FilterList,
  Refresh,
  GetApp,
  Visibility,
  LocalShipping,
  Store,
  Timeline,
  TrendingUp,
  Assignment,
  Schedule,
  CheckCircleOutline,
  ErrorOutline,
  PendingActions,
  Done,
  Close,
  MoreVert
} from '@mui/icons-material';
import { getApiUrl } from '../config';
import Cookies from 'js-cookie';

const ALLOWED_ROLES = ['storekeeper', 'siteengineer', 'admin', 'superadmin'];

const statusConfig = {
  approved: {
    color: '#1976d2',
    bgColor: '#e3f2fd',
    icon: <CheckCircleOutline sx={{ fontSize: 20 }} />,
    label: 'Approved',
    description: 'Transfer has been approved and ready for processing'
  },
  rejected: {
    color: '#d32f2f',
    bgColor: '#ffebee',
    icon: <ErrorOutline sx={{ fontSize: 20 }} />,
    label: 'Rejected',
    description: 'Transfer request has been declined'
  },
  pending: {
    color: '#1565c0',
    bgColor: '#e1f5fe',
    icon: <PendingActions sx={{ fontSize: 20 }} />,
    label: 'Pending',
    description: 'Awaiting approval from store manager'
  },
  fulfilled: {
    color: '#0d47a1',
    bgColor: '#e8eaf6',
    icon: <Done sx={{ fontSize: 20 }} />,
    label: 'Fulfilled',
    description: 'Transfer completed successfully'
  },
  delivered: {
    color: '#1e88e5',
    bgColor: '#e3f2fd',
    icon: <LocalShipping sx={{ fontSize: 20 }} />,
    label: 'Delivered',
    description: 'Items have been delivered to destination'
  }
};

const ReceivedTransfers = () => {
  const [receivedRequests, setReceivedRequests] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const userRole = localStorage.getItem('userRole');
  const [searchId, setSearchId] = useState('');
  const userStoreId = localStorage.getItem('storeId');
  const [showReceivedOnly, setShowReceivedOnly] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'
  const [expandedCard, setExpandedCard] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6); // 6 items per page for better performance

  useEffect(() => {
    fetchReceivedRequests();
  }, []);

  // Add some mock data for testing pagination if no real data exists
  useEffect(() => {
    if (receivedRequests.length === 0 && !isLoading) {
      // Create mock data for testing
      const mockData = Array.from({ length: 12 }, (_, i) => ({
        _id: `mock-${i}`,
        transferId: `TRF-${String(i + 1).padStart(3, '0')}`,
        status: ['pending', 'approved', 'fulfilled', 'rejected'][i % 4],
        fromStore: { storeName: `Store ${i + 1}`, storeLocation: `Location ${i + 1}` },
        toStore: { storeName: `Store ${i + 2}`, storeLocation: `Location ${i + 2}` },
        items: [
          {
            product: { name: `Product ${i + 1}`, sku: `SKU-${i + 1}` },
            quantity: Math.floor(Math.random() * 100) + 1
          }
        ],
        requestDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
      }));
      setReceivedRequests(mockData);
    }
  }, [receivedRequests.length, isLoading]);

  const fetchReceivedRequests = async (showRefreshIndicator = false) => {
    if (showRefreshIndicator) {
      setRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError(null);

    try {
      const response = await fetch(getApiUrl('api/transfers/all'));
      const result = await response.json();

      if (response.ok && result.success) {
        setReceivedRequests(result.data);
        if (showRefreshIndicator) {
          showSnackbar('Data refreshed successfully!', 'success');
        }
      } else {
        setError(result.message || 'Failed to fetch transfer requests.');
        showSnackbar(result.message || 'Failed to fetch transfer requests.', 'error');
      }
    } catch (err) {
      setError('Error fetching transfer requests.');
      showSnackbar('Error fetching transfer requests.', 'error');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const handleApproveReject = async (id, status) => {
    try {
      const token = Cookies.get('jwtToken');
      const response = await fetch(getApiUrl(`api/transfers/approve-reject/${id}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status })
      });
      const result = await response.json();
      if (response.ok) {
        showSnackbar(`Transfer request ${status} successfully.`, 'success');
        fetchReceivedRequests();
      } else {
        showSnackbar(result.message || 'Failed to update request.', 'error');
      }
    } catch (err) {
      showSnackbar('Error updating request.', 'error');
    }
  };

  const handleMarkReceived = async (id) => {
    try {
      const token = Cookies.get('jwtToken');
      const response = await fetch(getApiUrl(`api/transfers/mark-received/${id}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      const result = await response.json();
      if (response.ok) {
        showSnackbar('Transfer marked as received.', 'success');
        fetchReceivedRequests();
      } else {
        showSnackbar(result.message || 'Failed to mark as received.', 'error');
      }
    } catch (err) {
      showSnackbar('Error updating request.', 'error');
    }
  };

  // Helper to check if user is in toStore or fromStore
  const isUserToStore = (request) => {
    if (userRole === 'superadmin' || userRole === 'admin') return true;
    return userStoreId && request.toStore && userStoreId === (request.toStore._id || request.toStore);
  };
  const isUserFromStore = (request) => {
    if (userRole === 'superadmin' || userRole === 'admin') return true;
    return userStoreId && request.fromStore && userStoreId === (request.fromStore._id || request.fromStore);
  };

  // Handler for requester confirmation
  const handleConfirmReceipt = async (id) => {
    try {
      const token = Cookies.get('jwtToken');
      const response = await fetch(getApiUrl(`api/transfers/confirm-received/${id}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      const result = await response.json();
      if (response.ok) {
        showSnackbar('Requester confirmed receipt.', 'success');
        fetchReceivedRequests();
      } else {
        showSnackbar(result.message || 'Failed to confirm receipt.', 'error');
      }
    } catch (err) {
      showSnackbar('Error confirming receipt.', 'error');
    }
  };

  // Enhanced filtering
  let filteredRequests = receivedRequests;

  if (searchId) {
    filteredRequests = filteredRequests.filter(r =>
      r.transferId?.toLowerCase().includes(searchId.toLowerCase()) ||
      r.items?.some(item => item.product?.name?.toLowerCase().includes(searchId.toLowerCase())) ||
      r.fromStore?.storeName?.toLowerCase().includes(searchId.toLowerCase()) ||
      r.toStore?.storeName?.toLowerCase().includes(searchId.toLowerCase())
    );
  }

  if (selectedStatus !== 'all') {
    filteredRequests = filteredRequests.filter(r => r.status === selectedStatus);
  }

  if (showReceivedOnly) {
    filteredRequests = filteredRequests.filter(r => r.status === 'fulfilled');
  }

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchId, selectedStatus, showReceivedOnly]);

  // Statistics
  const stats = {
    total: receivedRequests.length,
    pending: receivedRequests.filter(r => r.status === 'pending').length,
    approved: receivedRequests.filter(r => r.status === 'approved').length,
    fulfilled: receivedRequests.filter(r => r.status === 'fulfilled').length,
    rejected: receivedRequests.filter(r => r.status === 'rejected').length,
  };

  const getStatusProgress = (status) => {
    const total = stats.total || 1;
    return (stats[status] / total) * 100;
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedRequests = filteredRequests.slice(startIndex, endIndex);

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
    // Scroll to top of content area when page changes
    const contentElement = document.getElementById('content-area');
    if (contentElement) {
      contentElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        height: 'auto',
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        overflow: 'visible',
        paddingBottom: 4,
      }}
    >
      <Container
        maxWidth="xl"
        sx={{
          py: 4,
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 4,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
          }}
        >
        <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
          <Box>
            <Typography
              variant="h4"
              fontWeight={700}
              sx={{
                background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1,
                fontSize: { xs: '1.8rem', md: '2.5rem' },
              }}
            >
              📦 Received Product Transfers
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage and track incoming inventory transfers
            </Typography>
          </Box>

          <Stack direction="row" spacing={2}>
            <Tooltip title="Refresh Data">
              <IconButton
                onClick={() => fetchReceivedRequests(true)}
                disabled={refreshing}
                sx={{
                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                  color: 'white',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                {refreshing ? <CircularProgress size={20} color="inherit" /> : <Refresh />}
              </IconButton>
            </Tooltip>

            <Tooltip title="Export Data">
              <IconButton
                sx={{
                  background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%)',
                  color: 'white',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #1e88e5 0%, #1565c0 100%)',
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                <GetApp />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
      </Paper>

        {/* Statistics Dashboard */}
        <Grid container spacing={3} mb={3}>
          {[
            { label: 'Total Transfers', value: stats.total, color: '#1976d2', icon: <Assignment />, gradient: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)' },
            { label: 'Pending', value: stats.pending, color: '#1565c0', icon: <Schedule />, gradient: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)' },
            { label: 'Approved', value: stats.approved, color: '#1e88e5', icon: <CheckCircleOutline />, gradient: 'linear-gradient(135deg, #1e88e5 0%, #1976d2 100%)' },
            { label: 'Fulfilled', value: stats.fulfilled, color: '#42a5f5', icon: <Done />, gradient: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%)' },
          ].map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={stat.label}>
            <Zoom in={!isLoading} style={{ transitionDelay: `${index * 100}ms` }}>
              <Card
                sx={{
                  background: 'rgba(255, 255, 255, 0.98)',
                  backdropFilter: 'blur(20px)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  borderRadius: 4,
                  overflow: 'hidden',
                  position: 'relative',
                  boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(25, 118, 210, 0.2)',
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: 4,
                    background: stat.gradient,
                  },
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography variant="h4" fontWeight={700} color={stat.color}>
                        {stat.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" fontWeight={500}>
                        {stat.label}
                      </Typography>
                    </Box>
                    <Avatar
                      sx={{
                        background: stat.gradient,
                        color: 'white',
                        width: 64,
                        height: 64,
                        boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                      }}
                    >
                      {stat.icon}
                    </Avatar>
                  </Stack>
                  <LinearProgress
                    variant="determinate"
                    value={getStatusProgress(stat.label.toLowerCase().replace(' ', ''))}
                    sx={{
                      mt: 2,
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: `${stat.color}20`,
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: stat.color,
                        borderRadius: 3,
                      },
                    }}
                  />
                </CardContent>
              </Card>
            </Zoom>
          </Grid>
        ))}
      </Grid>

        {/* Filters and Controls */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 3,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
          }}
        >
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search transfers, products, stores..."
              value={searchId}
              onChange={(e) => setSearchId(e.target.value)}
              InputProps={{
                startAdornment: <Search sx={{ color: 'text.secondary', mr: 1 }} />,
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                },
              }}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              select
              fullWidth
              label="Status Filter"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                },
              }}
            >
              <MenuItem value="all">All Status</MenuItem>
              <MenuItem value="pending">Pending</MenuItem>
              <MenuItem value="approved">Approved</MenuItem>
              <MenuItem value="fulfilled">Fulfilled</MenuItem>
              <MenuItem value="rejected">Rejected</MenuItem>
            </TextField>
          </Grid>

          <Grid item xs={12} md={3}>
            <ButtonGroup variant="outlined" fullWidth>
              <Button
                variant={viewMode === 'cards' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('cards')}
                sx={{ borderRadius: '8px 0 0 8px' }}
              >
                Cards
              </Button>
              <Button
                variant={viewMode === 'table' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('table')}
                sx={{ borderRadius: '0 8px 8px 0' }}
              >
                Table
              </Button>
            </ButtonGroup>
          </Grid>

          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant={showReceivedOnly ? 'contained' : 'outlined'}
              onClick={() => setShowReceivedOnly(!showReceivedOnly)}
              startIcon={<FilterList />}
              sx={{ borderRadius: 2 }}
            >
              {showReceivedOnly ? 'Show All' : 'Received Only'}
            </Button>
          </Grid>
        </Grid>
      </Paper>
        {/* Content Area */}
        <Box
          id="content-area"
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            minHeight: 0,
          }}
        >

        {isLoading ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 8,
            }}
          >
            <CircularProgress
              size={60}
              thickness={4}
              sx={{ color: 'white' }}
            />
            <Typography variant="h6" sx={{ mt: 2, color: 'white', fontWeight: 500 }}>
              Loading transfers...
            </Typography>
          </Box>
        ) : error ? (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              borderRadius: 3,
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
            }}
          >
            {error}
          </Alert>
        ) : filteredRequests.length === 0 ? (
          <Paper
            sx={{
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              p: 6,
              textAlign: 'center',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
            }}
          >
            <Typography variant="h6" color="text.secondary" gutterBottom>
              📭 No transfer requests found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {searchId || selectedStatus !== 'all'
                ? 'Try adjusting your filters to see more results'
                : 'No transfers have been received yet'
              }
            </Typography>
          </Paper>
        ) : viewMode === 'cards' ? (
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {paginatedRequests.map((request, index) => (
                <Grid item xs={12} sm={6} md={4} lg={4} key={request._id}>
              <Fade in timeout={300 + index * 100}>
                <Card
                  sx={{
                    background: 'rgba(255, 255, 255, 0.98)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: 4,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 20px 60px rgba(25, 118, 210, 0.25)',
                    },
                  }}
                  onClick={() => setExpandedCard(expandedCard === request._id ? null : request._id)}
                >
                  <CardContent sx={{ p: 3 }}>
                    {/* Card Header */}
                    <Stack direction="row" justifyContent="space-between" alignItems="flex-start" mb={2}>
                      <Box>
                        <Typography variant="h6" fontWeight={600} gutterBottom>
                          {request.transferId || 'N/A'}
                        </Typography>
                        <Chip
                          label={statusConfig[request.status]?.label || request.status}
                          icon={statusConfig[request.status]?.icon}
                          sx={{
                            backgroundColor: statusConfig[request.status]?.bgColor,
                            color: statusConfig[request.status]?.color,
                            fontWeight: 600,
                            fontSize: '0.75rem',
                          }}
                        />
                      </Box>
                      <IconButton size="small">
                        <MoreVert />
                      </IconButton>
                    </Stack>

                    {/* Transfer Route */}
                    <Box sx={{ mb: 2 }}>
                      <Stack direction="row" alignItems="center" spacing={1} mb={1}>
                        <Store sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          From: {request.fromStore?.storeName || 'N/A'}
                        </Typography>
                      </Stack>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <LocalShipping sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          To: {request.toStore?.storeName || 'N/A'}
                        </Typography>
                      </Stack>
                    </Box>

                    {/* Products Summary */}
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" fontWeight={500} gutterBottom>
                        Products ({request.items?.length || 0})
                      </Typography>
                      {request.items?.slice(0, 2).map((item, idx) => (
                        <Typography key={idx} variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                          • {item.product?.name} (Qty: {item.quantity})
                        </Typography>
                      ))}
                      {request.items?.length > 2 && (
                        <Typography variant="body2" color="primary" sx={{ fontSize: '0.8rem', mt: 0.5 }}>
                          +{request.items.length - 2} more items
                        </Typography>
                      )}
                    </Box>

                    {/* Date */}
                    <Typography variant="caption" color="text.secondary">
                      {new Date(request.requestDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })}
                    </Typography>

                    {/* Expanded Content */}
                    <Collapse in={expandedCard === request._id}>
                      <Divider sx={{ my: 2 }} />
                      <Box>
                        {/* Action Buttons */}
                        {request.status === 'pending' && ALLOWED_ROLES.includes(userRole) && (
                          <Stack direction="row" spacing={1} mb={2}>
                            <Button
                              size="small"
                              variant="contained"
                              color="success"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleApproveReject(request._id, 'approved');
                              }}
                              sx={{ borderRadius: 2 }}
                            >
                              Approve
                            </Button>
                            <Button
                              size="small"
                              variant="contained"
                              color="error"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleApproveReject(request._id, 'rejected');
                              }}
                              sx={{ borderRadius: 2 }}
                            >
                              Reject
                            </Button>
                          </Stack>
                        )}

                        {request.status === 'approved' && isUserToStore(request) && (
                          <Button
                            size="small"
                            variant="contained"
                            color="primary"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleMarkReceived(request._id);
                            }}
                            sx={{ borderRadius: 2, mb: 2 }}
                          >
                            Mark as Received
                          </Button>
                        )}

                        {request.status === 'delivered' && isUserFromStore(request) && (
                          <Button
                            size="small"
                            variant="contained"
                            color="success"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleConfirmReceipt(request._id);
                            }}
                            sx={{ borderRadius: 2, mb: 2 }}
                          >
                            Confirm Receipt
                          </Button>
                        )}

                        {/* All Products List */}
                        <Typography variant="body2" fontWeight={500} gutterBottom>
                          All Products:
                        </Typography>
                        {request.items?.map((item, idx) => (
                          <Box key={idx} sx={{ mb: 1, p: 1, backgroundColor: 'rgba(0,0,0,0.02)', borderRadius: 1 }}>
                            <Typography variant="body2" fontWeight={500}>
                              {item.product?.name || 'N/A'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              SKU: {item.product?.sku || 'N/A'} | Quantity: {item.quantity}
                            </Typography>
                          </Box>
                        ))}
                      </Box>
                    </Collapse>
                  </CardContent>
                </Card>
              </Fade>
            </Grid>
              ))}
            </Grid>

            {/* Pagination for Cards */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                mt: 'auto',
                pt: 4,
                pb: 2,
              }}
            >
              <Paper
                sx={{
                  background: 'rgba(255, 255, 255, 0.98)',
                  backdropFilter: 'blur(20px)',
                  borderRadius: 3,
                  p: 3,
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                }}
              >
                <Stack
                  direction={{ xs: 'column', sm: 'row' }}
                  alignItems="center"
                  spacing={2}
                  sx={{ textAlign: 'center' }}
                >
                  <Typography variant="body2" color="text.secondary">
                    Showing {startIndex + 1}-{Math.min(endIndex, filteredRequests.length)} of {filteredRequests.length} transfers
                  </Typography>
                  <Pagination
                    count={totalPages}
                    page={currentPage}
                    onChange={handlePageChange}
                    color="primary"
                    size="large"
                    showFirstButton
                    showLastButton
                    sx={{
                      '& .MuiPaginationItem-root': {
                        fontWeight: 600,
                        fontSize: '1rem',
                        minWidth: '40px',
                        height: '40px',
                        '&.Mui-selected': {
                          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                          color: 'white',
                          boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                        },
                        '&:hover': {
                          backgroundColor: 'rgba(25, 118, 210, 0.1)',
                        },
                      },
                    }}
                  />
                </Stack>
              </Paper>
            </Box>
          </Box>
        ) : (
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            {/* Table View */}
            <Paper
            elevation={0}
            sx={{
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              border: '1px solid rgba(255, 255, 255, 0.3)',
              overflow: 'hidden',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
            }}
          >
          <Box sx={{ overflowX: 'auto' }}>
            <Box sx={{ minWidth: 800 }}>
              {/* Table Header */}
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 2fr 1fr 1.5fr 1.5fr 1.5fr 1fr',
                  gap: 2,
                  p: 2,
                  backgroundColor: 'rgba(102, 126, 234, 0.1)',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
                }}
              >
                {['Transfer ID', 'Products', 'Quantity', 'From Store', 'To Store', 'Status', 'Date'].map((header) => (
                  <Typography key={header} variant="subtitle2" fontWeight={600} color="primary">
                    {header}
                  </Typography>
                ))}
              </Box>

              {/* Table Body */}
              {paginatedRequests.map((request, index) => (
                <Box
                  key={request._id}
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 2fr 1fr 1.5fr 1.5fr 1.5fr 1fr',
                    gap: 2,
                    p: 2,
                    borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                    '&:hover': {
                      backgroundColor: 'rgba(102, 126, 234, 0.05)',
                    },
                  }}
                >
                  <Typography variant="body2" fontWeight={500}>
                    {request.transferId || 'N/A'}
                  </Typography>

                  <Box>
                    {request.items?.slice(0, 2).map((item, idx) => (
                      <Typography key={idx} variant="body2" sx={{ fontSize: '0.8rem', mb: 0.5 }}>
                        {item.product?.name || 'N/A'}
                      </Typography>
                    ))}
                    {request.items?.length > 2 && (
                      <Typography variant="caption" color="primary">
                        +{request.items.length - 2} more
                      </Typography>
                    )}
                  </Box>

                  <Box>
                    {request.items?.slice(0, 2).map((item, idx) => (
                      <Typography key={idx} variant="body2" sx={{ fontSize: '0.8rem', mb: 0.5 }}>
                        {item.quantity}
                      </Typography>
                    ))}
                  </Box>

                  <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                    {request.fromStore?.storeName || 'N/A'}
                  </Typography>

                  <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                    {request.toStore?.storeName || 'N/A'}
                  </Typography>

                  <Box>
                    {request.status === 'pending' && ALLOWED_ROLES.includes(userRole) ? (
                      <Stack direction="row" spacing={1}>
                        <Button
                          size="small"
                          variant="contained"
                          color="success"
                          onClick={() => handleApproveReject(request._id, 'approved')}
                          sx={{ fontSize: '0.7rem', py: 0.5 }}
                        >
                          Approve
                        </Button>
                        <Button
                          size="small"
                          variant="contained"
                          color="error"
                          onClick={() => handleApproveReject(request._id, 'rejected')}
                          sx={{ fontSize: '0.7rem', py: 0.5 }}
                        >
                          Reject
                        </Button>
                      </Stack>
                    ) : (
                      <Chip
                        label={statusConfig[request.status]?.label || request.status}
                        icon={statusConfig[request.status]?.icon}
                        size="small"
                        sx={{
                          backgroundColor: statusConfig[request.status]?.bgColor,
                          color: statusConfig[request.status]?.color,
                          fontWeight: 600,
                          fontSize: '0.7rem',
                        }}
                      />
                    )}
                  </Box>

                  <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                    {new Date(request.requestDate).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                    })}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>

            {/* Pagination for Table */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                p: 3,
                mt: 'auto',
                backgroundColor: 'rgba(255, 255, 255, 0.05)',
              }}
            >
              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                alignItems="center"
                spacing={2}
                sx={{ textAlign: 'center' }}
              >
                <Typography variant="body2" color="text.secondary">
                  Showing {startIndex + 1}-{Math.min(endIndex, filteredRequests.length)} of {filteredRequests.length} transfers
                </Typography>
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={handlePageChange}
                  color="primary"
                  size="large"
                  showFirstButton
                  showLastButton
                  sx={{
                    '& .MuiPaginationItem-root': {
                      fontWeight: 600,
                      fontSize: '1rem',
                      minWidth: '40px',
                      height: '40px',
                      '&.Mui-selected': {
                        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                        color: 'white',
                        boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                      },
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.1)',
                      },
                    },
                  }}
                />
              </Stack>
            </Box>
            </Paper>
          </Box>
        )}
        </Box>

        {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{
            width: '100%',
            borderRadius: 2,
            backdropFilter: 'blur(20px)',
            background: snackbarSeverity === 'success'
              ? 'rgba(76, 175, 80, 0.9)'
              : 'rgba(244, 67, 54, 0.9)',
            color: 'white',
          }}
        >
          {snackbarMessage}
        </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

export default ReceivedTransfers;