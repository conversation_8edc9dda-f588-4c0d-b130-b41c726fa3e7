/* eslint-disable no-unused-vars */
import { lazy, Suspense, useState, useEffect, useMemo } from 'react';
import {
  Container,
  Stack,
  Box,
  Typography,
  Button,
  Fab,
  useTheme,
  alpha,
  Skeleton,
  CircularProgress,
  Alert,
  Paper,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  useMediaQuery
} from '@mui/material';
import { Warning, SmartToy } from '@mui/icons-material';
import axios from 'axios';
import Cookies from 'js-cookie';
import { getApiUrl } from '../config';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveContainer } from '../hooks/useResponsive';

// Lazy load components
const DashboardStatsCards = lazy(() => import('../components/DashboardStatsCard'));
const InventorySearchFilter = lazy(() => import('../components/InventorySearchFilter'));
const InventoryTable = lazy(() => import('../components/InventoryTable'));
const StockUpdateDialog = lazy(() => import('../components/StockUpdateDialog'));
const BillGenerationDialog = lazy(() => import('../components/BillGenerationDialog')); 
const LowStockAlertsDialog = lazy(() => import('../components/LowStockAlerts'));
const ExpiryDialog = lazy(() => import('../components/ExpiryDialog'));
const ChatbotDialog = lazy(() => import('../components/ChatBotDialog'));
const MultipleBillDialog = lazy(() => import('../components/MultipleBillDialog'));

const Dashboard = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { isMobile, isTablet, isTouchDevice } = useResponsive();
  const { maxWidth, padding } = useResponsiveContainer();

  // State management
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState('all');
  const [tablePage, setTablePage] = useState(0);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [stockUpdateDialog, setStockUpdateDialog] = useState(false);
  const [newStockValue, setNewStockValue] = useState('');
  const [billDialog, setBillDialog] = useState(false);
  const [multipleBillDialog, setMultipleBillDialog] = useState(false);
  const [saleQuantity, setSaleQuantity] = useState('');
  const [vendorName, setVendorName] = useState('');
  const [paymentType, setPaymentType] = useState('paid');
  const [alertsDialog, setAlertsDialog] = useState(false);
  const [expiryDialog, setExpiryDialog] = useState(false);
  const [chatbotOpen, setChatbotOpen] = useState(false);
  const [userRequests, setUserRequests] = useState([]);
  const [loadingRequests, setLoadingRequests] = useState(false);
  const [requestError, setRequestError] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const userRole = Cookies.get('userRole');

  const itemsPerPage = 5;

  // Memoized derived states with debouncing
  const lowStockItems = useMemo(() => 
    inventory.filter(item => item.stock < item.lowStockThreshold),
    [inventory]
  );

  const totalValue = useMemo(() => 
    inventory.reduce((acc, item) => acc + (item.stock * item.price), 0),
    [inventory]
  );

  const filteredInventory = useMemo(() => {
    const searchTermLower = searchTerm.toLowerCase();
    return inventory.filter(item => {
      if (category !== 'all' && item.category !== category) return false;
      return !searchTerm || 
             item.name.toLowerCase().includes(searchTermLower) ||
             item.sku.toLowerCase().includes(searchTermLower);
    });
  }, [inventory, searchTerm, category]);

  const paginatedInventory = useMemo(() => 
    filteredInventory.slice(
      tablePage * itemsPerPage,
      (tablePage + 1) * itemsPerPage
    ),
    [filteredInventory, tablePage]
  );

  // Fetch Inventory with caching
  useEffect(() => {
    const fetchInventory = async () => {
      try {
        // const cachedData = sessionStorage.getItem('inventoryData');
        // if (cachedData) {
        //   setInventory(JSON.parse(cachedData));
        //   setLoading(false);
        //   return;
        // }

        const response = await fetch(getApiUrl('api/product/'));
        const result = await response.json();
        
        if (result.success) {
          const products = result.data.map(({ _id, name, sku, stock, lowStockThreshold, price, category }) => ({
            id: _id,
            name,
            sku, 
            stock,
            lowStockThreshold,
            price,
            category
          }));
          
          setInventory(products);
          sessionStorage.setItem('inventoryData', JSON.stringify(products));
        }
      } catch (error) {
        console.error('Error fetching inventory:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInventory();
  }, []);

  useEffect(() => {
    if (userRole === 'admin') {
      fetchUserRequests();
    }
  }, [userRole]);

  const fetchUserRequests = async () => {
    setLoadingRequests(true);
    setRequestError('');
    try {
      const res = await axios.get(getApiUrl('api/requests')); // You may need to adjust the endpoint
      setUserRequests(res.data.requests || []);
    } catch (err) {
      setRequestError('Failed to fetch user requests');
    } finally {
      setLoadingRequests(false);
    }
  };

  const handleApproveReject = async (id, status) => {
    try {
      await axios.put(getApiUrl(`api/approve-reject/${id}`), { status });
      setSuccessMsg(`Request ${status}`);
      fetchUserRequests();
    } catch (err) {
      setRequestError('Failed to update request status');
    }
  };

  const handleTableNextPage = () => {
    if (tablePage < Math.ceil(filteredInventory.length / itemsPerPage) - 1) {
      setTablePage(p => p + 1);
    }
  };

  const handleTablePrevPage = () => {
    if (tablePage > 0) {
      setTablePage(p => p - 1);
    }
  };

  const handleUpdateStock = async () => {
    if (!selectedProduct?.sku || !newStockValue) return;

    try {
      const response = await fetch(getApiUrl(`api/product/update/${selectedProduct.sku}`), {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ stock: +newStockValue })
      });

      if (response.ok) {
        setInventory(prev => prev.map(item => 
          item.sku === selectedProduct.sku ? {...item, stock: +newStockValue} : item
        ));
        setStockUpdateDialog(false);
        setSelectedProduct(null);
        setNewStockValue('');
        sessionStorage.removeItem('inventoryData');
      }
    } catch (error) {
      console.error('Error updating stock:', error);
    }
  };

  const handleGenerateBill = async () => {
    if (!selectedProduct?.sku || !saleQuantity || !vendorName || !paymentType) return;

    try {
      const billNumber = `BILL-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      const [billResponse, stockResponse] = await Promise.all([
        fetch(getApiUrl('api/bill/create'), {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            billNumber,
            productSku: selectedProduct.sku,
            quantity: +saleQuantity,
            totalAmount: selectedProduct.price * +saleQuantity,
            vendorName,
            paymentType
          })
        }),
        fetch(getApiUrl(`api/product/update/${selectedProduct.sku}`), {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            stock: selectedProduct.stock - +saleQuantity
          })
        })
      ]);

      if (billResponse.ok && stockResponse.ok) {
        setInventory(prev => prev.map(item => 
          item.sku === selectedProduct.sku 
            ? {...item, stock: item.stock - +saleQuantity} 
            : item
        ));
        setBillDialog(false);
        setSelectedProduct(null);
        setSaleQuantity('');
        setVendorName('');
        setPaymentType('paid');
        sessionStorage.removeItem('inventoryData');
      }
    } catch (error) {
      console.error('Error generating bill:', error);
    }
  };

  return (
    <Container maxWidth={maxWidth} sx={{ py: padding, minHeight: '100vh' }}>
      <Stack spacing={isMobile ? 2 : 3}>
        <Box sx={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          justifyContent: 'space-between',
          alignItems: isMobile ? 'flex-start' : 'center',
          gap: isMobile ? 2 : 0,
          minHeight: '48px'
        }}>
          <Typography
            variant={isMobile ? "h5" : "h4"}
            fontWeight="bold"
            color="primary"
            sx={{
              fontSize: isMobile ? '1.5rem' : '2rem',
              lineHeight: 1.2
            }}
          >
            {t('menu.dashboard')}
          </Typography>

          <Stack
            direction={isMobile ? "column" : "row"}
            spacing={isMobile ? 1 : 2}
            sx={{ width: isMobile ? '100%' : 'auto' }}
          >
            {loading ? (
              <Skeleton
                variant="rectangular"
                width={isMobile ? '100%' : 200}
                height={isMobile ? 44 : 48}
              />
            ) : (
              <>
                <Button
                  variant="soft"
                  color="warning"
                  startIcon={!isMobile && <Warning />}
                  onClick={() => setExpiryDialog(true)}
                  fullWidth={isMobile}
                  sx={{
                    borderRadius: isMobile ? 2 : 3,
                    px: isMobile ? 2 : 3,
                    py: isMobile ? 1.5 : 1.5,
                    bgcolor: alpha(theme.palette.error.main, 0.1),
                    color: theme.palette.error.dark,
                    minHeight: isTouchDevice ? '44px' : '40px',
                    fontSize: isMobile ? '0.875rem' : '1rem',
                    '&:hover': {
                      bgcolor: alpha(theme.palette.error.main, 0.2)
                    }
                  }}
                >
                  {isMobile && <Warning sx={{ mr: 1, fontSize: '1rem' }} />}
                  {t('dashboard.expiryStatus')}
                </Button>
                <Button
                  variant="soft"
                  color="warning"
                  startIcon={!isMobile && <Warning />}
                  onClick={() => setAlertsDialog(true)}
                  disabled={!lowStockItems.length}
                  fullWidth={isMobile}
                  sx={{
                    borderRadius: isMobile ? 2 : 3,
                    px: isMobile ? 2 : 3,
                    py: isMobile ? 1.5 : 1.5,
                    bgcolor: alpha(theme.palette.warning.main, 0.1),
                    color: theme.palette.warning.dark,
                    minHeight: isTouchDevice ? '44px' : '40px',
                    fontSize: isMobile ? '0.875rem' : '1rem',
                    '&:hover': {
                      bgcolor: alpha(theme.palette.warning.main, 0.2)
                    }
                  }}
                >
                  {isMobile && <Warning sx={{ mr: 1, fontSize: '1rem' }} />}
                  {t('dashboard.lowStock')} ({lowStockItems.length})
                </Button>
              </>
            )}
          </Stack>
        </Box>

        {loading ? (
          <Box sx={{ width: '100%' }}>
            <Skeleton variant="rectangular" width="100%" height={200} sx={{ mb: 2 }} />
            <Skeleton variant="rectangular" width="100%" height={400} />
          </Box>
        ) : (
          <Suspense fallback={<CircularProgress />}>
            <DashboardStatsCards 
              inventory={inventory}
              lowStockItems={lowStockItems}
              totalValue={totalValue}
              stockTrend={5.2}
            />

            <InventorySearchFilter 
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              category={category}
              setCategory={setCategory}
              setMultipleBillDialog={setMultipleBillDialog}
            />

            <InventoryTable 
              paginatedInventory={paginatedInventory}
              tablePage={tablePage}
              totalTablePages={Math.ceil(filteredInventory.length / itemsPerPage)}
              handleTablePrevPage={handleTablePrevPage}
              handleTableNextPage={handleTableNextPage}
              setSelectedProduct={setSelectedProduct}
              setStockUpdateDialog={setStockUpdateDialog}
              setBillDialog={setBillDialog}
              getStockPercentage={(stock, threshold) => (stock / threshold) * 100}
            />
          </Suspense>
        )}

        <Suspense fallback={null}>
          <StockUpdateDialog 
            stockUpdateDialog={stockUpdateDialog}
            selectedProduct={selectedProduct}
            newStockValue={newStockValue}
            setStockUpdateDialog={setStockUpdateDialog}
            setSelectedProduct={setSelectedProduct}
            setNewStockValue={setNewStockValue}
            handleUpdateStock={handleUpdateStock}
          />

          <BillGenerationDialog 
            billDialog={billDialog}
            selectedProduct={selectedProduct}
            saleQuantity={saleQuantity}
            vendorName={vendorName}
            setVendorName={setVendorName}
            paymentType={paymentType}
            setPaymentType={setPaymentType}
            setBillDialog={setBillDialog}
            setSelectedProduct={setSelectedProduct}
            setSaleQuantity={setSaleQuantity}
            handleGenerateBill={handleGenerateBill}
          />

          <MultipleBillDialog
            billDialog={multipleBillDialog}
            setBillDialog={setMultipleBillDialog}
            handleGenerateBill={handleGenerateBill}
          />

          <LowStockAlertsDialog 
            alertsDialog={alertsDialog}
            lowStockItems={lowStockItems}
            setAlertsDialog={setAlertsDialog}
            setSelectedProduct={setSelectedProduct}
            setStockUpdateDialog={setStockUpdateDialog}
          />

          <ExpiryDialog
            open={expiryDialog}
            onClose={() => setExpiryDialog(false)}
          />

          <ChatbotDialog 
            open={chatbotOpen}
            onClose={() => setChatbotOpen(false)}
          />
        </Suspense>

        <Fab
          color="primary"
          aria-label="chat"
          onClick={() => setChatbotOpen(true)}
          sx={{
            position: 'fixed',
            bottom: isMobile ? 16 : 20,
            right: isMobile ? 16 : 20,
            width: isTouchDevice ? 56 : 56,
            height: isTouchDevice ? 56 : 56,
            zIndex: theme.zIndex.speedDial
          }}
        >
          <SmartToy sx={{ fontSize: isMobile ? '1.25rem' : '1.5rem' }} />
        </Fab>

        {userRole === 'admin' && (
          <Box mt={6}>
            <Typography variant="h5" sx={{ mb: 2, fontWeight: 700 }}>
              Pending User Access Requests
            </Typography>
            {loadingRequests ? (
              <Typography>Loading...</Typography>
            ) : requestError ? (
              <Alert severity="error">{requestError}</Alert>
            ) : (
              <Paper sx={{ p: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('dashboard.businessName')}</TableCell>
                      <TableCell>{t('dashboard.email')}</TableCell>
                      <TableCell>{t('dashboard.phone')}</TableCell>
                      <TableCell>{t('dashboard.industry')}</TableCell>
                      <TableCell>{t('dashboard.description')}</TableCell>
                      <TableCell>{t('dashboard.status')}</TableCell>
                      <TableCell>{t('dashboard.actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {userRequests.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} align="center">{t('dashboard.noPendingRequests')}</TableCell>
                      </TableRow>
                    ) : userRequests.map((req) => (
                      <TableRow key={req._id}>
                        <TableCell>{req.businessName}</TableCell>
                        <TableCell>{req.email}</TableCell>
                        <TableCell>{req.phone}</TableCell>
                        <TableCell>{req.industry}</TableCell>
                        <TableCell>{req.description}</TableCell>
                        <TableCell>{req.status}</TableCell>
                        <TableCell>
                          <Button
                            color="success"
                            size="small"
                            onClick={() => handleApproveReject(req._id, 'approved')}
                            disabled={req.status === 'approved'}
                          >
                            {t('dashboard.approve')}
                          </Button>
                          <Button
                            color="error"
                            size="small"
                            onClick={() => handleApproveReject(req._id, 'rejected')}
                            disabled={req.status === 'rejected'}
                          >
                            {t('dashboard.reject')}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {successMsg && <Alert severity="success" sx={{ mt: 2 }}>{successMsg}</Alert>}
              </Paper>
            )}
          </Box>
        )}
      </Stack>
    </Container>
  );
};

export default Dashboard;