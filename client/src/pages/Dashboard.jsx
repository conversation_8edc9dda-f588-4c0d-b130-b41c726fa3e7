/* eslint-disable no-unused-vars */
import { lazy, Suspense, useState, useEffect, useMemo } from 'react';
import {
  Container,
  Stack,
  Box,
  Typography,
  Button,
  Fab,
  useTheme,
  alpha,
  Skeleton,
  CircularProgress,
  Alert,
  Paper,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Grid,
  Card,
  CardContent,
  Avatar,
  Fade,
  Zoom,
  Tooltip,
  Chip,
  Divider,
  IconButton
} from '@mui/material';
import {
  Warning,
  SmartToy,
  Dashboard as DashboardIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  Inventory as InventoryIcon,
  Assessment as AssessmentIcon,
  Notifications as NotificationsIcon,
  Chat as ChatIcon,
  Support as SupportIcon,
  Help as HelpIcon,
  QuestionAnswer as QuestionAnswerIcon,
  Android as AndroidIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import axios from 'axios';
import Cookies from 'js-cookie';
import { getApiUrl } from '../config';
import { useTranslation } from 'react-i18next';

// Lazy load components
const DashboardStatsCards = lazy(() => import('../components/DashboardStatsCard'));
const InventorySearchFilter = lazy(() => import('../components/InventorySearchFilter'));
const InventoryTable = lazy(() => import('../components/InventoryTable'));
const StockUpdateDialog = lazy(() => import('../components/StockUpdateDialog'));
const BillGenerationDialog = lazy(() => import('../components/BillGenerationDialog')); 
const LowStockAlertsDialog = lazy(() => import('../components/LowStockAlerts'));
const ExpiryDialog = lazy(() => import('../components/ExpiryDialog'));
const ChatbotDialog = lazy(() => import('../components/ChatBotDialog'));
const MultipleBillDialog = lazy(() => import('../components/MultipleBillDialog'));

const Dashboard = () => {
  const theme = useTheme();
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState('all');
  const [tablePage, setTablePage] = useState(0);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [stockUpdateDialog, setStockUpdateDialog] = useState(false);
  const [newStockValue, setNewStockValue] = useState('');
  const [billDialog, setBillDialog] = useState(false);
  const [multipleBillDialog, setMultipleBillDialog] = useState(false);
  const [saleQuantity, setSaleQuantity] = useState('');
  const [vendorName, setVendorName] = useState('');
  const [paymentType, setPaymentType] = useState('paid');
  const [alertsDialog, setAlertsDialog] = useState(false);
  const [expiryDialog, setExpiryDialog] = useState(false);
  const [chatbotOpen, setChatbotOpen] = useState(false);
  const [userRequests, setUserRequests] = useState([]);
  const [loadingRequests, setLoadingRequests] = useState(false);
  const [requestError, setRequestError] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const userRole = Cookies.get('userRole');
  const { t } = useTranslation();

  const itemsPerPage = 5;

  // Memoized derived states with debouncing
  const lowStockItems = useMemo(() => 
    inventory.filter(item => item.stock < item.lowStockThreshold),
    [inventory]
  );

  const totalValue = useMemo(() => 
    inventory.reduce((acc, item) => acc + (item.stock * item.price), 0),
    [inventory]
  );

  const filteredInventory = useMemo(() => {
    const searchTermLower = searchTerm.toLowerCase();
    return inventory.filter(item => {
      if (category !== 'all' && item.category !== category) return false;
      return !searchTerm || 
             item.name.toLowerCase().includes(searchTermLower) ||
             item.sku.toLowerCase().includes(searchTermLower);
    });
  }, [inventory, searchTerm, category]);

  const paginatedInventory = useMemo(() => 
    filteredInventory.slice(
      tablePage * itemsPerPage,
      (tablePage + 1) * itemsPerPage
    ),
    [filteredInventory, tablePage]
  );

  // Fetch Inventory with caching
  useEffect(() => {
    const fetchInventory = async () => {
      try {
        // const cachedData = sessionStorage.getItem('inventoryData');
        // if (cachedData) {
        //   setInventory(JSON.parse(cachedData));
        //   setLoading(false);
        //   return;
        // }

        const response = await fetch(getApiUrl('api/product/'));
        const result = await response.json();
        
        if (result.success) {
          const products = result.data.map(({ _id, name, sku, stock, lowStockThreshold, price, category }) => ({
            id: _id,
            name,
            sku, 
            stock,
            lowStockThreshold,
            price,
            category
          }));
          
          setInventory(products);
          sessionStorage.setItem('inventoryData', JSON.stringify(products));
        }
      } catch (error) {
        console.error('Error fetching inventory:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInventory();
  }, []);

  useEffect(() => {
    if (userRole === 'admin') {
      fetchUserRequests();
    }
  }, [userRole]);

  const fetchUserRequests = async () => {
    setLoadingRequests(true);
    setRequestError('');
    try {
      const res = await axios.get(getApiUrl('api/requests')); // You may need to adjust the endpoint
      setUserRequests(res.data.requests || []);
    } catch (err) {
      setRequestError('Failed to fetch user requests');
    } finally {
      setLoadingRequests(false);
    }
  };

  const handleApproveReject = async (id, status) => {
    try {
      await axios.put(getApiUrl(`api/approve-reject/${id}`), { status });
      setSuccessMsg(`Request ${status}`);
      fetchUserRequests();
    } catch (err) {
      setRequestError('Failed to update request status');
    }
  };

  const handleTableNextPage = () => {
    if (tablePage < Math.ceil(filteredInventory.length / itemsPerPage) - 1) {
      setTablePage(p => p + 1);
    }
  };

  const handleTablePrevPage = () => {
    if (tablePage > 0) {
      setTablePage(p => p - 1);
    }
  };

  const handleUpdateStock = async () => {
    if (!selectedProduct?.sku || !newStockValue) return;

    try {
      const response = await fetch(getApiUrl(`api/product/update/${selectedProduct.sku}`), {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ stock: +newStockValue })
      });

      if (response.ok) {
        setInventory(prev => prev.map(item => 
          item.sku === selectedProduct.sku ? {...item, stock: +newStockValue} : item
        ));
        setStockUpdateDialog(false);
        setSelectedProduct(null);
        setNewStockValue('');
        sessionStorage.removeItem('inventoryData');
      }
    } catch (error) {
      console.error('Error updating stock:', error);
    }
  };

  const handleGenerateBill = async () => {
    if (!selectedProduct?.sku || !saleQuantity || !vendorName || !paymentType) return;

    try {
      const billNumber = `BILL-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      const [billResponse, stockResponse] = await Promise.all([
        fetch(getApiUrl('api/bill/create'), {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            billNumber,
            productSku: selectedProduct.sku,
            quantity: +saleQuantity,
            totalAmount: selectedProduct.price * +saleQuantity,
            vendorName,
            paymentType
          })
        }),
        fetch(getApiUrl(`api/product/update/${selectedProduct.sku}`), {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            stock: selectedProduct.stock - +saleQuantity
          })
        })
      ]);

      if (billResponse.ok && stockResponse.ok) {
        setInventory(prev => prev.map(item => 
          item.sku === selectedProduct.sku 
            ? {...item, stock: item.stock - +saleQuantity} 
            : item
        ));
        setBillDialog(false);
        setSelectedProduct(null);
        setSaleQuantity('');
        setVendorName('');
        setPaymentType('paid');
        sessionStorage.removeItem('inventoryData');
      }
    } catch (error) {
      console.error('Error generating bill:', error);
    }
  };

  return (
    <Box
      sx={{
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        height: '100%',
        padding: 3,
        overflow: 'auto',
      }}
    >
      <Container maxWidth="xl">
        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 4,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight={700}
                sx={{
                  background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                  fontSize: { xs: '1.8rem', md: '2.5rem' },
                }}
              >
                📊 {t('menu.dashboard')}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Real-time inventory insights and business analytics
              </Typography>
            </Box>

            <Stack direction="row" spacing={2}>
              {loading ? (
                <Skeleton variant="rectangular" width={200} height={48} />
              ) : (
                <>
                  <Tooltip title="Refresh Dashboard">
                    <IconButton
                      onClick={() => window.location.reload()}
                      sx={{
                        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                        color: 'white',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                          transform: 'scale(1.05)',
                        },
                        transition: 'all 0.2s ease',
                      }}
                    >
                      <RefreshIcon />
                    </IconButton>
                  </Tooltip>

                  <Button
                    variant="outlined"
                    startIcon={<Warning />}
                    onClick={() => setExpiryDialog(true)}
                    sx={{
                      borderRadius: 3,
                      px: 3,
                      py: 1.5,
                      borderColor: '#f44336',
                      color: '#f44336',
                      '&:hover': {
                        borderColor: '#d32f2f',
                        backgroundColor: 'rgba(244, 67, 54, 0.1)',
                      }
                    }}
                  >
                    {t('dashboard.expiryStatus')}
                  </Button>

                  <Button
                    variant="outlined"
                    startIcon={<NotificationsIcon />}
                    onClick={() => setAlertsDialog(true)}
                    disabled={!lowStockItems.length}
                    sx={{
                      borderRadius: 3,
                      px: 3,
                      py: 1.5,
                      borderColor: '#ff9800',
                      color: '#ff9800',
                      '&:hover': {
                        borderColor: '#f57c00',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                      },
                      '&:disabled': {
                        borderColor: '#bdbdbd',
                        color: '#bdbdbd',
                      }
                    }}
                  >
                    {t('dashboard.lowStock')} ({lowStockItems.length})
                  </Button>
                </>
              )}
            </Stack>
          </Stack>
        </Paper>

        {/* Content Area */}
        {loading ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 8,
            }}
          >
            <CircularProgress
              size={60}
              thickness={4}
              sx={{ color: 'white' }}
            />
            <Typography variant="h6" sx={{ mt: 2, color: 'white', fontWeight: 500 }}>
              Loading dashboard...
            </Typography>
          </Box>
        ) : (
          <Suspense fallback={
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                py: 8,
              }}
            >
              <CircularProgress
                size={60}
                thickness={4}
                sx={{ color: 'white' }}
              />
              <Typography variant="h6" sx={{ mt: 2, color: 'white', fontWeight: 500 }}>
                Loading components...
              </Typography>
            </Box>
          }>
            <Stack spacing={3}>
              <DashboardStatsCards
                inventory={inventory}
                lowStockItems={lowStockItems}
                totalValue={totalValue}
                stockTrend={5.2}
              />

              <InventorySearchFilter
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                category={category}
                setCategory={setCategory}
                setMultipleBillDialog={setMultipleBillDialog}
              />

              <InventoryTable
                paginatedInventory={paginatedInventory}
                tablePage={tablePage}
                totalTablePages={Math.ceil(filteredInventory.length / itemsPerPage)}
                handleTablePrevPage={handleTablePrevPage}
                handleTableNextPage={handleTableNextPage}
                setSelectedProduct={setSelectedProduct}
                setStockUpdateDialog={setStockUpdateDialog}
                setBillDialog={setBillDialog}
                getStockPercentage={(stock, threshold) => (stock / threshold) * 100}
              />
            </Stack>
          </Suspense>
        )}

        <Suspense fallback={null}>
          <StockUpdateDialog 
            stockUpdateDialog={stockUpdateDialog}
            selectedProduct={selectedProduct}
            newStockValue={newStockValue}
            setStockUpdateDialog={setStockUpdateDialog}
            setSelectedProduct={setSelectedProduct}
            setNewStockValue={setNewStockValue}
            handleUpdateStock={handleUpdateStock}
          />

          <BillGenerationDialog 
            billDialog={billDialog}
            selectedProduct={selectedProduct}
            saleQuantity={saleQuantity}
            vendorName={vendorName}
            setVendorName={setVendorName}
            paymentType={paymentType}
            setPaymentType={setPaymentType}
            setBillDialog={setBillDialog}
            setSelectedProduct={setSelectedProduct}
            setSaleQuantity={setSaleQuantity}
            handleGenerateBill={handleGenerateBill}
          />

          <MultipleBillDialog
            billDialog={multipleBillDialog}
            setBillDialog={setMultipleBillDialog}
            handleGenerateBill={handleGenerateBill}
          />

          <LowStockAlertsDialog 
            alertsDialog={alertsDialog}
            lowStockItems={lowStockItems}
            setAlertsDialog={setAlertsDialog}
            setSelectedProduct={setSelectedProduct}
            setStockUpdateDialog={setStockUpdateDialog}
          />

          <ExpiryDialog
            open={expiryDialog}
            onClose={() => setExpiryDialog(false)}
          />

          <ChatbotDialog 
            open={chatbotOpen}
            onClose={() => setChatbotOpen(false)}
          />
        </Suspense>



        {/* Admin Section */}
        {userRole === 'admin' && (
          <Paper
            elevation={0}
            sx={{
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              p: 4,
              mt: 3,
              border: '1px solid rgba(255, 255, 255, 0.3)',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2} mb={3}>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                  width: 40,
                  height: 40,
                }}
              >
                <AssessmentIcon />
              </Avatar>
              <Typography variant="h5" fontWeight={700} color="#1976d2">
                Pending User Access Requests
              </Typography>
            </Stack>

            {loadingRequests ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : requestError ? (
              <Alert
                severity="error"
                sx={{
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(211, 47, 47, 0.05) 100%)',
                }}
              >
                {requestError}
              </Alert>
            ) : (
              <Paper
                elevation={0}
                sx={{
                  background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(21, 101, 192, 0.01) 100%)',
                  border: '1px solid rgba(25, 118, 210, 0.1)',
                  borderRadius: 3,
                  overflow: 'hidden'
                }}
              >
                <Table>
                  <TableHead>
                    <TableRow
                      sx={{
                        background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
                      }}
                    >
                      <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>{t('dashboard.businessName')}</TableCell>
                      <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>{t('dashboard.email')}</TableCell>
                      <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>{t('dashboard.phone')}</TableCell>
                      <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>{t('dashboard.industry')}</TableCell>
                      <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>{t('dashboard.description')}</TableCell>
                      <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>{t('dashboard.status')}</TableCell>
                      <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>{t('dashboard.actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {userRequests.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} sx={{ textAlign: 'center', py: 4 }}>
                          <Typography variant="h6" color="text.secondary" gutterBottom>
                            📋 {t('dashboard.noPendingRequests')}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            All user access requests have been processed
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : userRequests.map((req, index) => (
                      <Fade in timeout={300 + index * 50} key={req._id}>
                        <TableRow
                          hover
                          sx={{
                            '&:hover': {
                              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(21, 101, 192, 0.02) 100%)',
                            },
                            transition: 'all 0.2s ease',
                          }}
                        >
                          <TableCell>
                            <Typography variant="body2" fontWeight={600}>
                              {req.businessName}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {req.email}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {req.phone}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={req.industry}
                              size="small"
                              sx={{
                                backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                color: '#1976d2',
                                fontWeight: 600,
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {req.description}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={req.status}
                              size="small"
                              color={
                                req.status === 'approved' ? 'success' :
                                req.status === 'rejected' ? 'error' : 'warning'
                              }
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={1}>
                              <Button
                                variant="outlined"
                                color="success"
                                size="small"
                                onClick={() => handleApproveReject(req._id, 'approved')}
                                disabled={req.status === 'approved'}
                                sx={{
                                  borderRadius: 2,
                                  '&:hover': {
                                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                                  },
                                }}
                              >
                                {t('dashboard.approve')}
                              </Button>
                              <Button
                                variant="outlined"
                                color="error"
                                size="small"
                                onClick={() => handleApproveReject(req._id, 'rejected')}
                                disabled={req.status === 'rejected'}
                                sx={{
                                  borderRadius: 2,
                                  '&:hover': {
                                    backgroundColor: 'rgba(244, 67, 54, 0.1)',
                                  },
                                }}
                              >
                                {t('dashboard.reject')}
                              </Button>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      </Fade>
                    ))}
                  </TableBody>
                </Table>
                {successMsg && (
                  <Alert
                    severity="success"
                    sx={{
                      mt: 2,
                      mx: 2,
                      mb: 2,
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(56, 142, 60, 0.05) 100%)',
                    }}
                  >
                    {successMsg}
                  </Alert>
                )}
              </Paper>
            )}
          </Paper>
        )}
        {/* AI Assistant Floating Action Button */}
        <Tooltip
          title="🤖 Ask AI Assistant"
          placement="left"
          sx={{
            '& .MuiTooltip-tooltip': {
              backgroundColor: 'rgba(25, 118, 210, 0.9)',
              color: 'white',
              fontSize: '0.875rem',
              fontWeight: 600,
              borderRadius: 2,
              backdropFilter: 'blur(10px)',
            },
          }}
        >
          <Fab
            color="primary"
            aria-label="Ask AI Assistant for help"
            onClick={() => setChatbotOpen(true)}
            sx={{
              position: 'fixed',
              bottom: 24,
              right: 24,
              width: 72,
              height: 72,
              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',
              border: '2px solid rgba(255, 255, 255, 0.2)',
              '&:hover': {
                background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                transform: 'scale(1.15) rotate(5deg)',
                boxShadow: '0 16px 48px rgba(25, 118, 210, 0.5)',
                border: '2px solid rgba(255, 255, 255, 0.4)',
              },
              transition: 'all 0.3s ease',
              zIndex: 1000,
              '&::before': {
                content: '""',
                position: 'absolute',
                top: -3,
                left: -3,
                right: -3,
                bottom: -3,
                background: 'linear-gradient(135deg, #42a5f5, #1976d2, #0d47a1)',
                borderRadius: '50%',
                zIndex: -1,
                opacity: 0,
                animation: 'pulse 2s infinite',
                transition: 'opacity 0.3s ease',
              },
              '&:hover::before': {
                opacity: 0.4,
              },
              '@keyframes pulse': {
                '0%': {
                  transform: 'scale(1)',
                  opacity: 0.4,
                },
                '50%': {
                  transform: 'scale(1.1)',
                  opacity: 0.2,
                },
                '100%': {
                  transform: 'scale(1)',
                  opacity: 0.4,
                },
              },
            }}
          >
            <AndroidIcon
              sx={{
                fontSize: 38,
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))',
                transition: 'all 0.3s ease',
                '&:hover': {
                  filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))',
                },
              }}
            />
          </Fab>
        </Tooltip>
      </Container>
    </Box>
  );
};

export default Dashboard;