import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  MenuItem,
  Select,
  InputLabel,
  FormControl,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Container,
  Stack,
  Avatar,
  Fade,
  Zoom,
  Tooltip,
  Badge,
  Card,
  CardContent,
  Chip,
  Snackbar,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Save as SaveIcon,
  Storefront as StoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  Map as MapIcon,
  TrendingUp as TrendingUpIcon,
  Store as StoreIconAlt,
  Category as CategoryIcon
} from '@mui/icons-material';
import { getApiUrl } from '../config';

const StoreRegistrationPage = () => {
  const [stores, setStores] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [currentStore, setCurrentStore] = useState({
    _id: null,
    storeName: '',
    storeLocation: '',
    locationType: '',
    address: '',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  // Helper function to show snackbar
  const showSnackbar = (message, severity = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  // Fetch stores from backend on component mount
  useEffect(() => {
    fetchStores();
  }, []);

  const fetchStores = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('api/store'));
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setStores(data);
      showSnackbar(`Loaded ${data.length} stores successfully`);
    } catch (error) {
      console.error('Error fetching stores:', error);
      showSnackbar('Error loading stores', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenDialog = (store = null) => {
    if (store) {
      setCurrentStore(store);
    } else {
      setCurrentStore({ _id: null, storeName: '', storeLocation: '', locationType: '', address: '' });
    }
    setOpenDialog(true);
  };
  const handleCloseDialog = () => setOpenDialog(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentStore({ ...currentStore, [name]: value });
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Filter stores based on search term
  const filteredStores = stores.filter(store =>
    store.storeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    store.storeLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
    store.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSaveStore = async () => {
    try {
      let response;
      const isEditing = currentStore._id;

      if (isEditing) {
        response = await fetch(getApiUrl(`api/store/${currentStore._id}`), {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(currentStore),
        });
      } else {
        response = await fetch(getApiUrl('api/store/add'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(currentStore),
        });
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const savedStore = await response.json();

      if (isEditing) {
        setStores(stores.map(store => store._id === savedStore._id ? savedStore : store));
        showSnackbar('Store updated successfully!');
      } else {
        setStores([...stores, savedStore]);
        showSnackbar('Store added successfully!');
      }

      handleCloseDialog();
    } catch (error) {
      console.error('Error saving store:', error);
      showSnackbar('Error saving store', 'error');
    }
  };

  const handleDeleteStore = async (_id) => {
    if (!window.confirm('Are you sure you want to delete this store?')) {
      return;
    }

    try {
      const response = await fetch(getApiUrl(`api/store/${_id}`), {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      setStores(stores.filter(store => store._id !== _id));
      showSnackbar('Store deleted successfully!');
    } catch (error) {
      console.error('Error deleting store:', error);
      showSnackbar('Error deleting store', 'error');
    }
  };

  // Export functionality
  const handleExportList = () => {
    const headers = ['Store Name', 'Location', 'Type', 'Address'];
    const csvData = [
      headers,
      ...filteredStores.map(store => [
        store.storeName,
        store.storeLocation,
        store.locationType,
        store.address
      ])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'stores_list.csv';
    a.click();
    window.URL.revokeObjectURL(url);
    showSnackbar('Store list exported successfully!');
  };

  // Calculate statistics
  const stats = {
    total: stores.length,
    stores: stores.filter(store => store.locationType === 'store').length,
    paintingYards: stores.filter(store => store.locationType === 'paintingyard').length,
    subcontractors: stores.filter(store => store.locationType === 'subcontractor').length,
  };

  return (
    <Box
      sx={{
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        height: '100%',
        padding: 3,
        overflow: 'auto',
      }}
    >
      <Container maxWidth="xl">
        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 4,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight={700}
                sx={{
                  background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                  fontSize: { xs: '1.8rem', md: '2.5rem' },
                }}
              >
                🏪 Store Registration
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage your store locations and business units with real-time tracking
              </Typography>
            </Box>

            <Stack direction="row" spacing={2}>
              <Tooltip title="Export Store List">
                <IconButton
                  onClick={handleExportList}
                  sx={{
                    background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1e88e5 0%, #1565c0 100%)',
                      transform: 'scale(1.05)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <DownloadIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={fetchStores}
                  sx={{
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                      transform: 'scale(1.05)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>

              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenDialog()}
                sx={{
                  borderRadius: 3,
                  textTransform: 'none',
                  px: 3,
                  py: 1.5,
                  background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                  boxShadow: '0 4px 20px rgba(76, 175, 80, 0.3)',
                  fontWeight: 600,
                  '&:hover': {
                    background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 25px rgba(76, 175, 80, 0.4)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Add New Store
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {/* Statistics Dashboard */}
        <Grid container spacing={3} mb={3}>
          {[
            {
              label: 'Total Locations',
              value: stats.total,
              icon: <BusinessIcon />,
              gradient: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              change: '+12%'
            },
            {
              label: 'Stores',
              value: stats.stores,
              icon: <StoreIcon />,
              gradient: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
              change: '+8%'
            },
            {
              label: 'Painting Yards',
              value: stats.paintingYards,
              icon: <CategoryIcon />,
              gradient: 'linear-gradient(135deg, #1e88e5 0%, #1976d2 100%)',
              change: '+15%'
            },
            {
              label: 'Subcontractors',
              value: stats.subcontractors,
              icon: <TrendingUpIcon />,
              gradient: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%)',
              change: '+5%'
            },
          ].map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={stat.label}>
              <Zoom in timeout={300 + index * 100}>
                <Card
                  sx={{
                    background: 'rgba(255, 255, 255, 0.98)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    borderRadius: 4,
                    overflow: 'hidden',
                    position: 'relative',
                    boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 40px rgba(25, 118, 210, 0.2)',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 4,
                      background: stat.gradient,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="h4" fontWeight={700} color="#1976d2">
                          {stat.value}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" fontWeight={500}>
                          {stat.label}
                        </Typography>
                        <Chip
                          label={stat.change}
                          size="small"
                          sx={{
                            mt: 1,
                            backgroundColor: stat.change.startsWith('+') ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
                            color: stat.change.startsWith('+') ? '#4caf50' : '#f44336',
                            fontWeight: 600,
                          }}
                        />
                      </Box>
                      <Avatar
                        sx={{
                          background: stat.gradient,
                          color: 'white',
                          width: 64,
                          height: 64,
                          boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                        }}
                      >
                        {stat.icon}
                      </Avatar>
                    </Stack>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>

        {/* Search Section */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 3,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
          }}
        >
          <TextField
            fullWidth
            placeholder="Search stores by name, location, or address..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />,
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 3,
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#1976d2',
                },
              },
            }}
          />
        </Paper>

        {/* Content Area */}
        {isLoading ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 8,
            }}
          >
            <CircularProgress
              size={60}
              thickness={4}
              sx={{ color: 'white' }}
            />
            <Typography variant="h6" sx={{ mt: 2, color: 'white', fontWeight: 500 }}>
              Loading stores...
            </Typography>
          </Box>
        ) : filteredStores.length === 0 ? (
          <Paper
            sx={{
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              p: 6,
              textAlign: 'center',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
            }}
          >
            <Typography variant="h6" color="text.secondary" gutterBottom>
              🏪 No stores found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {searchTerm
                ? 'Try adjusting your search terms to see more results'
                : 'No stores have been registered yet. Add your first store to get started!'
              }
            </Typography>
          </Paper>
        ) : (
          <Grid container spacing={3}>
            {filteredStores.map((store, index) => (
              <Grid item xs={12} sm={6} md={4} key={store._id}>
                <Fade in timeout={300 + index * 100}>
                  <Card
                    sx={{
                      background: 'rgba(255, 255, 255, 0.98)',
                      backdropFilter: 'blur(20px)',
                      borderRadius: 4,
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      transition: 'all 0.3s ease',
                      cursor: 'pointer',
                      boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 20px 60px rgba(25, 118, 210, 0.25)',
                      },
                    }}
                  >
                    <CardContent sx={{ p: 3 }}>
                      <Stack spacing={2}>
                        {/* Header */}
                        <Stack direction="row" alignItems="center" justifyContent="space-between">
                          <Avatar
                            sx={{
                              width: 56,
                              height: 56,
                              background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                              fontSize: '1.5rem',
                              fontWeight: 700,
                            }}
                          >
                            {store.storeName.charAt(0)}
                          </Avatar>
                          <Chip
                            label={store.locationType}
                            size="small"
                            sx={{
                              backgroundColor: store.locationType === 'store'
                                ? 'rgba(76, 175, 80, 0.1)'
                                : store.locationType === 'paintingyard'
                                ? 'rgba(255, 152, 0, 0.1)'
                                : 'rgba(25, 118, 210, 0.1)',
                              color: store.locationType === 'store'
                                ? '#4caf50'
                                : store.locationType === 'paintingyard'
                                ? '#ff9800'
                                : '#1976d2',
                              fontWeight: 600,
                              textTransform: 'capitalize',
                            }}
                          />
                        </Stack>

                        {/* Store Info */}
                        <Box>
                          <Typography variant="h6" fontWeight={700} color="#1976d2" gutterBottom>
                            {store.storeName}
                          </Typography>
                          <Stack direction="row" alignItems="center" spacing={1} mb={1}>
                            <LocationIcon fontSize="small" color="primary" />
                            <Typography variant="body2" color="text.secondary">
                              {store.storeLocation}
                            </Typography>
                          </Stack>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <MapIcon fontSize="small" color="primary" />
                            <Typography variant="body2" color="text.secondary">
                              {store.address}
                            </Typography>
                          </Stack>
                        </Box>

                        {/* Actions */}
                        <Stack direction="row" spacing={1} justifyContent="center" pt={1}>
                          <Tooltip title="Edit Store">
                            <Button
                              variant="outlined"
                              startIcon={<EditIcon />}
                              onClick={() => handleOpenDialog(store)}
                              sx={{
                                borderRadius: 2,
                                borderColor: '#1976d2',
                                color: '#1976d2',
                                '&:hover': {
                                  backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                  borderColor: '#1565c0',
                                },
                              }}
                            >
                              Edit
                            </Button>
                          </Tooltip>
                          <Tooltip title="Delete Store">
                            <Button
                              variant="outlined"
                              startIcon={<DeleteIcon />}
                              onClick={() => handleDeleteStore(store._id)}
                              color="error"
                              sx={{
                                borderRadius: 2,
                                '&:hover': {
                                  backgroundColor: 'rgba(244, 67, 54, 0.1)',
                                },
                              }}
                            >
                              Delete
                            </Button>
                          </Tooltip>
                        </Stack>
                      </Stack>
                    </CardContent>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Store Dialog */}
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 4,
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              boxShadow: '0 20px 60px rgba(25, 118, 210, 0.3)',
            }
          }}
        >
          <DialogTitle
            sx={{
              fontWeight: 700,
              color: '#1976d2',
              pb: 1,
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
              borderRadius: '16px 16px 0 0',
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                  width: 40,
                  height: 40,
                }}
              >
                <StoreIcon />
              </Avatar>
              <Typography variant="h6" fontWeight={700}>
                {currentStore._id ? 'Edit Store Location' : 'Add New Store Location'}
              </Typography>
            </Stack>
          </DialogTitle>

          <DialogContent sx={{ pt: 3, pb: 2 }}>
            <Stack spacing={3}>
              <TextField
                autoFocus
                name="storeName"
                label="Store Name"
                type="text"
                fullWidth
                variant="outlined"
                value={currentStore.storeName}
                onChange={handleInputChange}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    },
                  },
                }}
              />

              <TextField
                name="storeLocation"
                label="Location (e.g., City/Area)"
                type="text"
                fullWidth
                variant="outlined"
                value={currentStore.storeLocation}
                onChange={handleInputChange}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    },
                  },
                }}
              />

              <FormControl fullWidth variant="outlined">
                <InputLabel>Location Type</InputLabel>
                <Select
                  name="locationType"
                  value={currentStore.locationType}
                  onChange={handleInputChange}
                  label="Location Type"
                  sx={{
                    borderRadius: 3,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    },
                  }}
                >
                  <MenuItem value="">
                    <em>Select Type</em>
                  </MenuItem>
                  <MenuItem value="store">
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <StoreIcon fontSize="small" />
                      <span>Store</span>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="paintingyard">
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <CategoryIcon fontSize="small" />
                      <span>Painting Yard</span>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="subcontractor">
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <BusinessIcon fontSize="small" />
                      <span>Subcontractor</span>
                    </Stack>
                  </MenuItem>
                </Select>
              </FormControl>

              <TextField
                name="address"
                label="Full Address"
                type="text"
                fullWidth
                variant="outlined"
                multiline
                rows={3}
                value={currentStore.address}
                onChange={handleInputChange}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    },
                  },
                }}
              />
            </Stack>
          </DialogContent>

          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleCloseDialog}
              variant="outlined"
              sx={{
                fontWeight: 600,
                borderRadius: 3,
                px: 3,
                borderColor: '#bdbdbd',
                color: '#757575',
                '&:hover': {
                  borderColor: '#9e9e9e',
                  backgroundColor: 'rgba(189, 189, 189, 0.1)',
                },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveStore}
              variant="contained"
              sx={{
                fontWeight: 700,
                borderRadius: 3,
                px: 4,
                background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                  boxShadow: '0 6px 25px rgba(25, 118, 210, 0.4)',
                },
              }}
            >
              {currentStore._id ? 'Save Changes' : 'Add Store'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={() => setSnackbarOpen(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setSnackbarOpen(false)}
            severity={snackbarSeverity}
            sx={{
              width: '100%',
              borderRadius: 3,
              backdropFilter: 'blur(20px)',
              background: snackbarSeverity === 'success'
                ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.95) 0%, rgba(56, 142, 60, 0.95) 100%)'
                : 'linear-gradient(135deg, rgba(244, 67, 54, 0.95) 0%, rgba(211, 47, 47, 0.95) 100%)',
              color: 'white',
              fontWeight: 600,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
            }}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

export default StoreRegistrationPage; 