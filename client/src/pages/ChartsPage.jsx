/* eslint-disable no-unused-vars */
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  useTheme,
  LinearProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material';
import { 
  BarChart, Bar, 
  XAxis, YAxis, 
  CartesianGrid, 
  Tooltip as ChartTooltip, 
  ResponsiveContainer, 
  Legend,
  Line,
  ComposedChart,
  Cell
} from 'recharts';
import { Fullscreen } from '@mui/icons-material';
import { getApiUrl } from '../config';
import { ResponsiveContainer as ResponsiveLayoutContainer } from '../components/ResponsiveLayout';
import ResponsiveDialog from '../components/ResponsiveDialog';
import { useResponsive } from '../hooks/useResponsive';

const ChartsPage = () => {
  const theme = useTheme();
  const { isMobile, isTablet, isTouchDevice } = useResponsive();

  const [productData, setProductData] = useState([]);
  const [billData, setBillData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [fullscreenChart, setFullscreenChart] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [productResponse, billResponse] = await Promise.all([
          fetch(getApiUrl('api/product/')),
          fetch(getApiUrl('api/bill/'))
        ]);

        const productResult = await productResponse.json();
        const billResult = await billResponse.json();

        if (productResult.success) {
          // Combine product and sales data
          const salesByProduct = billResult.reduce((acc, bill) => {
            if (!acc[bill.productSku]) {
              acc[bill.productSku] = 0;
            }
            acc[bill.productSku] += bill.quantity;
            return acc;
          }, {});

          const chartData = productResult.data.map(product => ({
            name: product.name,
            stock: product.stock,
            price: Math.floor(product.price),
            salesVolume: salesByProduct[product.sku] || 0,
            stockValue: product.stock * product.price
          }));
          setProductData(chartData);
        }

        if (billResult) {
          const salesData = billResult.map(bill => ({
            name: `Bill #${bill.billNumber}`,
            totalAmount: bill.totalAmount,
            quantity: bill.quantity
          }));
          setBillData(salesData);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const renderFullscreenChart = () => {
    switch(fullscreenChart) {
      case 'bar':
        return (
          <BarChart width={800} height={600} data={productData} layout="vertical">
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis dataKey="name" type="category" width={150} />
            <ChartTooltip />
            <Bar dataKey="stock" name="Stock Level">
              {productData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={entry.stock < 10 ? theme.palette.error.main : 
                        entry.stock < 30 ? theme.palette.warning.main : 
                        theme.palette.success.main}
                />
              ))}
            </Bar>
          </BarChart>
        );
      case 'composed':
        return (
          <ComposedChart width={800} height={600} data={productData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <ChartTooltip />
            <Legend />
            <Bar yAxisId="left" dataKey="salesVolume" fill={theme.palette.primary.main} name="Sales Volume" />
            <Line yAxisId="right" type="monotone" dataKey="price" stroke={theme.palette.secondary.main} name="Price" />
          </ComposedChart>
        );
      default:
        return null;
    }
  };

  return (
    <ResponsiveLayoutContainer>
      {loading ? (
        <LinearProgress sx={{ mb: isMobile ? 3 : 4 }} />
      ) : (
        <Grid container spacing={isMobile ? 2 : 4}>
          <Grid item xs={12}>
            <Card sx={{
              boxShadow: isMobile ? theme.shadows[1] : theme.shadows[3],
              borderRadius: isMobile ? 2 : 4,
              transition: 'transform 0.2s ease-in-out',
              '&:hover': {
                transform: isMobile ? 'none' : 'translateY(-4px)',
                boxShadow: isMobile ? theme.shadows[1] : theme.shadows[6],
              }
            }}>
              <CardContent sx={{ p: isMobile ? 2 : 4 }}>
                <Box sx={{
                  display: 'flex',
                  flexDirection: isMobile ? 'column' : 'row',
                  justifyContent: 'space-between',
                  alignItems: isMobile ? 'flex-start' : 'center',
                  mb: isMobile ? 2 : 3,
                  gap: isMobile ? 1 : 0
                }}>
                  <Typography
                    variant={isMobile ? "h6" : "h5"}
                    sx={{
                      fontWeight: 700,
                      color: theme.palette.secondary.main,
                      fontSize: isMobile ? '1.125rem' : '1.5rem'
                    }}
                  >
                    Stock Level Heatmap
                  </Typography>
                  <IconButton
                    onClick={() => setFullscreenChart('bar')}
                    sx={{
                      minWidth: isTouchDevice ? '48px' : '40px',
                      minHeight: isTouchDevice ? '48px' : '40px'
                    }}
                  >
                    <Fullscreen />
                  </IconButton>
                </Box>
                <ResponsiveContainer width="100%" height={isMobile ? 300 : 400}>
                  <BarChart data={productData} layout="vertical">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis
                      dataKey="name"
                      type="category"
                      width={isMobile ? 100 : 150}
                      tick={{ fontSize: isMobile ? 10 : 12 }}
                    />
                    <ChartTooltip />
                    <Bar dataKey="stock" name="Stock Level">
                      {productData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={entry.stock < 10 ? theme.palette.error.main :
                                entry.stock < 30 ? theme.palette.warning.main :
                                theme.palette.success.main}
                        />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Card sx={{
              boxShadow: isMobile ? theme.shadows[1] : theme.shadows[3],
              borderRadius: isMobile ? 2 : 4,
              transition: 'transform 0.2s ease-in-out',
              '&:hover': {
                transform: isMobile ? 'none' : 'translateY(-4px)',
                boxShadow: isMobile ? theme.shadows[1] : theme.shadows[6],
              }
            }}>
              <CardContent sx={{ p: isMobile ? 2 : 4 }}>
                <Box sx={{
                  display: 'flex',
                  flexDirection: isMobile ? 'column' : 'row',
                  justifyContent: 'space-between',
                  alignItems: isMobile ? 'flex-start' : 'center',
                  mb: isMobile ? 2 : 3,
                  gap: isMobile ? 1 : 0
                }}>
                  <Typography
                    variant={isMobile ? "h6" : "h5"}
                    sx={{
                      fontWeight: 700,
                      color: theme.palette.success.main,
                      fontSize: isMobile ? '1.125rem' : '1.5rem'
                    }}
                  >
                    Sales Volume vs Price Trend
                  </Typography>
                  <IconButton
                    onClick={() => setFullscreenChart('composed')}
                    sx={{
                      minWidth: isTouchDevice ? '48px' : '40px',
                      minHeight: isTouchDevice ? '48px' : '40px'
                    }}
                  >
                    <Fullscreen />
                  </IconButton>
                </Box>
                <ResponsiveContainer width="100%" height={isMobile ? 300 : 400}>
                  <ComposedChart data={productData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: isMobile ? 10 : 12 }}
                      angle={isMobile ? -45 : 0}
                      textAnchor={isMobile ? 'end' : 'middle'}
                      height={isMobile ? 80 : 60}
                    />
                    <YAxis yAxisId="left" tick={{ fontSize: isMobile ? 10 : 12 }} />
                    <YAxis
                      yAxisId="right"
                      orientation="right"
                      tick={{ fontSize: isMobile ? 10 : 12 }}
                    />
                    <ChartTooltip />
                    <Legend wrapperStyle={{ fontSize: isMobile ? '12px' : '14px' }} />
                    <Bar
                      yAxisId="left"
                      dataKey="salesVolume"
                      fill={theme.palette.primary.main}
                      name="Sales Volume"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="price"
                      stroke={theme.palette.secondary.main}
                      name="Price"
                      strokeWidth={isMobile ? 2 : 3}
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      <ResponsiveDialog
        open={!!fullscreenChart}
        onClose={() => setFullscreenChart(null)}
        title={
          fullscreenChart === 'bar' ? 'Stock Level Heatmap' :
          fullscreenChart === 'composed' ? 'Sales Volume vs Price Trend' : ''
        }
        maxWidth="lg"
        actions={
          <Button
            onClick={() => setFullscreenChart(null)}
            sx={{
              minHeight: isTouchDevice ? '44px' : '40px',
              fontSize: isMobile ? '0.875rem' : '1rem'
            }}
          >
            Close
          </Button>
        }
      >
        <Box sx={{
          width: '100%',
          height: isMobile ? 400 : 600,
          overflow: 'auto'
        }}>
          {renderFullscreenChart()}
        </Box>
      </ResponsiveDialog>
    </ResponsiveLayoutContainer>
  );
};

export default ChartsPage;