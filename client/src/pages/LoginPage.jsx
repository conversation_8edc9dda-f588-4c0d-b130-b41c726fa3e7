import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  useTheme,
  useMediaQuery,
  Stack,
  IconButton,
  Alert,
  Snackbar,
  CircularProgress,
  Tabs,
  Tab,
  Divider,
  Card,
  CardContent,
  Fade,
  Zoom,
  Slide,
  Avatar,
  InputAdornment,
  alpha
} from '@mui/material';
import axios from 'axios';
import InventoryIcon from '@mui/icons-material/Inventory';
import HomeIcon from '@mui/icons-material/Home';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import LoginIcon from '@mui/icons-material/Login';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import PersonIcon from '@mui/icons-material/Person';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import SecurityIcon from '@mui/icons-material/Security';
import BusinessIcon from '@mui/icons-material/Business';
import ConstructionIcon from '@mui/icons-material/Construction';
import Cookies from 'js-cookie';
import { getApiUrl } from '../config';
import PremiumLanguageSwitcher from '../components/PremiumLanguageSwitcher';
import '../components/PremiumLanguageSwitcher.css';
import mantenaLogo from '../assets/mantena_logo.jpeg';
import { useTranslation } from 'react-i18next';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`auth-tabpanel-${index}`}
      aria-labelledby={`auth-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const LoginPage = () => {
  const { t } = useTranslation();
  const [tabValue, setTabValue] = useState(0);
  const [loginData, setLoginData] = useState({
    email: '',
    password: '',
  });
  const [registerData, setRegisterData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: ''
  });
  const [error, setError] = useState('');
  const [showError, setShowError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showRegisterPassword, setShowRegisterPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setError('');
  };

  const handleLoginInputChange = (e) => {
    setLoginData({
      ...loginData,
      [e.target.name]: e.target.value
    });
  };

  const handleRegisterInputChange = (e) => {
    setRegisterData({
      ...registerData,
      [e.target.name]: e.target.value
    });
  };

  const validateLoginForm = () => {
    if (!loginData.email || !loginData.password) {
      setError(t('common.fieldRequired'));
      setShowError(true);
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(loginData.email)) {
      setError(t('common.invalidEmail'));
      setShowError(true);
      return false;
    }

    if (loginData.password.length < 8) {
      setError(t('common.passwordTooShort'));
      setShowError(true);
      return false;
    }

    return true;
  };

  const validateRegisterForm = () => {
    if (!registerData.email || !registerData.password || !registerData.confirmPassword) {
      setError(t('common.fieldRequired'));
      setShowError(true);
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(registerData.email)) {
      setError(t('common.invalidEmail'));
      setShowError(true);
      return false;
    }

    if (registerData.password.length < 8) {
      setError(t('common.passwordTooShort'));
      setShowError(true);
      return false;
    }

    if (registerData.password !== registerData.confirmPassword) {
      setError(t('common.passwordsDoNotMatch'));
      setShowError(true);
      return false;
    }

    return true;
  };

  const handleLoginSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateLoginForm()) {
      return;
    }

    try {
      setLoading(true);
      const response = await axios.post(getApiUrl('api/auth/login'), {
        email: loginData.email.toLowerCase().trim(),
        password: loginData.password
      });

      const { token, email: userEmail, role } = response.data;

      Cookies.set('jwtToken', token, { 
        secure: true, 
        sameSite: 'strict',
        expires: 10/24
      });
      
      Cookies.set('email', userEmail, { 
        secure: true, 
        sameSite: 'strict',
        expires: 10/24
      });
      
      Cookies.set('userRole', role, { 
        secure: true, 
        sameSite: 'strict',
        expires: 10/24
      });

      // Also set userRole in localStorage for components that read it from there
      localStorage.setItem('userRole', role);

      // Navigate based on role
      if (role === 'superadmin' || role === 'admin') {
        navigate('/manager/dashboard');
      } else {
        navigate('/user/dashboard');
      }

    } catch (error) {
      let errorMessage = t('login.loginError');

      if (error.response) {
        switch (error.response.status) {
          case 400:
            errorMessage = t('common.invalidInput');
            break;
          case 401:
            errorMessage = t('login.loginError');
            break;
          case 403:
            errorMessage = t('common.accessDenied');
            break;
          case 429:
            errorMessage = 'Too many login attempts. Please try again later.';
            break;
          default:
            errorMessage = error.response.data?.error || errorMessage;
        }
      }

      setError(errorMessage);
      setShowError(true);
      console.error('Login error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRegisterSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateRegisterForm()) {
      return;
    }

    try {
      setLoading(true);
      const response = await axios.post(getApiUrl('api/auth/register'), {
        email: registerData.email.toLowerCase().trim(),
        password: registerData.password,
        name: registerData.name
      });

      // Extract data from response
      const { token, email: userEmail, role } = response.data;

      // Store authentication data
      Cookies.set('jwtToken', token, { 
        secure: true, 
        sameSite: 'strict',
        expires: 10/24
      });
      
      Cookies.set('email', userEmail, { 
        secure: true, 
        sameSite: 'strict',
        expires: 10/24
      });
      
      Cookies.set('userRole', role, { 
        secure: true, 
        sameSite: 'strict',
        expires: 10/24
      });

      // Also set userRole in localStorage for components that read it from there
      localStorage.setItem('userRole', role);

      // Navigate based on role (should be 'user' for self-registration)
      navigate('/user/dashboard');
    } catch (error) {
      let errorMessage = t('login.registrationError');

      if (error.response) {
        switch (error.response.status) {
          case 400:
            errorMessage = error.response.data?.error || t('common.invalidInput');
            break;
          case 409:
            errorMessage = t('userManagement.emailAlreadyExists');
            break;
          default:
            errorMessage = error.response.data?.error || errorMessage;
        }
      }

      setError(errorMessage);
      setShowError(true);
      console.error('Registration error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseError = () => {
    setShowError(false);
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      width: '100vw',
      background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      overflow: 'hidden',
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(25, 118, 210, 0.2) 0%, transparent 50%)
        `,
        pointerEvents: 'none',
      },
    }}>
      <Zoom in timeout={800}>
        <Card sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          alignItems: 'stretch',
          justifyContent: 'center',
          width: { xs: '95vw', md: '1000px' },
          maxWidth: '98vw',
          minHeight: { xs: 'auto', md: '650px' },
          background: 'rgba(255, 255, 255, 0.98)',
          backdropFilter: 'blur(20px)',
          borderRadius: 6,
          overflow: 'hidden',
          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.3)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          position: 'relative',
          zIndex: 1,
        }}>
          <Box
            sx={{
              flex: 1,
              minWidth: { xs: 320, md: 400 },
              background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
              color: 'white',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              p: { xs: 4, md: 6 },
              position: 'relative',
              overflow: 'hidden',
              fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `
                  radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%)
                `,
                pointerEvents: 'none',
              },
            }}
          >
            {/* Premium Logo Section */}
            <Fade in timeout={1000}>
              <Box
                sx={{
                  position: 'relative',
                  zIndex: 2,
                  mb: 4,
                }}
              >
                <Avatar
                  sx={{
                    width: 140,
                    height: 140,
                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 4px rgba(255, 255, 255, 0.2)',
                    border: '2px solid rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.05)',
                      boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4), 0 0 0 6px rgba(255, 255, 255, 0.3)',
                    },
                  }}
                >
                  <img
                    src={mantenaLogo}
                    alt="Mantena Logo"
                    style={{
                      width: '90%',
                      height: '90%',
                      objectFit: 'cover',
                      borderRadius: '50%',
                    }}
                  />
                </Avatar>
              </Box>
            </Fade>
            <Slide direction="up" in timeout={1200}>
              <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 2 }}>
                <Typography
                  variant="h3"
                  fontWeight={700}
                  sx={{
                    mb: 3,
                    background: 'linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                    fontSize: { xs: '2.2rem', md: '3rem' },
                    letterSpacing: 2,
                    lineHeight: 1.2,
                  }}
                >
                  {t('login.welcomeTo')}
                  <br />
                  {t('login.mantena')}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
                  <ConstructionIcon sx={{ fontSize: 24, mr: 1, opacity: 0.9 }} />
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      opacity: 0.95,
                      fontSize: { xs: '1rem', md: '1.2rem' },
                      letterSpacing: 1,
                    }}
                  >
                    {t('login.infrastructureExcellence')}
                  </Typography>
                  <BusinessIcon sx={{ fontSize: 24, ml: 1, opacity: 0.9 }} />
                </Box>

                <Typography
                  variant="body1"
                  sx={{
                    opacity: 0.9,
                    fontWeight: 400,
                    textAlign: 'center',
                    maxWidth: 350,
                    fontSize: { xs: '0.95rem', md: '1.1rem' },
                    lineHeight: 1.6,
                    mx: 'auto',
                  }}
                >
                  {t('login.companyDescription')}
                </Typography>
              </Box>
            </Slide>
          </Box>

          <Box sx={{
            flex: 1,
            minWidth: { xs: 320, md: 400 },
            background: 'rgba(255, 255, 255, 0.98)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            p: { xs: 4, md: 6 },
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(21, 101, 192, 0.01) 100%)',
              pointerEvents: 'none',
            },
          }}>
            {/* Premium Tabs Section */}
            <Fade in timeout={1000}>
              <Box sx={{ width: '100%', position: 'relative', zIndex: 2, mb: 4 }}>
                <Tabs
                  value={tabValue}
                  onChange={handleTabChange}
                  variant="fullWidth"
                  sx={{
                    mb: 3,
                    '& .MuiTabs-indicator': {
                      background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                      height: 3,
                      borderRadius: 2,
                    },
                    '& .MuiTab-root': {
                      borderRadius: 3,
                      mx: 0.5,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: 'rgba(25, 118, 210, 0.05)',
                      },
                      '&.Mui-selected': {
                        background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
                        color: '#1976d2',
                      },
                    },
                  }}
                >
                  <Tab
                    icon={<LoginIcon />}
                    label={t('login.signIn').toUpperCase()}
                    iconPosition="start"
                    sx={{
                      fontWeight: 700,
                      fontSize: '0.9rem',
                      letterSpacing: 1,
                      textTransform: 'none',
                    }}
                  />
                  <Tab
                    icon={<PersonAddIcon />}
                    label={t('login.register').toUpperCase()}
                    iconPosition="start"
                    sx={{
                      fontWeight: 700,
                      fontSize: '0.9rem',
                      letterSpacing: 1,
                      textTransform: 'none',
                    }}
                  />
                </Tabs>
              </Box>
            </Fade>

            {successMessage && (
              <Alert
                severity="success"
                sx={{
                  mb: 3,
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(56, 142, 60, 0.05) 100%)',
                }}
                onClose={() => setSuccessMessage('')}
              >
                {successMessage}
              </Alert>
            )}

            {/* Premium Login Panel */}
            <TabPanel value={tabValue} index={0}>
              <Slide direction="up" in={tabValue === 0} timeout={600}>
                <Box>
                  <Stack direction="row" alignItems="center" spacing={1} mb={3}>
                    <SecurityIcon sx={{ color: '#1976d2', fontSize: 28 }} />
                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 700,
                        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                        backgroundClip: 'text',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        fontSize: { xs: '1.4rem', md: '1.6rem' },
                      }}
                    >
                      {t('login.subtitle')}
                    </Typography>
                  </Stack>

                  <Box component="form" onSubmit={handleLoginSubmit}>
                    <TextField
                      fullWidth
                      variant="outlined"
                      required
                      id="email"
                      label={t('login.emailAddress')}
                      name="email"
                      autoComplete="email"
                      autoFocus
                      value={loginData.email}
                      onChange={handleLoginInputChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon sx={{ color: '#1976d2' }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 3,
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 4,
                          background: 'rgba(25, 118, 210, 0.02)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            background: 'rgba(25, 118, 210, 0.05)',
                          },
                          '&.Mui-focused': {
                            background: 'rgba(25, 118, 210, 0.08)',
                            boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)',
                          },
                        },
                      }}
                    />

                    <TextField
                      fullWidth
                      variant="outlined"
                      required
                      name="password"
                      label={t('common.password')}
                      type={showPassword ? 'text' : 'password'}
                      id="password"
                      autoComplete="current-password"
                      value={loginData.password}
                      onChange={handleLoginInputChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LockIcon sx={{ color: '#1976d2' }} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowPassword(!showPassword)}
                              edge="end"
                              sx={{ color: '#1976d2' }}
                            >
                              {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 4,
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 4,
                          background: 'rgba(25, 118, 210, 0.02)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            background: 'rgba(25, 118, 210, 0.05)',
                          },
                          '&.Mui-focused': {
                            background: 'rgba(25, 118, 210, 0.08)',
                            boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)',
                          },
                        },
                      }}
                    />

                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      disabled={loading}
                      size="large"
                      sx={{
                        mt: 2,
                        py: 2,
                        fontWeight: 700,
                        fontSize: '1.1rem',
                        borderRadius: 4,
                        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                        boxShadow: '0 8px 25px rgba(25, 118, 210, 0.3)',
                        textTransform: 'none',
                        letterSpacing: 1,
                        '&:hover': {
                          background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                          boxShadow: '0 12px 35px rgba(25, 118, 210, 0.4)',
                          transform: 'translateY(-3px)',
                        },
                        '&:disabled': {
                          background: 'rgba(0, 0, 0, 0.12)',
                          color: 'rgba(0, 0, 0, 0.26)',
                          boxShadow: 'none',
                        },
                        transition: 'all 0.3s ease',
                      }}
                    >
                      {loading ? (
                        <CircularProgress size={26} sx={{ color: 'white' }} />
                      ) : (
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <LoginIcon />
                          <span>{t('login.signIn')}</span>
                        </Stack>
                      )}
                    </Button>
                  </Box>
                </Box>
              </Slide>
            </TabPanel>

            {/* Premium Register Panel */}
            <TabPanel value={tabValue} index={1}>
              <Slide direction="up" in={tabValue === 1} timeout={600}>
                <Box>
                  <Stack direction="row" alignItems="center" spacing={1} mb={3}>
                    <PersonAddIcon sx={{ color: '#1976d2', fontSize: 28 }} />
                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 700,
                        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                        backgroundClip: 'text',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        fontSize: { xs: '1.4rem', md: '1.6rem' },
                      }}
                    >
                      {t('login.createNewAccount')}
                    </Typography>
                  </Stack>

                  <Box component="form" onSubmit={handleRegisterSubmit}>
                    <TextField
                      fullWidth
                      variant="outlined"
                      required
                      id="register-email"
                      label={t('login.emailAddress')}
                      name="email"
                      autoComplete="email"
                      value={registerData.email}
                      onChange={handleRegisterInputChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon sx={{ color: '#1976d2' }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 3,
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 4,
                          background: 'rgba(25, 118, 210, 0.02)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            background: 'rgba(25, 118, 210, 0.05)',
                          },
                          '&.Mui-focused': {
                            background: 'rgba(25, 118, 210, 0.08)',
                            boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)',
                          },
                        },
                      }}
                    />

                    <TextField
                      fullWidth
                      variant="outlined"
                      id="register-name"
                      label={`${t('login.fullName')} (${t('common.optional')})`}
                      name="name"
                      autoComplete="name"
                      value={registerData.name}
                      onChange={handleRegisterInputChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon sx={{ color: '#1976d2' }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 3,
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 4,
                          background: 'rgba(25, 118, 210, 0.02)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            background: 'rgba(25, 118, 210, 0.05)',
                          },
                          '&.Mui-focused': {
                            background: 'rgba(25, 118, 210, 0.08)',
                            boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)',
                          },
                        },
                      }}
                    />

                    <TextField
                      fullWidth
                      variant="outlined"
                      required
                      name="password"
                      label={t('common.password')}
                      type={showRegisterPassword ? 'text' : 'password'}
                      id="register-password"
                      autoComplete="new-password"
                      value={registerData.password}
                      onChange={handleRegisterInputChange}
                      helperText={t('login.passwordMinLength')}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LockIcon sx={{ color: '#1976d2' }} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowRegisterPassword(!showRegisterPassword)}
                              edge="end"
                              sx={{ color: '#1976d2' }}
                            >
                              {showRegisterPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 3,
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 4,
                          background: 'rgba(25, 118, 210, 0.02)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            background: 'rgba(25, 118, 210, 0.05)',
                          },
                          '&.Mui-focused': {
                            background: 'rgba(25, 118, 210, 0.08)',
                            boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)',
                          },
                        },
                      }}
                    />

                    <TextField
                      fullWidth
                      variant="outlined"
                      required
                      name="confirmPassword"
                      label={t('common.confirmPassword')}
                      type={showConfirmPassword ? 'text' : 'password'}
                      id="register-confirm-password"
                      autoComplete="new-password"
                      value={registerData.confirmPassword}
                      onChange={handleRegisterInputChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LockIcon sx={{ color: '#1976d2' }} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              edge="end"
                              sx={{ color: '#1976d2' }}
                            >
                              {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 4,
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 4,
                          background: 'rgba(25, 118, 210, 0.02)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            background: 'rgba(25, 118, 210, 0.05)',
                          },
                          '&.Mui-focused': {
                            background: 'rgba(25, 118, 210, 0.08)',
                            boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)',
                          },
                        },
                      }}
                    />

                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      disabled={loading}
                      size="large"
                      sx={{
                        mt: 2,
                        py: 2,
                        fontWeight: 700,
                        fontSize: '1.1rem',
                        borderRadius: 4,
                        background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                        boxShadow: '0 8px 25px rgba(76, 175, 80, 0.3)',
                        textTransform: 'none',
                        letterSpacing: 1,
                        '&:hover': {
                          background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
                          boxShadow: '0 12px 35px rgba(76, 175, 80, 0.4)',
                          transform: 'translateY(-3px)',
                        },
                        '&:disabled': {
                          background: 'rgba(0, 0, 0, 0.12)',
                          color: 'rgba(0, 0, 0, 0.26)',
                          boxShadow: 'none',
                        },
                        transition: 'all 0.3s ease',
                      }}
                    >
                      {loading ? (
                        <CircularProgress size={26} sx={{ color: 'white' }} />
                      ) : (
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <PersonAddIcon />
                          <span>{t('login.createAccount')}</span>
                        </Stack>
                      )}
                    </Button>

                    <Card
                      sx={{
                        mt: 3,
                        background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(21, 101, 192, 0.02) 100%)',
                        border: '1px solid rgba(25, 118, 210, 0.1)',
                        borderRadius: 3,
                      }}
                    >
                      <CardContent sx={{ p: 2 }}>
                        <Typography variant="body2" color="text.secondary" align="center" sx={{ fontWeight: 500 }}>
                          📝 {t('login.registrationNote')}
                          <br />
                          🔐 {t('login.contactAdmin')}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Box>
                </Box>
              </Slide>
            </TabPanel>
          </Box>
        </Card>
      </Zoom>

      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        TransitionComponent={Slide}
      >
        <Alert
          onClose={handleCloseError}
          severity="error"
          sx={{
            width: '100%',
            borderRadius: 3,
            background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.95) 0%, rgba(211, 47, 47, 0.9) 100%)',
            backdropFilter: 'blur(10px)',
            color: 'white',
            fontWeight: 600,
            boxShadow: '0 8px 25px rgba(244, 67, 54, 0.3)',
          }}
        >
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LoginPage;
