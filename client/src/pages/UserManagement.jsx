import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  useTheme,
  Chip,
  Stack,
  Switch,
  FormControlLabel,
  Tooltip,
  CircularProgress,
  Alert,
  Divider,
  Grid,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  PersonAdd,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  VpnKey as VpnKeyIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { getApiUrl } from '../config';

const UserManagement = () => {
  const theme = useTheme();
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    role: '',
    active: true,
    password: '',
    confirmPassword: '',
  });

  useEffect(() => {
    // Fetch users and roles when component mounts
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch users
        const usersResponse = await axios.get(getApiUrl('api/user/'));
        setUsers(usersResponse.data);
        
        // Fetch roles
        const rolesResponse = await axios.get(getApiUrl('api/roles'));
        setRoles(rolesResponse.data);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load users or roles');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  const handleOpenDialog = (user = null) => {
    if (user) {
      setSelectedUser(user);
      setFormData({ 
        email: user.email,
        name: user.name || '',
        role: user.role,
        active: user.active !== false, // Default to true if not specified
        password: '',
        confirmPassword: '',
      });
    } else {
      setSelectedUser(null);
      setFormData({ 
        email: '',
        name: '',
        role: '',
        active: true,
        password: '',
        confirmPassword: '',
      });
    }
    setError('');
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedUser(null);
    setError('');
  };

  const handleInputChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'active' ? checked : value
    });
  };

  const validateForm = () => {
    // Email validation
    if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }

    // Role validation
    if (!formData.role) {
      setError('Please select a role');
      return false;
    }

    // Password validation for new users
    if (!selectedUser) {
      if (!formData.password) {
        setError('Password is required for new users');
        return false;
      }
      
      if (formData.password.length < 8) {
        setError('Password must be at least 8 characters long');
        return false;
      }
      
      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match');
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      if (selectedUser) {
        // Update existing user
        const dataToUpdate = {
          name: formData.name,
          role: formData.role,
          active: formData.active
        };
        
        // Only include password if it's provided
        if (formData.password && formData.password.length >= 8) {
          dataToUpdate.password = formData.password;
        }
        
        await axios.put(getApiUrl(`api/user/${selectedUser.email}`), dataToUpdate);
        
        setUsers(users.map(user => 
          user.email === selectedUser.email ? { ...user, ...dataToUpdate } : user
        ));
      } else {
        // Add new user
        const newUserData = {
          email: formData.email,
          name: formData.name,
          role: formData.role,
          active: formData.active,
          password: formData.password
        };
        
        const response = await axios.post(getApiUrl('api/user/add'), newUserData);
        setUsers([...users, response.data.user]);
      }
      
      handleCloseDialog();
    } catch (error) {
      console.error('Error saving user:', error);
      if (error.response && error.response.data && error.response.data.message) {
        setError(error.response.data.message);
      } else {
        setError('Failed to save user. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleToggleUserStatus = async (user) => {
    try {
      const updatedActive = !user.active;
      await axios.put(getApiUrl(`api/user/${user.email}`), {
        active: updatedActive
      });
      
      setUsers(users.map(u => 
        u.email === user.email ? { ...u, active: updatedActive } : u
      ));
    } catch (error) {
      console.error('Error toggling user status:', error);
    }
  };

  const handleDeleteUser = async (userEmail) => {
    try {
      await axios.delete(getApiUrl(`api/user/${userEmail}`));
      setUsers(users.filter(user => user.email !== userEmail));
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  };

  // Get color for role chip based on role name
  const getRoleColor = (roleName) => {
    switch (roleName) {
      case 'superadmin':
        return 'error';
      case 'admin':
        return 'primary';
      case 'siteengineer':
        return 'success';
      case 'storekeeper':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Box
      sx={{
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        height: '100%',
        padding: 3,
        overflow: 'auto',
      }}
    >
      <Container maxWidth="xl">
        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 4,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight={700}
                sx={{
                  background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                  fontSize: { xs: '1.8rem', md: '2.5rem' },
                }}
              >
                👥 User Management
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage user accounts, roles, and permissions
              </Typography>
            </Box>

            <Stack direction="row" spacing={2}>
              <Tooltip title="Refresh Users">
                <IconButton
                  onClick={() => window.location.reload()}
                  sx={{
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                      transform: 'scale(1.05)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>

              <Button
                variant="contained"
                startIcon={<PersonAdd />}
                onClick={() => handleOpenDialog()}
                sx={{
                  borderRadius: 3,
                  textTransform: 'none',
                  px: 3,
                  py: 1.5,
                  background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                  boxShadow: '0 4px 20px rgba(76, 175, 80, 0.3)',
                  fontWeight: 600,
                  '&:hover': {
                    background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 25px rgba(76, 175, 80, 0.4)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Add New User
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {/* Statistics Dashboard */}
        <Grid container spacing={3} mb={3}>
          {[
            {
              label: 'Total Users',
              value: users.length,
              icon: <GroupIcon />,
              gradient: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              change: '+12%'
            },
            {
              label: 'Active Users',
              value: users.filter(user => user.active !== false).length,
              icon: <CheckCircleIcon />,
              gradient: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
              change: '+8%'
            },
            {
              label: 'Inactive Users',
              value: users.filter(user => user.active === false).length,
              icon: <BlockIcon />,
              gradient: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
              change: '-15%'
            },
            {
              label: 'Admin Users',
              value: users.filter(user => user.role === 'admin' || user.role === 'superadmin').length,
              icon: <AdminIcon />,
              gradient: 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
              change: '+5%'
            },
          ].map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={stat.label}>
              <Zoom in timeout={300 + index * 100}>
                <Card
                  sx={{
                    background: 'rgba(255, 255, 255, 0.98)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    borderRadius: 4,
                    overflow: 'hidden',
                    position: 'relative',
                    boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 40px rgba(25, 118, 210, 0.2)',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 4,
                      background: stat.gradient,
                    },
                  }}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="h6" fontWeight={700} color="#1976d2">
                          {stat.value}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" fontWeight={500}>
                          {stat.label}
                        </Typography>
                        <Chip
                          label={stat.change}
                          size="small"
                          sx={{
                            mt: 0.5,
                            backgroundColor: stat.change.startsWith('+') ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
                            color: stat.change.startsWith('+') ? '#4caf50' : '#f44336',
                            fontWeight: 600,
                            fontSize: '0.7rem',
                          }}
                        />
                      </Box>
                      <Avatar
                        sx={{
                          background: stat.gradient,
                          color: 'white',
                          width: 40,
                          height: 40,
                          boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                        }}
                      >
                        {stat.icon}
                      </Avatar>
                    </Stack>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>

        {error && !openDialog && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              borderRadius: 3,
              background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(211, 47, 47, 0.05) 100%)',
            }}
          >
            {error}
          </Alert>
        )}

        {/* Content Area */}
        {loading && !openDialog ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 8,
            }}
          >
            <CircularProgress
              size={60}
              thickness={4}
              sx={{ color: 'white' }}
            />
            <Typography variant="h6" sx={{ mt: 2, color: 'white', fontWeight: 500 }}>
              Loading users...
            </Typography>
          </Box>
        ) : (
          <Paper
            elevation={0}
            sx={{
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              border: '1px solid rgba(255, 255, 255, 0.3)',
              overflow: 'hidden',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
            }}
          >
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow
                    sx={{
                      background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
                    }}
                  >
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Name</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Email</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Role</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Status</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 700, color: '#1976d2' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          👥 No users found
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Add your first user to get started
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    users.map((user, index) => (
                      <Fade in timeout={300 + index * 50} key={user.email}>
                        <TableRow
                          hover
                          sx={{
                            '&:hover': {
                              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(21, 101, 192, 0.02) 100%)',
                              transform: 'scale(1.01)',
                            },
                            transition: 'all 0.2s ease',
                            cursor: 'pointer',
                          }}
                        >
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Avatar
                                sx={{
                                  width: 32,
                                  height: 32,
                                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                                  fontSize: '0.875rem',
                                  fontWeight: 600,
                                }}
                              >
                                {(user.name || user.email).charAt(0).toUpperCase()}
                              </Avatar>
                              <Typography variant="body2" fontWeight={600}>
                                {user.name || user.email.split('@')[0]}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <EmailIcon fontSize="small" color="primary" />
                              <Typography variant="body2">
                                {user.email}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={user.role}
                              color={getRoleColor(user.role)}
                              size="small"
                              icon={<VpnKeyIcon />}
                              sx={{
                                fontWeight: 600,
                                '& .MuiChip-icon': {
                                  fontSize: 16,
                                },
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={user.active !== false ? 'Active' : 'Inactive'}
                              color={user.active !== false ? 'success' : 'default'}
                              size="small"
                              icon={user.active !== false ? <CheckCircleIcon /> : <BlockIcon />}
                              variant="outlined"
                              sx={{
                                fontWeight: 600,
                                '& .MuiChip-icon': {
                                  fontSize: 16,
                                },
                              }}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Stack direction="row" spacing={1} justifyContent="flex-end">
                              <Tooltip title={user.active !== false ? "Deactivate user" : "Activate user"}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleToggleUserStatus(user)}
                                  sx={{
                                    color: user.active !== false ? '#f44336' : '#4caf50',
                                    '&:hover': {
                                      backgroundColor: user.active !== false ? 'rgba(244, 67, 54, 0.1)' : 'rgba(76, 175, 80, 0.1)',
                                    },
                                  }}
                                >
                                  {user.active !== false ? <BlockIcon /> : <CheckCircleIcon />}
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit User">
                                <IconButton
                                  size="small"
                                  onClick={() => handleOpenDialog(user)}
                                  sx={{
                                    color: '#1976d2',
                                    '&:hover': {
                                      backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                    },
                                  }}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete User">
                                <IconButton
                                  size="small"
                                  onClick={() => handleDeleteUser(user.email)}
                                  sx={{
                                    color: '#f44336',
                                    '&:hover': {
                                      backgroundColor: 'rgba(244, 67, 54, 0.1)',
                                    },
                                  }}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Stack>
                          </TableCell>
                        </TableRow>
                      </Fade>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        )}

        {/* User Dialog */}
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          maxWidth="md"
          fullWidth
          TransitionComponent={Zoom}
          transitionDuration={400}
          PaperProps={{
            sx: {
              borderRadius: 4,
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              boxShadow: '0 20px 60px rgba(25, 118, 210, 0.3)',
            }
          }}
        >
          <DialogTitle
            sx={{
              fontWeight: 700,
              color: '#1976d2',
              pb: 1,
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
              borderRadius: '16px 16px 0 0',
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                  width: 40,
                  height: 40,
                }}
              >
                {selectedUser ? <EditIcon /> : <PersonAdd />}
              </Avatar>
              <Typography variant="h6" fontWeight={700}>
                {selectedUser ? 'Edit User' : 'Add New User'}
              </Typography>
            </Stack>
          </DialogTitle>
          <DialogContent sx={{ pt: 3, pb: 2 }}>
            {error && (
              <Alert
                severity="error"
                sx={{
                  mb: 3,
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(211, 47, 47, 0.05) 100%)',
                }}
              >
                {error}
              </Alert>
            )}

            <Stack spacing={1} mb={3}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <PersonIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                <Typography variant="h6" fontWeight={600} color="#1976d2">
                  User Information
                </Typography>
              </Stack>
            </Stack>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  name="email"
                  label="Email Address"
                  fullWidth
                  value={formData.email}
                  onChange={handleInputChange}
                  disabled={!!selectedUser}
                  required
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  name="name"
                  label="Full Name"
                  fullWidth
                  value={formData.name}
                  onChange={handleInputChange}
                  variant="outlined"
                  placeholder="Enter full name (optional)"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl
                  fullWidth
                  required
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      }
                    }
                  }}
                >
                  <InputLabel>User Role</InputLabel>
                  <Select
                    name="role"
                    value={formData.role}
                    label="User Role"
                    onChange={handleInputChange}
                    required
                  >
                    {roles.length > 0 ? (
                      roles.map(role => (
                        <MenuItem key={role._id} value={role.name}>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <VpnKeyIcon fontSize="small" />
                            <span>{role.displayName}</span>
                          </Stack>
                        </MenuItem>
                      ))
                    ) : (
                      <>
                        <MenuItem value="superadmin">
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <AdminIcon fontSize="small" />
                            <span>Super Administrator</span>
                          </Stack>
                        </MenuItem>
                        <MenuItem value="admin">
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <AdminIcon fontSize="small" />
                            <span>Administrator</span>
                          </Stack>
                        </MenuItem>
                        <MenuItem value="siteengineer">
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <PersonIcon fontSize="small" />
                            <span>Site Engineer</span>
                          </Stack>
                        </MenuItem>
                        <MenuItem value="storekeeper">
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <PersonIcon fontSize="small" />
                            <span>Store Keeper</span>
                          </Stack>
                        </MenuItem>
                        <MenuItem value="user">
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <PersonIcon fontSize="small" />
                            <span>Regular User</span>
                          </Stack>
                        </MenuItem>
                      </>
                    )}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box sx={{ mt: 2 }}>
                  <Stack direction="row" alignItems="center" spacing={1} mb={1}>
                    <SecurityIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                    <Typography variant="subtitle2" fontWeight={600} color="#1976d2">
                      Account Status
                    </Typography>
                  </Stack>
                  <FormControlLabel
                    control={
                      <Switch
                        name="active"
                        checked={formData.active}
                        onChange={handleInputChange}
                        color="primary"
                        sx={{
                          '& .MuiSwitch-switchBase.Mui-checked': {
                            color: '#4caf50',
                          },
                          '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                            backgroundColor: '#4caf50',
                          },
                        }}
                      />
                    }
                    label={
                      <Typography variant="body2" fontWeight={500}>
                        {formData.active ? 'Account Active' : 'Account Inactive'}
                      </Typography>
                    }
                  />
                </Box>
              </Grid>
            </Grid>

            {!selectedUser && (
              <>
                <Divider sx={{ my: 3, borderColor: 'rgba(25, 118, 210, 0.1)' }} />
                <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                  <SecurityIcon sx={{ color: '#1976d2', fontSize: 20 }} />
                  <Typography variant="h6" fontWeight={600} color="#1976d2">
                    Security Settings
                  </Typography>
                </Stack>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      name="password"
                      label="Password"
                      type="password"
                      fullWidth
                      value={formData.password}
                      onChange={handleInputChange}
                      required={!selectedUser}
                      variant="outlined"
                      placeholder="Enter secure password"
                      helperText="At least 8 characters"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 3,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1976d2',
                          },
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      name="confirmPassword"
                      label="Confirm Password"
                      type="password"
                      fullWidth
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      required={!selectedUser}
                      variant="outlined"
                      placeholder="Confirm password"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 3,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#1976d2',
                          },
                        },
                      }}
                    />
                  </Grid>
                </Grid>
              </>
            )}

            {selectedUser && (
              <>
                <Divider sx={{ my: 3, borderColor: 'rgba(25, 118, 210, 0.1)' }} />
                <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                  <SecurityIcon sx={{ color: '#ff9800', fontSize: 20 }} />
                  <Typography variant="h6" fontWeight={600} color="#ff9800">
                    Change Password (Optional)
                  </Typography>
                </Stack>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      name="password"
                      label="New Password"
                      type="password"
                      fullWidth
                      value={formData.password}
                      onChange={handleInputChange}
                      variant="outlined"
                      placeholder="Enter new password"
                      helperText="Leave blank to keep current password"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 3,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#ff9800',
                          },
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      name="confirmPassword"
                      label="Confirm New Password"
                      type="password"
                      fullWidth
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      variant="outlined"
                      placeholder="Confirm new password"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 3,
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#ff9800',
                          },
                        },
                      }}
                    />
                  </Grid>
                </Grid>
              </>
            )}
          </DialogContent>

          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleCloseDialog}
              disabled={loading}
              variant="outlined"
              sx={{
                fontWeight: 600,
                borderRadius: 3,
                px: 3,
                borderColor: '#bdbdbd',
                color: '#757575',
                '&:hover': {
                  borderColor: '#9e9e9e',
                  backgroundColor: 'rgba(189, 189, 189, 0.1)',
                },
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleSubmit}
              disabled={loading}
              sx={{
                fontWeight: 700,
                borderRadius: 3,
                px: 4,
                background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                  boxShadow: '0 6px 25px rgba(25, 118, 210, 0.4)',
                },
                '&:disabled': {
                  background: 'rgba(0, 0, 0, 0.12)',
                  color: 'rgba(0, 0, 0, 0.26)',
                  boxShadow: 'none',
                },
              }}
            >
              {loading ? (
                <CircularProgress size={20} sx={{ color: 'white' }} />
              ) : (
                selectedUser ? 'Update User' : 'Add User'
              )}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
};

export default UserManagement;
