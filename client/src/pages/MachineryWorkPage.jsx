import React, { useState, useEffect } from 'react';
import {
  Box, Button, TextField, MenuItem, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Select, InputLabel, FormControl, Dialog, DialogTitle, DialogContent, DialogActions, Snackbar, Alert, Grid, IconButton, TablePagination, Stack, useTheme, alpha, Container, Card, CardContent, Avatar, Fade, Zoom, Tooltip, Chip, Divider, CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Download as DownloadIcon,
  Construction as ConstructionIcon,
  Schedule as ScheduleIcon,
  LocalGasStation as FuelIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import axios from 'axios';
import { getApiUrl } from '../config';

const conditions = ['Working', 'Breakdown', 'Maintenance'];
const fuelTypes = ['Diesel', 'Petrol'];

const initialForm = {
  company: '',
  machineName: '',
  description: '',
  condition: '',
  location: '',
  fuelType: '',
  fuelUsedLiters: '',
  workingFrom: '',
  workingTo: '',
  totalHoursWorked: '',
  remarks: '',
  date: '',
};

export default function MachineryWorkPage() {
  const theme = useTheme();
  const [form, setForm] = useState(initialForm);
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(8);
  const [conditionFilter, setConditionFilter] = useState('all');
  const [fuelTypeFilter, setFuelTypeFilter] = useState('all');

  const fetchLogs = async () => {
    setLoading(true);
    setError('');
    try {
      const res = await axios.get(getApiUrl('api/machinery-work'));
      setLogs(Array.isArray(res.data) ? res.data : []);
      setSnackbar({
        open: true,
        message: `Loaded ${res.data?.length || 0} machinery logs successfully`,
        severity: 'success'
      });
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Failed to fetch machinery logs';
      setError(errorMessage);
      setLogs([]);
      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchLogs();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm({ ...form, [name]: value });

    // --- Calculate total hours if From or To time changes ---
    if (name === 'workingFrom' || name === 'workingTo') {
      const updatedForm = { ...form, [name]: value };
      const { workingFrom, workingTo } = updatedForm;

      if (workingFrom && workingTo) {
        try {
          // Simple time parsing (assuming HH:mm)
          const [fromHours, fromMinutes] = workingFrom.split(':').map(Number);
          const [toHours, toMinutes] = workingTo.split(':').map(Number);

          // Convert times to total minutes from midnight
          const fromTotalMinutes = fromHours * 60 + fromMinutes;
          let toTotalMinutes = toHours * 60 + toMinutes;

          // Handle cases where 'To' time is before 'From' in the same day, assuming 0 hours.
          // More complex logic needed for overnight shifts.
          if (toTotalMinutes < fromTotalMinutes) {
             setForm(prev => ({ ...prev, totalHoursWorked: 0 }));
             return; // Exit to prevent incorrect calculation
          }


          const durationMinutes = toTotalMinutes - fromTotalMinutes;
          const totalHours = durationMinutes / 60;

          // Update the form state with the calculated total hours
          setForm(prev => ({ ...prev, totalHoursWorked: totalHours.toFixed(2) })); // Use toFixed for display
        } catch (error) {
          console.error("Error calculating total hours:", error);
          // Optionally, set totalHoursWorked to 0 or NaN on calculation error
          setForm(prev => ({ ...prev, totalHoursWorked: 0 }));
        }
      } else {
         // If either time is cleared, reset total hours
         setForm(prev => ({ ...prev, totalHoursWorked: '' }));
      }
    }
    // --- End calculation logic ---

  };

  const handleOpenDialog = () => {
    setForm(initialForm);
    setOpenDialog(true);
  };
  const handleCloseDialog = () => setOpenDialog(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    try {
      const payload = {
        ...form,
        fuelUsedLiters: Number(form.fuelUsedLiters),
        totalHoursWorked: Number(form.totalHoursWorked),
        date: form.date || new Date().toISOString().slice(0, 10),
      };
      await axios.post(getApiUrl('api/machinery-work'), payload);
      setSnackbar({ open: true, message: 'Log added successfully!', severity: 'success' });
      setForm(initialForm);
      setOpenDialog(false);
      fetchLogs();
    } catch (err) {
      setSnackbar({ open: true, message: err.response?.data?.error || 'Failed to add log', severity: 'error' });
    }
  };

  const handleSearchChange = (e) => {
    setSearch(e.target.value);
    setPage(0);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(1);
  };

  // Calculate statistics
  const stats = {
    total: logs.length,
    working: logs.filter(log => log.condition === 'Working').length,
    breakdown: logs.filter(log => log.condition === 'Breakdown').length,
    maintenance: logs.filter(log => log.condition === 'Maintenance').length,
    totalFuelUsed: logs.reduce((sum, log) => sum + (log.fuelUsedLiters || 0), 0),
    totalHours: logs.reduce((sum, log) => sum + (log.totalHoursWorked || 0), 0),
    avgHoursPerDay: logs.length > 0 ? (logs.reduce((sum, log) => sum + (log.totalHoursWorked || 0), 0) / logs.length).toFixed(1) : 0,
  };

  const handleExportLogs = () => {
    const headers = ['Date', 'Company', 'Machine Name', 'Description', 'Condition', 'Location', 'Fuel Type', 'Fuel Used', 'Working From', 'Working To', 'Total Hours', 'Remarks'];

    const csvData = [
      headers,
      ...filteredLogs.map(log => [
        log.date ? new Date(log.date).toLocaleDateString() : '',
        log.company,
        log.machineName,
        log.description,
        log.condition,
        log.location,
        log.fuelType,
        log.fuelUsedLiters,
        log.workingFrom,
        log.workingTo,
        log.totalHoursWorked,
        log.remarks
      ])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'machinery_work_logs.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Filter logs by search and filters
  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.company?.toLowerCase().includes(search.toLowerCase()) ||
                         log.machineName?.toLowerCase().includes(search.toLowerCase()) ||
                         log.location?.toLowerCase().includes(search.toLowerCase()) ||
                         log.description?.toLowerCase().includes(search.toLowerCase());

    const matchesCondition = conditionFilter === 'all' || log.condition === conditionFilter;
    const matchesFuelType = fuelTypeFilter === 'all' || log.fuelType === fuelTypeFilter;

    return matchesSearch && matchesCondition && matchesFuelType;
  });

  return (
    <Box
      sx={{
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        height: '100%',
        padding: 3,
        overflow: 'auto',
      }}
    >
      <Container maxWidth="xl">
        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 4,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight={700}
                sx={{
                  background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                  fontSize: { xs: '1.8rem', md: '2.5rem' },
                }}
              >
                🏗️ Plant & Machinery Tracking
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Monitor and track your machinery operations with real-time analytics
              </Typography>
            </Box>

            <Stack direction="row" spacing={2}>
              <Tooltip title="Export Data">
                <IconButton
                  onClick={handleExportLogs}
                  sx={{
                    background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1e88e5 0%, #1565c0 100%)',
                      transform: 'scale(1.05)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <DownloadIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={fetchLogs}
                  sx={{
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                      transform: 'scale(1.05)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>

              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleOpenDialog}
                sx={{
                  borderRadius: 3,
                  textTransform: 'none',
                  px: 3,
                  py: 1.5,
                  background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                  boxShadow: '0 4px 20px rgba(76, 175, 80, 0.3)',
                  fontWeight: 600,
                  '&:hover': {
                    background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 25px rgba(76, 175, 80, 0.4)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Add Machinery Log
              </Button>
            </Stack>
          </Stack>
        </Paper>

        {/* Statistics Dashboard */}
        <Grid container spacing={3} mb={3}>
          {[
            {
              label: 'Total Machines',
              value: stats.total,
              icon: <ConstructionIcon />,
              gradient: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
              change: '+12%'
            },
            {
              label: 'Working',
              value: stats.working,
              icon: <SettingsIcon />,
              gradient: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
              change: '+8%'
            },
            {
              label: 'Breakdown',
              value: stats.breakdown,
              icon: <AssessmentIcon />,
              gradient: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
              change: '-15%'
            },
            {
              label: 'Maintenance',
              value: stats.maintenance,
              icon: <ScheduleIcon />,
              gradient: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
              change: '+5%'
            },
            {
              label: 'Total Fuel Used',
              value: `${stats.totalFuelUsed}L`,
              icon: <FuelIcon />,
              gradient: 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
              change: '+20%'
            },
            {
              label: 'Total Hours',
              value: `${stats.totalHours}h`,
              icon: <TrendingUpIcon />,
              gradient: 'linear-gradient(135deg, #00bcd4 0%, #0097a7 100%)',
              change: '+18%'
            },
          ].map((stat, index) => (
            <Grid item xs={12} sm={6} md={4} lg={2} key={stat.label}>
              <Zoom in timeout={300 + index * 100}>
                <Card
                  sx={{
                    background: 'rgba(255, 255, 255, 0.98)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    borderRadius: 4,
                    overflow: 'hidden',
                    position: 'relative',
                    boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 40px rgba(25, 118, 210, 0.2)',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 4,
                      background: stat.gradient,
                    },
                  }}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="h6" fontWeight={700} color="#1976d2">
                          {stat.value}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" fontWeight={500}>
                          {stat.label}
                        </Typography>
                        <Chip
                          label={stat.change}
                          size="small"
                          sx={{
                            mt: 0.5,
                            backgroundColor: stat.change.startsWith('+') ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
                            color: stat.change.startsWith('+') ? '#4caf50' : '#f44336',
                            fontWeight: 600,
                            fontSize: '0.7rem',
                          }}
                        />
                      </Box>
                      <Avatar
                        sx={{
                          background: stat.gradient,
                          color: 'white',
                          width: 40,
                          height: 40,
                          boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                        }}
                      >
                        {stat.icon}
                      </Avatar>
                    </Stack>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>

        {/* Filters and Search Section */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 3,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search by company, machine, location, or description..."
                value={search}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    },
                  },
                }}
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Condition Filter</InputLabel>
                <Select
                  value={conditionFilter}
                  label="Condition Filter"
                  onChange={(e) => setConditionFilter(e.target.value)}
                  sx={{
                    borderRadius: 3,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    },
                  }}
                >
                  <MenuItem value="all">All Conditions</MenuItem>
                  <MenuItem value="Working">Working</MenuItem>
                  <MenuItem value="Breakdown">Breakdown</MenuItem>
                  <MenuItem value="Maintenance">Maintenance</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Fuel Type Filter</InputLabel>
                <Select
                  value={fuelTypeFilter}
                  label="Fuel Type Filter"
                  onChange={(e) => setFuelTypeFilter(e.target.value)}
                  sx={{
                    borderRadius: 3,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1976d2',
                    },
                  }}
                >
                  <MenuItem value="all">All Fuel Types</MenuItem>
                  <MenuItem value="Diesel">Diesel</MenuItem>
                  <MenuItem value="Petrol">Petrol</MenuItem>
                  <MenuItem value="Electric">Electric</MenuItem>
                  <MenuItem value="Hybrid">Hybrid</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>
        {/* Content Area */}
        {loading ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 8,
            }}
          >
            <CircularProgress
              size={60}
              thickness={4}
              sx={{ color: 'white' }}
            />
            <Typography variant="h6" sx={{ mt: 2, color: 'white', fontWeight: 500 }}>
              Loading machinery logs...
            </Typography>
          </Box>
        ) : (
          <Paper
            elevation={0}
            sx={{
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              border: '1px solid rgba(255, 255, 255, 0.3)',
              overflow: 'hidden',
              boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
            }}
          >
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow
                    sx={{
                      background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
                    }}
                  >
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Date</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Company</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Machine</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Description</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Condition</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Location</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Fuel Type</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Fuel Used</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>From</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>To</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Hours</TableCell>
                    <TableCell sx={{ fontWeight: 700, color: '#1976d2' }}>Remarks</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {!Array.isArray(filteredLogs) || filteredLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={12} sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          📋 No machinery logs found
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {search || conditionFilter !== 'all' || fuelTypeFilter !== 'all'
                            ? 'Try adjusting your filters to see more results'
                            : 'No machinery logs have been recorded yet'
                          }
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedLogs.map((log, index) => (
                      <Fade in timeout={300 + index * 50} key={log._id}>
                        <TableRow
                          hover
                          sx={{
                            '&:hover': {
                              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(21, 101, 192, 0.02) 100%)',
                              transform: 'scale(1.01)',
                            },
                            transition: 'all 0.2s ease',
                            cursor: 'pointer',
                          }}
                        >
                          <TableCell>
                            <Typography variant="body2" fontWeight={500}>
                              {log.date ? new Date(log.date).toLocaleDateString() : 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <BusinessIcon fontSize="small" color="primary" />
                              <Typography variant="body2" fontWeight={600}>
                                {log.company}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <ConstructionIcon fontSize="small" color="primary" />
                              <Typography variant="body2" fontWeight={600}>
                                {log.machineName}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {log.description || 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={log.condition}
                              size="small"
                              color={
                                log.condition === 'Working' ? 'success' :
                                log.condition === 'Breakdown' ? 'error' :
                                log.condition === 'Maintenance' ? 'warning' : 'default'
                              }
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <LocationIcon fontSize="small" color="primary" />
                              <Typography variant="body2">
                                {log.location}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <FuelIcon fontSize="small" color="primary" />
                              <Typography variant="body2">
                                {log.fuelType}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight={600} color="primary">
                              {log.fuelUsedLiters}L
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {log.workingFrom}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {log.workingTo}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" alignItems="center" spacing={0.5}>
                              <ScheduleIcon fontSize="small" color="success" />
                              <Typography variant="body2" fontWeight={600} color="success.main">
                                {log.totalHoursWorked}h
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {log.remarks || 'N/A'}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      </Fade>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* Pagination */}
            <Box sx={{
              p: 3,
              borderTop: '1px solid rgba(25, 118, 210, 0.1)',
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.02) 0%, rgba(21, 101, 192, 0.01) 100%)'
            }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  Showing {startIndex + 1}-{Math.min(endIndex, filteredLogs.length)} of {filteredLogs.length} logs
                </Typography>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Button
                    variant="outlined"
                    size="small"
                    disabled={page === 1}
                    onClick={() => setPage(page - 1)}
                    sx={{
                      borderRadius: 2,
                      borderColor: '#1976d2',
                      color: '#1976d2',
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.1)',
                      },
                    }}
                  >
                    Previous
                  </Button>
                  <Typography variant="body2" sx={{ mx: 2 }}>
                    Page {page} of {totalPages}
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    disabled={page >= totalPages}
                    onClick={() => setPage(page + 1)}
                    sx={{
                      borderRadius: 2,
                      borderColor: '#1976d2',
                      color: '#1976d2',
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.1)',
                      },
                    }}
                  >
                    Next
                  </Button>
                </Stack>
              </Stack>
            </Box>
          </Paper>
        )}
        {/* Add/Edit Log Dialog */}
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 4,
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              boxShadow: '0 20px 60px rgba(25, 118, 210, 0.3)',
            }
          }}
        >
          <DialogTitle
            sx={{
              fontWeight: 700,
              color: '#1976d2',
              pb: 1,
              background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
              borderRadius: '16px 16px 0 0',
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                  width: 40,
                  height: 40,
                }}
              >
                <ConstructionIcon />
              </Avatar>
              <Typography variant="h6" fontWeight={700}>
                Add Machinery Work Log
              </Typography>
            </Stack>
          </DialogTitle>
          <DialogContent sx={{ pt: 3, pb: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Company"
                  name="company"
                  value={form.company}
                  onChange={handleChange}
                  required
                  fullWidth
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Machine Name"
                  name="machineName"
                  value={form.machineName}
                  onChange={handleChange}
                  required
                  fullWidth
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 3,
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#1976d2',
                      },
                    },
                  }}
                />
              </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField label="Description" name="description" value={form.description} onChange={handleChange} fullWidth variant="outlined" size="small"
                InputLabelProps={{
                  // Removed bold styling from label
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <FormControl fullWidth required variant="outlined" size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              >
                <InputLabel>Condition</InputLabel>
                <Select label="Condition" name="condition" value={form.condition} onChange={handleChange}>
                  {conditions.map(c => <MenuItem key={c} value={c}>{c}</MenuItem>)}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField label="Location" name="location" value={form.location} onChange={handleChange} required fullWidth variant="outlined" size="small"
                InputLabelProps={{
                  // Removed bold styling from label
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <FormControl fullWidth required variant="outlined" size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              >
                <InputLabel>Fuel Type</InputLabel>
                <Select label="Fuel Type" name="fuelType" value={form.fuelType} onChange={handleChange}>
                  {fuelTypes.map(f => <MenuItem key={f} value={f}>{f}</MenuItem>)}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField label="Fuel Used (liters)" name="fuelUsedLiters" type="number" value={form.fuelUsedLiters} onChange={handleChange} required fullWidth variant="outlined" size="small"
                InputLabelProps={{
                  // Removed bold styling from label
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField label="Date" name="date" type="date" value={form.date} onChange={handleChange} fullWidth InputLabelProps={{ shrink: true }} variant="outlined" size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField label="Working From (HH:mm)" name="workingFrom" type="time" value={form.workingFrom} onChange={handleChange} required fullWidth InputLabelProps={{ shrink: true }} variant="outlined" size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField label="Working To (HH:mm)" name="workingTo" type="time" value={form.workingTo} onChange={handleChange} required fullWidth InputLabelProps={{ shrink: true }} variant="outlined" size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField label="Total Hours Worked" name="totalHoursWorked" type="number" value={form.totalHoursWorked} onChange={handleChange} required fullWidth variant="outlined" size="small"
                InputLabelProps={{
                  // Removed bold styling from label
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <TextField label="Remarks" name="remarks" value={form.remarks} onChange={handleChange} fullWidth variant="outlined" size="small"
                InputLabelProps={{
                  // Removed bold styling from label
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                  }
                }}
              />
            </Grid>
          </Grid>
        </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleCloseDialog}
              variant="outlined"
              sx={{
                fontWeight: 600,
                borderRadius: 3,
                px: 3,
                borderColor: '#bdbdbd',
                color: '#757575',
                '&:hover': {
                  borderColor: '#9e9e9e',
                  backgroundColor: 'rgba(189, 189, 189, 0.1)',
                },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                fontWeight: 700,
                borderRadius: 3,
                px: 4,
                background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                  boxShadow: '0 6px 25px rgba(25, 118, 210, 0.4)',
                },
              }}
            >
              Add Machinery Log
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{
              width: '100%',
              borderRadius: 3,
              backdropFilter: 'blur(20px)',
              background: snackbar.severity === 'success'
                ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.95) 0%, rgba(56, 142, 60, 0.95) 100%)'
                : 'linear-gradient(135deg, rgba(244, 67, 54, 0.95) 0%, rgba(211, 47, 47, 0.95) 100%)',
              color: 'white',
              fontWeight: 600,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
            }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
}