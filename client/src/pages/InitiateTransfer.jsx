import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Grid,
  Snackbar,
  Alert,
  IconButton,
  Divider,
  FormHelperText,
  Tooltip,
  Fade,
  Card,
  CardContent,
  Stack,
  Avatar,
  Chip,
  Container,
  CircularProgress,
  LinearProgress,
  Zoom
} from '@mui/material';
import {
  Send as SendIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Inventory2 as InventoryIcon,
  InfoOutlined as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Store as StoreIcon,
  SwapHoriz as SwapIcon,
  ShoppingCart as CartIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { getApiUrl } from '../config';

const dropdownSx = {
  borderRadius: 3,
  boxShadow: '0 4px 20px rgba(25, 118, 210, 0.15)',
  background: 'rgba(255, 255, 255, 0.98)',
  backdropFilter: 'blur(20px)',
  minHeight: 56,
  '& .MuiOutlinedInput-notchedOutline': {
    borderRadius: 3,
    borderColor: 'rgba(25, 118, 210, 0.3)',
    borderWidth: 2,
  },
  '& .MuiSelect-icon': {
    color: '#1976d2',
    fontSize: 28,
  },
  '& .MuiInputLabel-root': {
    fontWeight: 600,
    color: '#1976d2',
  },
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: '#1976d2',
  },
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: '#1976d2',
    boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.1)',
  },
};

const InitiateTransfer = () => {
  const [products, setProducts] = useState([]);
  const [stores, setStores] = useState([]);
  const [items, setItems] = useState([{ productId: '', quantity: '' }]);
  const [fromStoreId, setFromStoreId] = useState('');
  const [toStoreId, setToStoreId] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchProducts();
    fetchStores();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await fetch(getApiUrl('api/product/'));
      if (!response.ok) throw new Error();
      const result = await response.json();
      if (result.success) setProducts(result.data);
    } catch {
      showSnackbar('Error fetching products.', 'error');
    }
  };

  const fetchStores = async () => {
    try {
      const response = await fetch(getApiUrl('api/store'));
      if (!response.ok) throw new Error();
      const data = await response.json();
      setStores(data);
    } catch {
      showSnackbar('Error fetching stores.', 'error');
    }
  };

  const handleItemChange = (index, e) => {
    const { name, value } = e.target;
    setItems(items => items.map((item, i) => i === index ? { ...item, [name]: value } : item));
  };

  const handleStoreChange = (e) => {
    const { name, value } = e.target;
    if (name === 'fromStoreId') {
      setFromStoreId(value);
      setItems([{ productId: '', quantity: '' }]);
    } else if (name === 'toStoreId') {
      setToStoreId(value);
    }
  };

  const handleAddItem = () => {
    setItems([...items, { productId: '', quantity: '' }]);
  };

  const handleRemoveItem = (index) => {
    setItems(items => items.filter((_, i) => i !== index));
  };

  const showSnackbar = (message, severity) => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => setSnackbarOpen(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('api/transfers/request/'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          items: items.map(item => ({ productId: item.productId, quantity: Number(item.quantity) })),
          fromStoreId,
          toStoreId,
        })
      });
      const result = await response.json();
      if (response.ok) {
        showSnackbar('Transfer request initiated successfully!', 'success');
        setItems([{ productId: '', quantity: '' }]);
        setFromStoreId('');
        setToStoreId('');
      } else {
        showSnackbar(result.message || 'Failed to initiate transfer request.', 'error');
      }
    } catch {
      showSnackbar('Error initiating transfer request.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const getAvailableQuantity = (productId) => {
    if (!productId || !fromStoreId) return 0;
    const product = products.find(p => p._id === productId && p.store_id === fromStoreId);
    return product ? product.stock : 0;
  };

  const isQuantityError = (item) => {
    if (!item.quantity || !item.productId) return false;
    const availableQuantity = getAvailableQuantity(item.productId);
    return Number(item.quantity) > availableQuantity;
  };

  const isFormValid = () => {
    if (!fromStoreId || !toStoreId) return false;
    if (fromStoreId === toStoreId) return false;
    if (items.length === 0) return false;
    for (const item of items) {
      if (!item.productId || !item.quantity) return false;
      if (isNaN(Number(item.quantity)) || Number(item.quantity) <= 0) return false;
      if (isQuantityError(item)) return false;
    }
    return true;
  };

  const getFilteredProducts = () => {
    if (!fromStoreId) return [];
    return products.filter(product => product.store_id === fromStoreId);
  };

  const canAddAnother = () => {
    if (items.length === 0) return false;
    const last = items[items.length - 1];
    return last.productId && last.quantity && !isNaN(Number(last.quantity)) && Number(last.quantity) > 0;
  };

  return (
    <Box
      sx={{
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        height: '100%',
        padding: 3,
        overflow: 'auto',
      }}
    >
      <Container maxWidth="xl">
        {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 4,
            mb: 3,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
            <Box>
              <Typography
                variant="h4"
                fontWeight={700}
                sx={{
                  background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1976d2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1,
                  fontSize: { xs: '1.8rem', md: '2.5rem' },
                }}
              >
                🚀 Initiate Product Transfer
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Move products seamlessly between your store locations with real-time tracking
              </Typography>
            </Box>

            <Stack direction="row" spacing={2}>
              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={() => {
                    fetchProducts();
                    fetchStores();
                  }}
                  sx={{
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                      transform: 'scale(1.05)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Stack>
          </Stack>
        </Paper>

        {/* Statistics Cards */}
        <Grid container spacing={3} mb={3}>
          {[
            {
              label: 'Available Products',
              value: products.length,
              icon: <InventoryIcon />,
              gradient: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)'
            },
            {
              label: 'Store Locations',
              value: stores.length,
              icon: <StoreIcon />,
              gradient: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)'
            },
            {
              label: 'Items to Transfer',
              value: items.filter(item => item.productId && item.quantity).length,
              icon: <CartIcon />,
              gradient: 'linear-gradient(135deg, #1e88e5 0%, #1976d2 100%)'
            },
          ].map((stat, index) => (
            <Grid item xs={12} sm={4} key={stat.label}>
              <Zoom in timeout={300 + index * 100}>
                <Card
                  sx={{
                    background: 'rgba(255, 255, 255, 0.98)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    borderRadius: 4,
                    overflow: 'hidden',
                    position: 'relative',
                    boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 40px rgba(25, 118, 210, 0.2)',
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 4,
                      background: stat.gradient,
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography variant="h4" fontWeight={700} color="#1976d2">
                          {stat.value}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" fontWeight={500}>
                          {stat.label}
                        </Typography>
                      </Box>
                      <Avatar
                        sx={{
                          background: stat.gradient,
                          color: 'white',
                          width: 64,
                          height: 64,
                          boxShadow: '0 4px 20px rgba(25, 118, 210, 0.3)',
                        }}
                      >
                        {stat.icon}
                      </Avatar>
                    </Stack>
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          ))}
        </Grid>

        {/* Main Form */}
        <Paper
          elevation={0}
          sx={{
            background: 'rgba(255, 255, 255, 0.98)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: 4,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.15)',
          }}
        >
          <form onSubmit={handleSubmit} autoComplete="off">
            {/* Store Selection Section */}
            <Box sx={{ mb: 4 }}>
              <Stack direction="row" alignItems="center" spacing={1} mb={3}>
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                    width: 40,
                    height: 40,
                  }}
                >
                  <SwapIcon />
                </Avatar>
                <Typography variant="h6" fontWeight={600} color="#1976d2">
                  Select Transfer Route
                </Typography>
              </Stack>

              <Card
                sx={{
                  background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(21, 101, 192, 0.02) 100%)',
                  border: '2px solid rgba(25, 118, 210, 0.1)',
                  borderRadius: 4,
                  p: 3,
                  boxShadow: '0 4px 20px rgba(25, 118, 210, 0.1)',
                }}
              >
                <Grid container spacing={3} alignItems="center">
                  <Grid item xs={12} md={5}>
                    <FormControl fullWidth variant="outlined">
                      <InputLabel>From Store</InputLabel>
                      <Select
                        name="fromStoreId"
                        value={fromStoreId}
                        onChange={handleStoreChange}
                        label="From Store"
                        disabled={isLoading}
                        sx={dropdownSx}
                      >
                        <MenuItem value=""><em>Select Source Store</em></MenuItem>
                        {stores.map((store) => (
                          <MenuItem key={store._id} value={store._id}>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <StoreIcon fontSize="small" color="primary" />
                              <Box>
                                <Typography variant="body2" fontWeight={600}>
                                  {store.storeName}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {store.storeLocation}
                                </Typography>
                              </Box>
                            </Stack>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={2} sx={{ textAlign: 'center' }}>
                    <Avatar
                      sx={{
                        background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%)',
                        mx: 'auto',
                        animation: fromStoreId && toStoreId ? 'pulse 2s infinite' : 'none',
                        '@keyframes pulse': {
                          '0%': { transform: 'scale(1)' },
                          '50%': { transform: 'scale(1.1)' },
                          '100%': { transform: 'scale(1)' },
                        },
                      }}
                    >
                      <SwapIcon />
                    </Avatar>
                  </Grid>

                  <Grid item xs={12} md={5}>
                    <FormControl fullWidth variant="outlined">
                      <InputLabel>To Store</InputLabel>
                      <Select
                        name="toStoreId"
                        value={toStoreId}
                        onChange={handleStoreChange}
                        label="To Store"
                        disabled={isLoading}
                        sx={dropdownSx}
                      >
                        <MenuItem value=""><em>Select Destination Store</em></MenuItem>
                        {stores.filter(store => store._id !== fromStoreId).map((store) => (
                          <MenuItem key={store._id} value={store._id}>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <StoreIcon fontSize="small" color="primary" />
                              <Box>
                                <Typography variant="body2" fontWeight={600}>
                                  {store.storeName}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {store.storeLocation}
                                </Typography>
                              </Box>
                            </Stack>
                          </MenuItem>
                        ))}
                      </Select>
                      <FormHelperText>
                        Choose a different store to receive the products
                      </FormHelperText>
                    </FormControl>
                  </Grid>
                </Grid>

                {fromStoreId && toStoreId && (
                  <Fade in>
                    <Box sx={{ mt: 3, p: 2, background: 'rgba(25, 118, 210, 0.05)', borderRadius: 2 }}>
                      <Typography variant="body2" color="primary" fontWeight={600}>
                        ✅ Transfer route configured successfully!
                      </Typography>
                    </Box>
                  </Fade>
                )}
              </Card>
            </Box>

            {/* Items Section */}
            <Box sx={{ mb: 4 }}>
              <Stack direction="row" alignItems="center" spacing={1} mb={3}>
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #1e88e5 0%, #1976d2 100%)',
                    width: 40,
                    height: 40,
                  }}
                >
                  <CartIcon />
                </Avatar>
                <Typography variant="h6" fontWeight={600} color="#1976d2">
                  Products to Transfer
                </Typography>
                <Tooltip
                  title="Add products and quantities to transfer. Each product must be available in the selected store."
                  arrow
                >
                  <InfoIcon color="action" fontSize="small" />
                </Tooltip>
              </Stack>

              <Grid container direction="column" spacing={3} mb={3}>
                {items.map((item, index) => (
                  <Fade in key={index} timeout={400 + index * 100}>
                    <Grid item>
                      <Card
                        sx={{
                          background: 'rgba(255, 255, 255, 0.98)',
                          backdropFilter: 'blur(20px)',
                          border: '2px solid rgba(25, 118, 210, 0.1)',
                          borderRadius: 4,
                          boxShadow: '0 8px 32px rgba(25, 118, 210, 0.12)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 12px 40px rgba(25, 118, 210, 0.2)',
                            borderColor: 'rgba(25, 118, 210, 0.3)',
                          },
                        }}
                      >
                        <CardContent sx={{ p: 3 }}>
                          <Stack direction="row" spacing={2} alignItems="flex-start">
                            <Box sx={{ flex: 1 }}>
                              <FormControl fullWidth variant="outlined">
                                <InputLabel>Product *</InputLabel>
                                <Select
                                  name="productId"
                                  value={item.productId}
                                  onChange={(e) => handleItemChange(index, e)}
                                  label="Product *"
                                  disabled={isLoading || !fromStoreId}
                                  sx={dropdownSx}
                                  renderValue={selected => {
                                    if (!selected) return <span style={{ color: '#aaa' }}>Select a Product</span>;
                                    const product = getFilteredProducts().find(p => p._id === selected);
                                    return (
                                      <Stack direction="row" alignItems="center" spacing={1}>
                                        <Typography variant="body2" fontWeight={600}>
                                          {product ? `${product.name}` : ''}
                                        </Typography>
                                        {product && (
                                          <>
                                            <Chip
                                              label={`SKU: ${product.sku}`}
                                              size="small"
                                              sx={{
                                                backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                                color: '#1976d2',
                                                fontWeight: 600,
                                              }}
                                            />
                                            <Chip
                                              label={`Stock: ${product.stock}`}
                                              size="small"
                                              sx={{
                                                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                                                color: '#4caf50',
                                                fontWeight: 600,
                                              }}
                                            />
                                            <CheckCircleIcon sx={{ color: '#4caf50', fontSize: 20 }} />
                                          </>
                                        )}
                                      </Stack>
                                    );
                                  }}
                                >
                                  <MenuItem value=""><em>Select a Product</em></MenuItem>
                                  {getFilteredProducts().map((product) => (
                                    <MenuItem key={product._id} value={product._id}>
                                      <Stack direction="row" alignItems="center" spacing={1} sx={{ width: '100%' }}>
                                        <InventoryIcon fontSize="small" color="primary" />
                                        <Box sx={{ flex: 1 }}>
                                          <Typography variant="body2" fontWeight={600}>
                                            {product.name}
                                          </Typography>
                                          <Typography variant="caption" color="text.secondary">
                                            SKU: {product.sku} • Stock: {product.stock} units
                                          </Typography>
                                        </Box>
                                        <Chip
                                          label={product.stock > 10 ? 'In Stock' : 'Low Stock'}
                                          size="small"
                                          color={product.stock > 10 ? 'success' : 'warning'}
                                          variant="outlined"
                                        />
                                      </Stack>
                                    </MenuItem>
                                  ))}
                                </Select>
                                {!fromStoreId && (
                                  <FormHelperText>Please select a source store first</FormHelperText>
                                )}
                              </FormControl>
                            </Box>
                            <Box sx={{ width: 200 }}>
                              <TextField
                                name="quantity"
                                label="Quantity *"
                                type="number"
                                value={item.quantity}
                                onChange={(e) => handleItemChange(index, e)}
                                fullWidth
                                variant="outlined"
                                placeholder="Enter qty"
                                inputProps={{ min: 1 }}
                                disabled={isLoading || !item.productId}
                                error={isQuantityError(item)}
                                helperText={item.productId ?
                                  (isQuantityError(item)
                                    ? `Exceeds stock (${getAvailableQuantity(item.productId)})`
                                    : `Max: ${getAvailableQuantity(item.productId)}`)
                                  : ""
                                }
                                sx={{
                                  ...dropdownSx,
                                  '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: !isQuantityError(item) && item.quantity ? '#4caf50' : undefined,
                                    borderWidth: !isQuantityError(item) && item.quantity ? 2 : undefined,
                                  },
                                }}
                              />
                            </Box>

                            <Tooltip title="Remove Product" arrow>
                              <IconButton
                                onClick={() => handleRemoveItem(index)}
                                disabled={isLoading}
                                sx={{
                                  background: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
                                  color: 'white',
                                  '&:hover': {
                                    background: 'linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%)',
                                    transform: 'scale(1.1)',
                                  },
                                  transition: 'all 0.2s ease',
                                }}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </Stack>

                          {/* Progress indicator for this item */}
                          {item.productId && item.quantity && (
                            <Box sx={{ mt: 2 }}>
                              <LinearProgress
                                variant="determinate"
                                value={Math.min((Number(item.quantity) / getAvailableQuantity(item.productId)) * 100, 100)}
                                sx={{
                                  height: 6,
                                  borderRadius: 3,
                                  backgroundColor: 'rgba(25, 118, 210, 0.1)',
                                  '& .MuiLinearProgress-bar': {
                                    backgroundColor: isQuantityError(item) ? '#f44336' : '#4caf50',
                                    borderRadius: 3,
                                  },
                                }}
                              />
                              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                                {isQuantityError(item)
                                  ? 'Quantity exceeds available stock'
                                  : `${Number(item.quantity)} of ${getAvailableQuantity(item.productId)} units selected`
                                }
                              </Typography>
                            </Box>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  </Fade>
                ))}
              </Grid>

              {/* Add Another Product Button */}
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
                <Button
                  startIcon={<AddIcon />}
                  onClick={handleAddItem}
                  variant="outlined"
                  size="large"
                  sx={{
                    borderRadius: 3,
                    fontWeight: 600,
                    px: 4,
                    py: 1.5,
                    border: canAddAnother() ? '2px solid #1976d2' : '2px dashed #bdbdbd',
                    color: canAddAnother() ? '#1976d2' : '#bdbdbd',
                    background: canAddAnother()
                      ? 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)'
                      : '#f5f5f5',
                    boxShadow: canAddAnother() ? '0 4px 20px rgba(25, 118, 210, 0.2)' : 'none',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      background: canAddAnother()
                        ? 'linear-gradient(135deg, rgba(25, 118, 210, 0.15) 0%, rgba(21, 101, 192, 0.1) 100%)'
                        : '#f5f5f5',
                      border: canAddAnother() ? '2px solid #1565c0' : '2px dashed #bdbdbd',
                      transform: canAddAnother() ? 'translateY(-2px)' : 'none',
                      boxShadow: canAddAnother() ? '0 6px 25px rgba(25, 118, 210, 0.3)' : 'none',
                    },
                  }}
                  disabled={!canAddAnother() || isLoading || !fromStoreId}
                >
                  Add Another Product
                </Button>
              </Box>
            </Box>

            {/* Submit Section */}
            <Box sx={{ mt: 4, pt: 3, borderTop: '2px solid rgba(25, 118, 210, 0.1)' }}>
              <Stack direction="row" alignItems="center" spacing={2} mb={3}>
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                    width: 40,
                    height: 40,
                  }}
                >
                  <SendIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" fontWeight={600} color="#1976d2">
                    Ready to Transfer
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Review your selection and initiate the transfer
                  </Typography>
                </Box>
              </Stack>

              {isFormValid() && (
                <Card
                  sx={{
                    background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(56, 142, 60, 0.05) 100%)',
                    border: '2px solid rgba(76, 175, 80, 0.2)',
                    borderRadius: 3,
                    p: 2,
                    mb: 3,
                  }}
                >
                  <Typography variant="body2" color="success.main" fontWeight={600}>
                    ✅ Transfer request is ready! {items.filter(item => item.productId && item.quantity).length} product(s) will be transferred.
                  </Typography>
                </Card>
              )}

              <Button
                type="submit"
                startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                variant="contained"
                fullWidth
                size="large"
                sx={{
                  borderRadius: 3,
                  fontWeight: 700,
                  fontSize: '1.2rem',
                  py: 2,
                  background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                  boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',
                  textTransform: 'none',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                    boxShadow: '0 12px 40px rgba(25, 118, 210, 0.4)',
                    transform: 'translateY(-2px)',
                  },
                  '&:disabled': {
                    background: 'linear-gradient(135deg, #bdbdbd 0%, #9e9e9e 100%)',
                    color: 'white',
                  },
                  transition: 'all 0.3s ease',
                }}
                disabled={isLoading || !isFormValid()}
              >
                {isLoading ? 'Processing Transfer...' : 'Initiate Transfer'}
              </Button>
            </Box>
          </form>
        </Paper>
      </Container>
        {/* Snackbar */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbarSeverity}
            sx={{
              width: '100%',
              borderRadius: 3,
              backdropFilter: 'blur(20px)',
              background: snackbarSeverity === 'success'
                ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.95) 0%, rgba(56, 142, 60, 0.95) 100%)'
                : 'linear-gradient(135deg, rgba(244, 67, 54, 0.95) 0%, rgba(211, 47, 47, 0.95) 100%)',
              color: 'white',
              fontWeight: 600,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
            }}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>
    </Box>
  );
};

export default InitiateTransfer; 