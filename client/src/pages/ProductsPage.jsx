import { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  ShoppingCart as CartIcon,
} from '@mui/icons-material';
import { ResponsiveContainer, ResponsiveButtonGroup } from '../components/ResponsiveLayout';
import ResponsiveDialog from '../components/ResponsiveDialog';
import { useResponsive } from '../hooks/useResponsive';

const ProductsPage = () => {
  const { isMobile, isTablet, isTouchDevice } = useResponsive();

  const [searchQuery, setSearchQuery] = useState('');
  const [openAddProductDialog, setOpenAddProductDialog] = useState(false);
  const [newProduct, setNewProduct] = useState({
    name: '',
    price: '',
    image: '',
    stock: '',
    storeName: '',
    storeLocation: '',
  });

  // Sample store items - replace with actual data from your backend
  const [storeItems, setStoreItems] = useState([
    {
      id: 1,
      name: 'Product 1',
      price: 99.99,
      image: 'https://via.placeholder.com/150',
      stock: 10,
      storeName: 'Downtown Branch',
      storeLocation: 'Downtown',
    },
    {
      id: 2,
      name: 'Product 2',
      price: 149.99,
      image: 'https://via.placeholder.com/150',
      stock: 15,
      storeName: 'Uptown Mall',
      storeLocation: 'Uptown',
    },
    // Add more items as needed
  ]);

  const handleOpenAddProductDialog = () => setOpenAddProductDialog(true);
  const handleCloseAddProductDialog = () => setOpenAddProductDialog(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewProduct({ ...newProduct, [name]: value });
  };

  const handleAddProduct = () => {
    // In a real application, you would send this data to your backend
    console.log('Adding new product:', newProduct);
    // For now, add to local state with dummy ID and clear form
    setStoreItems([...storeItems, { ...newProduct, id: Date.now() }]);
    setNewProduct({ name: '', price: '', image: '', stock: '', storeName: '', storeLocation: '' });
    handleCloseAddProductDialog();
  };

  return (
    <ResponsiveContainer>
      <Box sx={{
        mb: isMobile ? 3 : 4,
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        justifyContent: 'space-between',
        alignItems: isMobile ? 'flex-start' : 'center',
        gap: isMobile ? 2 : 0
      }}>
        <Typography
          variant={isMobile ? "h5" : "h4"}
          component="h1"
          gutterBottom={!isMobile}
          sx={{ fontSize: isMobile ? '1.5rem' : '2rem' }}
        >
          Products
        </Typography>
        <Button
          variant="contained"
          startIcon={!isMobile && <AddIcon />}
          color="primary"
          onClick={handleOpenAddProductDialog}
          fullWidth={isMobile}
          sx={{
            minHeight: isTouchDevice ? '44px' : '40px',
            fontSize: isMobile ? '0.875rem' : '1rem'
          }}
        >
          {isMobile && <AddIcon sx={{ mr: 1, fontSize: '1rem' }} />}
          Add New Product
        </Button>
      </Box>

      <Paper sx={{ p: isMobile ? 1.5 : 2, mb: isMobile ? 3 : 4, borderRadius: isMobile ? 2 : 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Search products..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          size={isMobile ? "medium" : "small"}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            sx: {
              minHeight: isTouchDevice ? '48px' : '40px'
            }
          }}
        />
      </Paper>

      <Grid container spacing={isMobile ? 2 : 3}>
        {storeItems.map((item) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={item.id}>
            <Card sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              borderRadius: isMobile ? 2 : 3,
              '&:hover': {
                transform: isMobile ? 'none' : 'translateY(-4px)',
                boxShadow: isMobile ? 'none' : 4
              },
              transition: 'all 0.3s'
            }}>
              <CardMedia
                component="img"
                height={isMobile ? "180" : "200"}
                image={item.image}
                alt={item.name}
              />
              <CardContent sx={{ flexGrow: 1, p: isMobile ? 1.5 : 2 }}>
                <Typography
                  gutterBottom
                  variant={isMobile ? "subtitle1" : "h6"}
                  component="h2"
                  sx={{ fontSize: isMobile ? '1rem' : '1.25rem' }}
                >
                  {item.name}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  gutterBottom
                  sx={{ fontSize: isMobile ? '0.8125rem' : '0.875rem' }}
                >
                  Stock: {item.stock}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  gutterBottom
                  sx={{ fontSize: isMobile ? '0.8125rem' : '0.875rem' }}
                >
                  Store: {item.storeName} ({item.storeLocation})
                </Typography>
                <Typography
                  variant={isMobile ? "h6" : "h6"}
                  color="primary"
                  gutterBottom
                  sx={{ fontSize: isMobile ? '1.125rem' : '1.25rem' }}
                >
                  ${item.price}
                </Typography>
                <Button
                  variant="contained"
                  startIcon={!isMobile && <CartIcon />}
                  fullWidth
                  sx={{
                    mt: 2,
                    minHeight: isTouchDevice ? '44px' : '40px',
                    fontSize: isMobile ? '0.875rem' : '1rem'
                  }}
                >
                  {isMobile && <CartIcon sx={{ mr: 1, fontSize: '1rem' }} />}
                  Add to Cart
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <ResponsiveDialog
        open={openAddProductDialog}
        onClose={handleCloseAddProductDialog}
        title="Add New Product"
        maxWidth="sm"
        actions={
          <ResponsiveButtonGroup spacing={1} fullWidthOnMobile={isMobile}>
            <Button
              onClick={handleCloseAddProductDialog}
              color="secondary"
              sx={{
                minHeight: isTouchDevice ? '44px' : '40px',
                fontSize: isMobile ? '0.875rem' : '1rem'
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddProduct}
              color="primary"
              variant="contained"
              sx={{
                minHeight: isTouchDevice ? '44px' : '40px',
                fontSize: isMobile ? '0.875rem' : '1rem'
              }}
            >
              Add Product
            </Button>
          </ResponsiveButtonGroup>
        }
      >
          <TextField
            autoFocus
            margin="dense"
            name="name"
            label="Product Name"
            type="text"
            fullWidth
            variant="outlined"
            value={newProduct.name}
            onChange={handleInputChange}
            size={isMobile ? "medium" : "small"}
            sx={{
              mb: 2,
              '& .MuiInputBase-root': {
                minHeight: isTouchDevice ? '48px' : '40px'
              }
            }}
          />
           <TextField
            margin="dense"
            name="storeName"
            label="Store Name"
            type="text"
            fullWidth
            variant="outlined"
            value={newProduct.storeName}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="storeLocation"
            label="Store Location"
            type="text"
            fullWidth
            variant="outlined"
            value={newProduct.storeLocation}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="price"
            label="Price"
            type="number"
            fullWidth
            variant="outlined"
            value={newProduct.price}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
           <TextField
            margin="dense"
            name="stock"
            label="Stock"
            type="number"
            fullWidth
            variant="outlined"
            value={newProduct.stock}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
           <TextField
            margin="dense"
            name="image"
            label="Image URL"
            type="text"
            fullWidth
            variant="outlined"
            value={newProduct.image}
            onChange={handleInputChange}
          />
          {/* Add other form fields here */}
        </ResponsiveDialog>
    </ResponsiveContainer>
  );
};

export default ProductsPage; 