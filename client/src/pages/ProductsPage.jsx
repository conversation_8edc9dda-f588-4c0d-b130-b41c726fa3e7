import { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  ShoppingCart as CartIcon,
} from '@mui/icons-material';

const ProductsPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [openAddProductDialog, setOpenAddProductDialog] = useState(false);
  const [newProduct, setNewProduct] = useState({
    name: '',
    price: '',
    image: '',
    stock: '',
    storeName: '',
    storeLocation: '',
  });

  // Sample store items - replace with actual data from your backend
  const [storeItems, setStoreItems] = useState([
    {
      id: 1,
      name: 'Product 1',
      price: 99.99,
      image: 'https://via.placeholder.com/150',
      stock: 10,
      storeName: 'Downtown Branch',
      storeLocation: 'Downtown',
    },
    {
      id: 2,
      name: 'Product 2',
      price: 149.99,
      image: 'https://via.placeholder.com/150',
      stock: 15,
      storeName: 'Uptown Mall',
      storeLocation: 'Uptown',
    },
    // Add more items as needed
  ]);

  const handleOpenAddProductDialog = () => setOpenAddProductDialog(true);
  const handleCloseAddProductDialog = () => setOpenAddProductDialog(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewProduct({ ...newProduct, [name]: value });
  };

  const handleAddProduct = () => {
    // In a real application, you would send this data to your backend
    console.log('Adding new product:', newProduct);
    // For now, add to local state with dummy ID and clear form
    setStoreItems([...storeItems, { ...newProduct, id: Date.now() }]);
    setNewProduct({ name: '', price: '', image: '', stock: '', storeName: '', storeLocation: '' });
    handleCloseAddProductDialog();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Products
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          color="primary"
          onClick={handleOpenAddProductDialog}
        >
          Add New Product
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 4 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Search products..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Paper>

      <Grid container spacing={3}>
        {storeItems.map((item) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={item.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardMedia
                component="img"
                height="200"
                image={item.image}
                alt={item.name}
              />
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography gutterBottom variant="h6" component="h2">
                  {item.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Stock: {item.stock}
                </Typography>
                 <Typography variant="body2" color="text.secondary" gutterBottom>
                  Store: {item.storeName} ({item.storeLocation})
                </Typography>
                <Typography variant="h6" color="primary" gutterBottom>
                  ₹{item.price}
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<CartIcon />}
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  Add to Cart
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Dialog open={openAddProductDialog} onClose={handleCloseAddProductDialog}>
        <DialogTitle>Add New Product</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="name"
            label="Product Name"
            type="text"
            fullWidth
            variant="outlined"
            value={newProduct.name}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
           <TextField
            margin="dense"
            name="storeName"
            label="Store Name"
            type="text"
            fullWidth
            variant="outlined"
            value={newProduct.storeName}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="storeLocation"
            label="Store Location"
            type="text"
            fullWidth
            variant="outlined"
            value={newProduct.storeLocation}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="price"
            label="Price"
            type="number"
            fullWidth
            variant="outlined"
            value={newProduct.price}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
           <TextField
            margin="dense"
            name="stock"
            label="Stock"
            type="number"
            fullWidth
            variant="outlined"
            value={newProduct.stock}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
           <TextField
            margin="dense"
            name="image"
            label="Image URL"
            type="text"
            fullWidth
            variant="outlined"
            value={newProduct.image}
            onChange={handleInputChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddProductDialog} color="secondary">Cancel</Button>
          <Button onClick={handleAddProduct} color="primary" variant="contained">Add Product</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProductsPage; 