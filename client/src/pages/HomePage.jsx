import { useState, useEffect, useMemo } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  useTheme,
  Stack,
  Collapse,
  CircularProgress,
  Tooltip,
  alpha,
  SwipeableDrawer,
  useMediaQuery
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  AccountCircle,
  Dashboard as DashboardIcon,
  BarChart,
  Assessment,
  Equalizer,
  ReceiptLong,
  Logout as LogoutIcon,
  ExpandMore,
  ExpandLess,
  Warehouse,
  Category,
  CompareArrows,
  MoveToInbox,
  Store as StoreIcon,
  LocationOn as LocationOnIcon,
  AppRegistration as AppRegistrationIcon,
  Construction as ConstructionIcon,
  Group,
  VpnKey,
  Engineering,
  Security as SecurityIcon
} from '@mui/icons-material';
import Cookies from 'js-cookie';
import axios from 'axios';
import { getApiUrl } from '../config';
import PremiumLanguageSwitcher from '../components/PremiumLanguageSwitcher';
import '../components/PremiumLanguageSwitcher.css';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveDrawer } from '../hooks/useResponsive';
import { usePermissions, useNavigationPermissions } from '../hooks/usePermissions';

// Responsive drawer configuration will be handled by the hook

// Memoize styles
const getDrawerItemStyles = (theme) => ({
  borderRadius: 2, 
  mb: 1,
  '&.Mui-selected': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.15),
    }
  }
});

const getNestedItemStyles = (theme) => ({
  borderRadius: 2, 
  mb: 1,
  pl: 4,
  '&.Mui-selected': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.15),
    }
  }
});

const HomePage = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const userRole = Cookies.get('userRole');

  // Responsive hooks
  const { isMobile, isTablet, isTouchDevice } = useResponsive();
  const { drawerWidth, drawerVariant } = useResponsiveDrawer();

  // Permission hooks
  const { canView, isAdmin, isSuperAdmin } = usePermissions();
  const navigationItems = useNavigationPermissions();

  // State management
  const [mobileOpen, setMobileOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [inventoryOpen, setInventoryOpen] = useState(false);
  const [securityOpen, setSecurityOpen] = useState(false);
  const [roleInfo, setRoleInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  // Set loading to false since we're using the new permission system
  useEffect(() => {
    setLoading(false);
  }, []);

  // Memoize handlers
  const handleDrawerToggle = () => setMobileOpen(prev => !prev);

  // Close drawer on mobile when navigating
  const handleDrawerClose = () => {
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleLogout = () => {
    Cookies.remove('jwtToken');
    Cookies.remove('email');
    Cookies.remove('userRole');
    navigate('/login');
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    // If page is product-management, navigate to inventory instead
    if (page === 'product-management') {
      navigate(`/app/inventory`);
    } else {
      navigate(`/app/${page}`);
    }
    // Close drawer on mobile after navigation
    handleDrawerClose();
  };

  const handleInventoryClick = () => {
    // Only toggle the submenu without navigating
    setInventoryOpen(!inventoryOpen);
  };

  // Memoize drawer content
  const drawer = useMemo(() => {
    if (loading) {
      return (
        <Box sx={{
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <CircularProgress />
        </Box>
      );
    }

    return (
      <Box sx={{ height: '100%', bgcolor: 'background.paper', boxShadow: 1 }}>
        <Box sx={{
          p: isMobile ? 1 : 1.2,
          display: 'flex',
          alignItems: 'center',
          gap: isMobile ? 1 : 2,
          background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.dark} 90%)`,
          color: 'white',
          mb: 2,
          minHeight: isMobile ? '60px' : '70px'
        }}>
          <Avatar sx={{
            bgcolor: 'white',
            color: 'primary.main',
            width: isMobile ? 40 : 45,
            height: isMobile ? 40 : 45,
            boxShadow: 2
          }} src='https://scontent.fhyd7-1.fna.fbcdn.net/v/t39.30808-6/303292734_447599477389457_5103831908007527092_n.jpg?_nc_cat=105&ccb=1-7&_nc_sid=6ee11a&_nc_ohc=ZaF_9rvxcBkQ7kNvwEXFFtz&_nc_oc=AdlgL9TKgV3cfMPclvhzSH8zUVX2otf_-MeMATSe48uheHzgTMcDMugsyjCt8k5X77M&_nc_zt=23&_nc_ht=scontent.fhyd7-1.fna&_nc_gid=G7GKJlur4fuu1vU8taHtyA&oh=00_AfLddV1fl5FLCSyHoq-eKkcmkrG_moljP0HJxiJon1WokQ&oe=68464B20'/>
          <Typography
            variant={isMobile ? "subtitle1" : "h6"}
            sx={{
              fontWeight: 700,
              fontSize: isMobile ? '1rem' : '1.25rem'
            }}
          >
            {t('app.title')}
          </Typography>
        </Box>

        <List sx={{ px: isMobile ? 1 : 2 }}>
          {/* Dashboard */}
          {canView('dashboard') && (
            <ListItem
              button
              selected={currentPage === 'dashboard'}
              onClick={() => handlePageChange('dashboard')}
              sx={{
                ...getDrawerItemStyles(theme),
                minHeight: isTouchDevice ? '48px' : '40px',
                py: isMobile ? 1.5 : 1
              }}
            >
              <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                <DashboardIcon color={currentPage === 'dashboard' ? 'primary' : 'inherit'} />
              </ListItemIcon>
              <ListItemText
                primary={t('menu.dashboard')}
                primaryTypographyProps={{
                  fontWeight: currentPage === 'dashboard' ? 600 : 500,
                  fontSize: isMobile ? '0.875rem' : '1rem'
                }}
              />
            </ListItem>
          )}

          {/* Inventory Section */}
          {canView('inventory') && (
            <>
              <ListItem
                button
                selected={currentPage === 'inventory' ||
                           currentPage === 'initiate-transfer' ||
                           currentPage === 'received-transfers' ||
                           currentPage === 'store-registration' ||
                           currentPage === 'product-management'}
                onClick={handleInventoryClick}
                sx={{
                  ...getDrawerItemStyles(theme),
                  minHeight: isTouchDevice ? '48px' : '40px',
                  py: isMobile ? 1.5 : 1
                }}
              >
                <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                  <Warehouse
                    color={(currentPage === 'inventory' ||
                            currentPage === 'initiate-transfer' ||
                            currentPage === 'received-transfers' ||
                            currentPage === 'store-registration' ||
                            currentPage === 'product-management') ? 'primary' : 'inherit'}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={t('menu.inventory')}
                  primaryTypographyProps={{
                    fontWeight: (currentPage === 'inventory' ||
                                 currentPage === 'initiate-transfer' ||
                                 currentPage === 'received-transfers' ||
                                 currentPage === 'store-registration' ||
                                 currentPage === 'product-management') ? 600 : 500,
                    fontSize: isMobile ? '0.875rem' : '1rem'
                  }}
                />
                {inventoryOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItem>

              <Collapse in={inventoryOpen} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {/* Product Management */}
                  {canView('products') && (
                    <ListItem
                      button
                      selected={currentPage === 'product-management'}
                      onClick={() => handlePageChange('product-management')}
                      sx={{
                        ...getNestedItemStyles(theme),
                        minHeight: isTouchDevice ? '44px' : '36px'
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                        <Category color={currentPage === 'product-management' ? 'primary' : 'inherit'} />
                      </ListItemIcon>
                      <ListItemText
                        primary={t('menu.productManagement')}
                        primaryTypographyProps={{
                          fontWeight: currentPage === 'product-management' ? 600 : 500,
                          fontSize: isMobile ? '0.8125rem' : '0.875rem'
                        }}
                      />
                    </ListItem>
                  )}

                  {/* Initiate Transfer */}
                  {canView('transfers') && (
                    <ListItem
                      button
                      selected={currentPage === 'initiate-transfer'}
                      onClick={() => handlePageChange('initiate-transfer')}
                      sx={{
                        ...getNestedItemStyles(theme),
                        minHeight: isTouchDevice ? '44px' : '36px'
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                        <CompareArrows color={currentPage === 'initiate-transfer' ? 'primary' : 'inherit'} />
                      </ListItemIcon>
                      <ListItemText
                        primary={t('menu.initiateTransfer')}
                        primaryTypographyProps={{
                          fontWeight: currentPage === 'initiate-transfer' ? 600 : 500,
                          fontSize: isMobile ? '0.8125rem' : '0.875rem'
                        }}
                      />
                    </ListItem>
                  )}

                  {/* Received Transfers */}
                  {canView('transfers') && (
                    <ListItem
                      button
                      selected={currentPage === 'received-transfers'}
                      onClick={() => handlePageChange('received-transfers')}
                      sx={{
                        ...getNestedItemStyles(theme),
                        minHeight: isTouchDevice ? '44px' : '36px'
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                        <MoveToInbox color={currentPage === 'received-transfers' ? 'primary' : 'inherit'} />
                      </ListItemIcon>
                      <ListItemText
                        primary={t('menu.receivedTransfers')}
                        primaryTypographyProps={{
                          fontWeight: currentPage === 'received-transfers' ? 600 : 500,
                          fontSize: isMobile ? '0.8125rem' : '0.875rem'
                        }}
                      />
                    </ListItem>
                  )}

                  {/* Store Registration */}
                  {canView('storeRegistration') && (
                    <ListItem
                      button
                      selected={currentPage === 'store-registration'}
                      onClick={() => handlePageChange('store-registration')}
                      sx={{
                        ...getNestedItemStyles(theme),
                        minHeight: isTouchDevice ? '44px' : '36px'
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                        <AppRegistrationIcon color={currentPage === 'store-registration' ? 'primary' : 'inherit'} />
                      </ListItemIcon>
                      <ListItemText
                        primary={t('menu.storeRegistration')}
                        primaryTypographyProps={{
                          fontWeight: currentPage === 'store-registration' ? 600 : 500,
                          fontSize: isMobile ? '0.8125rem' : '0.875rem'
                        }}
                      />
                    </ListItem>
                  )}
                </List>
              </Collapse>
            </>
          )}

          {/* Security Section */}
          {(canView('users') || canView('roles')) && (
            <>
              <ListItem
                button
                selected={currentPage === 'user-management' || currentPage === 'role-management'}
                onClick={() => setSecurityOpen(!securityOpen)}
                sx={{
                  ...getDrawerItemStyles(theme),
                  minHeight: isTouchDevice ? '48px' : '40px',
                  py: isMobile ? 1.5 : 1
                }}
              >
                <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                  <SecurityIcon
                    color={(currentPage === 'user-management' || currentPage === 'role-management') ? 'primary' : 'inherit'}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={t('menu.security')}
                  primaryTypographyProps={{
                    fontWeight: (currentPage === 'user-management' || currentPage === 'role-management') ? 600 : 500,
                    fontSize: isMobile ? '0.875rem' : '1rem'
                  }}
                />
                {securityOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItem>

              <Collapse in={securityOpen} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {/* User Management */}
                  {canView('users') && (
                    <ListItem
                      button
                      selected={currentPage === 'user-management'}
                      onClick={() => handlePageChange('user-management')}
                      sx={{
                        ...getNestedItemStyles(theme),
                        minHeight: isTouchDevice ? '44px' : '36px'
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                        <Group color={currentPage === 'user-management' ? 'primary' : 'inherit'} />
                      </ListItemIcon>
                      <ListItemText
                        primary={t('menu.userManagement')}
                        primaryTypographyProps={{
                          fontWeight: currentPage === 'user-management' ? 600 : 500,
                          fontSize: isMobile ? '0.8125rem' : '0.875rem'
                        }}
                      />
                    </ListItem>
                  )}

                  {/* Role Management */}
                  {canView('roles') && (
                    <ListItem
                      button
                      selected={currentPage === 'role-management'}
                      onClick={() => handlePageChange('role-management')}
                      sx={{
                        ...getNestedItemStyles(theme),
                        minHeight: isTouchDevice ? '44px' : '36px'
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                        <VpnKey color={currentPage === 'role-management' ? 'primary' : 'inherit'} />
                      </ListItemIcon>
                      <ListItemText
                        primary={t('menu.roleManagement')}
                        primaryTypographyProps={{
                          fontWeight: currentPage === 'role-management' ? 600 : 500,
                          fontSize: isMobile ? '0.8125rem' : '0.875rem'
                        }}
                      />
                    </ListItem>
                  )}
                </List>
              </Collapse>
            </>
          )}

          {/* Bill Details */}
          {canView('billing') && (
            <ListItem
              button
              selected={currentPage === 'bill-details'}
              onClick={() => handlePageChange('bill-details')}
              sx={{
                ...getDrawerItemStyles(theme),
                minHeight: isTouchDevice ? '48px' : '40px',
                py: isMobile ? 1.5 : 1
              }}
            >
              <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                <ReceiptLong color={currentPage === 'bill-details' ? 'primary' : 'inherit'} />
              </ListItemIcon>
              <ListItemText
                primary={t('menu.billDetails')}
                primaryTypographyProps={{
                  fontWeight: currentPage === 'bill-details' ? 600 : 500,
                  fontSize: isMobile ? '0.875rem' : '1rem'
                }}
              />
            </ListItem>
          )}

          {/* Plant & Machinery */}
          {canView('machinery') && (
            <ListItem
              button
              selected={currentPage === 'machinery-work'}
              onClick={() => handlePageChange('machinery-work')}
              sx={{
                ...getDrawerItemStyles(theme),
                minHeight: isTouchDevice ? '48px' : '40px',
                py: isMobile ? 1.5 : 1
              }}
            >
              <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                <ConstructionIcon color={currentPage === 'machinery-work' ? 'primary' : 'inherit'} />
              </ListItemIcon>
              <ListItemText
                primary={t('menu.machineryWork')}
                primaryTypographyProps={{
                  fontWeight: currentPage === 'machinery-work' ? 600 : 500,
                  fontSize: isMobile ? '0.875rem' : '1rem'
                }}
              />
            </ListItem>
          )}

          {/* Charts */}
          {canView('charts') && (
            <ListItem
              button
              selected={currentPage === 'charts'}
              onClick={() => handlePageChange('charts')}
              sx={{
                ...getDrawerItemStyles(theme),
                minHeight: isTouchDevice ? '48px' : '40px',
                py: isMobile ? 1.5 : 1
              }}
            >
              <ListItemIcon sx={{ minWidth: isMobile ? '40px' : '56px' }}>
                <BarChart color={currentPage === 'charts' ? 'primary' : 'inherit'} />
              </ListItemIcon>
              <ListItemText
                primary={t('menu.charts')}
                primaryTypographyProps={{
                  fontWeight: currentPage === 'charts' ? 600 : 500,
                  fontSize: isMobile ? '0.875rem' : '1rem'
                }}
              />
            </ListItem>
          )}
        </List>

        <Divider sx={{ my: 2 }} />
        
        <List sx={{ px: 2 }}>
          <ListItem 
            button
            onClick={handleLogout} 
            sx={{ 
              borderRadius: 2, 
              color: 'error.main',
              '&:hover': {
                backgroundColor: alpha(theme.palette.error.main, 0.1),
              }
            }}
          >
            <ListItemIcon><LogoutIcon color="error" /></ListItemIcon>
            <ListItemText primary={t('menu.logout')} primaryTypographyProps={{ fontWeight: 500 }} />
          </ListItem>
        </List>
      </Box>
    );
  }, [currentPage, inventoryOpen, securityOpen, loading, canView, userRole, isMobile, isTablet, isTouchDevice, theme, t]);

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          bgcolor: 'background.paper',
          borderBottom: 1,
          borderColor: 'divider',
          backdropFilter: 'blur(8px)',
          zIndex: theme.zIndex.drawer + 1
        }}
        elevation={0}
      >
        <Toolbar sx={{ minHeight: isMobile ? '56px' : '64px' }}>
          <IconButton
            color="primary"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              mr: 2,
              display: { sm: 'none' },
              minWidth: isTouchDevice ? '48px' : '40px',
              minHeight: isTouchDevice ? '48px' : '40px'
            }}
          >
            <MenuIcon />
          </IconButton>
          <Box sx={{ flexGrow: 1 }} />
          <Box sx={{ mr: isMobile ? 1 : 2 }}>
            <PremiumLanguageSwitcher />
          </Box>
          <Avatar sx={{
            bgcolor: theme.palette.primary.main,
            cursor: 'pointer',
            transition: '0.3s',
            width: isMobile ? 36 : 40,
            height: isMobile ? 36 : 40,
            fontSize: isMobile ? '1rem' : '1.25rem',
            '&:hover': {
              transform: 'scale(1.1)',
            }
          }}>
            {userRole ? userRole.charAt(0).toUpperCase() : 'U'}
          </Avatar>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        {isMobile ? (
          <SwipeableDrawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            onOpen={handleDrawerToggle}
            disableBackdropTransition={!isTouchDevice}
            disableDiscovery={isTouchDevice}
            ModalProps={{
              keepMounted: true,
            }}
            sx={{
              display: { xs: 'block', sm: 'none' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: drawerWidth,
                borderRight: 'none',
                boxShadow: 3,
                maxWidth: '85vw'
              },
            }}
          >
            {drawer}
          </SwipeableDrawer>
        ) : (
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', sm: 'block' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: drawerWidth,
                borderRight: 'none',
                boxShadow: 3
              },
            }}
            open
          >
            {drawer}
          </Drawer>
        )}
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          mt: isMobile ? 7 : 8,
          bgcolor: alpha(theme.palette.primary.main, 0.02),
          minHeight: '100vh',
          p: isMobile ? 1 : isTablet ? 2 : 3,
          overflow: 'auto',
          // Safe area insets for devices with notches
          paddingBottom: isMobile ? 'max(env(safe-area-inset-bottom), 16px)' : 3
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
};

export default HomePage;