import { useState, useEffect, useMemo } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  useTheme,
  Stack,
  Collapse,
  CircularProgress,
  Tooltip,
  alpha
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  AccountCircle,
  Dashboard as DashboardIcon,
  BarChart,
  Assessment,
  Equalizer,
  ReceiptLong,
  Logout as LogoutIcon,
  ExpandMore,
  ExpandLess,
  Warehouse,
  Category,
  CompareArrows,
  MoveToInbox,
  Store as StoreIcon,
  LocationOn as LocationOnIcon,
  AppRegistration as AppRegistrationIcon,
  Construction as ConstructionIcon,
  Group,
  VpnKey,
  Engineering,
  Security as SecurityIcon
} from '@mui/icons-material';
import Cookies from 'js-cookie';
import axios from 'axios';
import { getApiUrl } from '../config';
import PremiumLanguageSwitcher from '../components/PremiumLanguageSwitcher';
import '../components/PremiumLanguageSwitcher.css';
import { useTranslation } from 'react-i18next';

// Memoize static values
const drawerWidth = 280;

// Memoize styles
const getDrawerItemStyles = (theme) => ({
  borderRadius: 2, 
  mb: 1,
  '&.Mui-selected': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.15),
    }
  }
});

const getNestedItemStyles = (theme) => ({
  borderRadius: 2, 
  mb: 1,
  pl: 4,
  '&.Mui-selected': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.15),
    }
  }
});

const HomePage = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();
  const userRole = Cookies.get('userRole');
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [inventoryOpen, setInventoryOpen] = useState(false);
  const [securityOpen, setSecurityOpen] = useState(false);
  const [roleInfo, setRoleInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch role info and permissions when component mounts
  useEffect(() => {
    const fetchRoleInfo = async () => {
      try {
        setLoading(true);
        const response = await axios.get(getApiUrl(`api/roles`));
        if (response.data && response.data.length > 0) {
          // Find the role that matches the current user's role
          const userRoleInfo = response.data.find(role => role.name === userRole);
          if (userRoleInfo) {
            setRoleInfo(userRoleInfo);
          }
        }
      } catch (error) {
        console.error('Error fetching role info:', error);
        // If role info can't be fetched, use default permissions
        setRoleInfo({
          permissions: {
            inventory: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' },
            transfers: { initiate: userRole !== 'user', receive: userRole !== 'user', view: true },
            storeRegistration: { view: userRole !== 'user', manage: userRole === 'admin' || userRole === 'superadmin' },
            products: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' },
            users: { view: userRole === 'admin' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' },
            billing: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' },
            machinery: { view: userRole !== 'user', manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'siteengineer' },
            reports: { view: userRole !== 'user', manage: userRole === 'admin' || userRole === 'superadmin' },
            roles: { view: userRole === 'superadmin', manage: userRole === 'superadmin' }
          }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchRoleInfo();
  }, [userRole]);

  // Memoize handlers
  const handleDrawerToggle = () => setMobileOpen(prev => !prev);

  const handleLogout = () => {
    Cookies.remove('jwtToken');
    Cookies.remove('email');
    Cookies.remove('userRole');
    navigate('/login');
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    // If page is product-management, navigate to inventory instead
    if (page === 'product-management') {
      navigate(`/${userRole === 'superadmin' || userRole === 'admin' ? 'manager' : 'user'}/inventory`);
    } else {
      navigate(`/${userRole === 'superadmin' || userRole === 'admin' ? 'manager' : 'user'}/${page}`);
    }
  };

  const handleInventoryClick = () => {
    // Only toggle the submenu without navigating
    setInventoryOpen(!inventoryOpen);
  };

  // Memoize drawer content
  const drawer = useMemo(() => {
    // If still loading permissions, show a loading indicator
    if (loading) {
      return (
        <Box sx={{ 
          height: '100%', 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center' 
        }}>
          <CircularProgress />
        </Box>
      );
    }

    // Extract permissions or set defaults if not available
    const permissions = roleInfo?.permissions || {
      inventory: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' },
      transfers: { initiate: userRole !== 'user', receive: userRole !== 'user', view: true },
      storeRegistration: { view: userRole !== 'user', manage: userRole === 'admin' || userRole === 'superadmin' },
      products: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'storekeeper' },
      users: { view: userRole === 'admin' || userRole === 'superadmin', manage: userRole === 'admin' || userRole === 'superadmin' },
      billing: { view: true, manage: userRole === 'admin' || userRole === 'superadmin' },
      machinery: { view: userRole !== 'user', manage: userRole === 'admin' || userRole === 'superadmin' || userRole === 'siteengineer' },
      reports: { view: userRole !== 'user', manage: userRole === 'admin' || userRole === 'superadmin' },
      roles: { view: userRole === 'superadmin', manage: userRole === 'superadmin' }
    };

    return (
      <Box sx={{ height: '100%', bgcolor: 'background.paper', boxShadow: 1 }}>
        <Box sx={{ 
          p: 1.2, 
          display: 'flex', 
          alignItems: 'center', 
          gap: 2,
          background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.dark} 90%)`,
          color: 'white',
          mb: 2
        }}>
          <Avatar sx={{ 
            bgcolor: 'white', 
            color: 'primary.main',
            width: 45, 
            height: 45,
            boxShadow: 2
          }} src='https://scontent.fhyd7-1.fna.fbcdn.net/v/t39.30808-6/303292734_447599477389457_5103831908007527092_n.jpg?_nc_cat=105&ccb=1-7&_nc_sid=6ee11a&_nc_ohc=ZaF_9rvxcBkQ7kNvwEXFFtz&_nc_oc=AdlgL9TKgV3cfMPclvhzSH8zUVX2otf_-MeMATSe48uheHzgTMcDMugsyjCt8k5X77M&_nc_zt=23&_nc_ht=scontent.fhyd7-1.fna&_nc_gid=G7GKJlur4fuu1vU8taHtyA&oh=00_AfLddV1fl5FLCSyHoq-eKkcmkrG_moljP0HJxiJon1WokQ&oe=68464B20'/>
          <Typography variant="h6" sx={{ fontWeight: 700 }}>{t('app.title')}</Typography>
        </Box>

        <List sx={{ px: 2 }}>
          <ListItem 
            button
            selected={currentPage === 'dashboard'} 
            onClick={() => handlePageChange('dashboard')}
            sx={getDrawerItemStyles(theme)}
          >
            <ListItemIcon><DashboardIcon color={currentPage === 'dashboard' ? 'primary' : 'inherit'} /></ListItemIcon>
            <ListItemText primary={t('menu.dashboard')} primaryTypographyProps={{ fontWeight: currentPage === 'dashboard' ? 600 : 500 }} />
          </ListItem>
          
          {/* Inventory Section - Only show if user has permission to view inventory */}
          {permissions.inventory.view && (
            <>
              <ListItem 
                button
                selected={currentPage === 'inventory' || 
                           currentPage === 'initiate-transfer' || 
                           currentPage === 'received-transfers' || 
                           currentPage === 'store-registration' ||
                           currentPage === 'product-management'} 
                onClick={handleInventoryClick}
                sx={getDrawerItemStyles(theme)}
              >
                <ListItemIcon>
                  <Warehouse 
                    color={(currentPage === 'inventory' || 
                            currentPage === 'initiate-transfer' || 
                            currentPage === 'received-transfers' || 
                            currentPage === 'store-registration' ||
                            currentPage === 'product-management') ? 'primary' : 'inherit'} 
                  />
                </ListItemIcon>
                <ListItemText 
                  primary={t('menu.inventory')} 
                  primaryTypographyProps={{ 
                    fontWeight: (currentPage === 'inventory' || 
                                 currentPage === 'initiate-transfer' || 
                                 currentPage === 'received-transfers' || 
                                 currentPage === 'store-registration' ||
                                 currentPage === 'product-management') ? 600 : 500 
                  }} 
                />
                {inventoryOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItem>
              
              <Collapse in={inventoryOpen} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {/* Product Management - Show for users with product view permissions */}
                  {permissions.products.view && (
                    <ListItem
                      button
                      selected={currentPage === 'product-management'}
                      onClick={() => handlePageChange('product-management')}
                      sx={getNestedItemStyles(theme)}
                    >
                      <ListItemIcon><Category color={currentPage === 'product-management' ? 'primary' : 'inherit'} /></ListItemIcon>
                      <ListItemText primary={t('menu.productManagement')} primaryTypographyProps={{ fontWeight: currentPage === 'product-management' ? 600 : 500 }} />
                    </ListItem>
                  )}

                  {/* Initiate Transfer - Show for users with transfer initiate permissions */}
                  {permissions.transfers.initiate && (
                    <ListItem
                      button
                      selected={currentPage === 'initiate-transfer'}
                      onClick={() => handlePageChange('initiate-transfer')}
                      sx={getNestedItemStyles(theme)}
                    >
                      <ListItemIcon><CompareArrows color={currentPage === 'initiate-transfer' ? 'primary' : 'inherit'} /></ListItemIcon>
                      <ListItemText primary={t('menu.initiateTransfer')} primaryTypographyProps={{ fontWeight: currentPage === 'initiate-transfer' ? 600 : 500 }} />
                    </ListItem>
                  )}

                  {/* Received Transfers - Show for users with transfer receive permissions */}
                  {permissions.transfers.receive && (
                    <ListItem
                      button
                      selected={currentPage === 'received-transfers'}
                      onClick={() => handlePageChange('received-transfers')}
                      sx={getNestedItemStyles(theme)}
                    >
                      <ListItemIcon><MoveToInbox color={currentPage === 'received-transfers' ? 'primary' : 'inherit'} /></ListItemIcon>
                      <ListItemText primary={t('menu.receivedTransfers')} primaryTypographyProps={{ fontWeight: currentPage === 'received-transfers' ? 600 : 500 }} />
                    </ListItem>
                  )}

                  {/* Store Registration - Show for users with store registration view permissions */}
                  {permissions.storeRegistration.view && (
                    <ListItem 
                      button
                      selected={currentPage === 'store-registration'} 
                      onClick={() => handlePageChange('store-registration')}
                      sx={getNestedItemStyles(theme)}
                    >
                      <ListItemIcon><AppRegistrationIcon color={currentPage === 'store-registration' ? 'primary' : 'inherit'} /></ListItemIcon>
                      <ListItemText primary={t('menu.storeRegistration')} primaryTypographyProps={{ fontWeight: currentPage === 'store-registration' ? 600 : 500 }} />
                    </ListItem>
                  )}
                </List>
              </Collapse>
            </>
          )}

          {/* Security Section - Only show if user has permission to view users or roles */}
          {(permissions.users.view || permissions.roles.view) && (
            <>
              <ListItem 
                button
                selected={currentPage === 'user-management' || currentPage === 'role-management'} 
                onClick={() => setSecurityOpen(!securityOpen)}
                sx={getDrawerItemStyles(theme)}
              >
                <ListItemIcon>
                  <SecurityIcon 
                    color={(currentPage === 'user-management' || currentPage === 'role-management') ? 'primary' : 'inherit'} 
                  />
                </ListItemIcon>
                <ListItemText 
                  primary={t('menu.security')} 
                  primaryTypographyProps={{ 
                    fontWeight: (currentPage === 'user-management' || currentPage === 'role-management') ? 600 : 500 
                  }} 
                />
                {securityOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItem>
              
              <Collapse in={securityOpen} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {/* User Management - Show for users with user view permissions */}
                  {permissions.users.view && (
                    <ListItem
                      button
                      selected={currentPage === 'user-management'}
                      onClick={() => handlePageChange('user-management')}
                      sx={getNestedItemStyles(theme)}
                    >
                      <ListItemIcon><Group color={currentPage === 'user-management' ? 'primary' : 'inherit'} /></ListItemIcon>
                      <ListItemText primary={t('menu.userManagement')} primaryTypographyProps={{ fontWeight: currentPage === 'user-management' ? 600 : 500 }} />
                    </ListItem>
                  )}

                  {/* Role Management - Show for users with role view permissions */}
                  {permissions.roles.view && (
                    <ListItem
                      button
                      selected={currentPage === 'role-management'}
                      onClick={() => handlePageChange('role-management')}
                      sx={getNestedItemStyles(theme)}
                    >
                      <ListItemIcon><VpnKey color={currentPage === 'role-management' ? 'primary' : 'inherit'} /></ListItemIcon>
                      <ListItemText primary={t('menu.roleManagement')} primaryTypographyProps={{ fontWeight: currentPage === 'role-management' ? 600 : 500 }} />
                    </ListItem>
                  )}
                </List>
              </Collapse>
            </>
          )}

          {/* Bill Details - Only show if user has permission to view billing */}
          {permissions.billing.view && (
            <ListItem 
              button
              selected={currentPage === 'bill-details'}
              onClick={() => handlePageChange('bill-details')}
              sx={getDrawerItemStyles(theme)}
            >
              <ListItemIcon><BarChart color={currentPage === 'bill-details' ? 'primary' : 'inherit'} /></ListItemIcon>
              <ListItemText primary={t('menu.billDetails')} primaryTypographyProps={{ fontWeight: currentPage === 'bill-details' ? 600 : 500 }} />
            </ListItem>
          )}

          {/* Plant & Machinery - Only show if user has permission to view machinery */}
          {permissions.machinery.view && (
            <ListItem 
              button
              selected={currentPage === 'machinery-work'} 
              onClick={() => handlePageChange('machinery-work')}
              sx={getDrawerItemStyles(theme)}
            >
              <ListItemIcon><ConstructionIcon color={currentPage === 'machinery-work' ? 'primary' : 'inherit'} /></ListItemIcon>
              <ListItemText primary={t('menu.machineryWork')} primaryTypographyProps={{ fontWeight: currentPage === 'machinery-work' ? 600 : 500 }} />
            </ListItem>
          )}

          {/* Charts - Available to all users */}
          <ListItem 
            button
            selected={currentPage === 'charts'} 
            onClick={() => handlePageChange('charts')}
            sx={getDrawerItemStyles(theme)}
          >
            <ListItemIcon><BarChart color={currentPage === 'charts' ? 'primary' : 'inherit'} /></ListItemIcon>
            <ListItemText primary={t('menu.charts')} primaryTypographyProps={{ fontWeight: currentPage === 'charts' ? 600 : 500 }} />
          </ListItem>
        </List>

        <Divider sx={{ my: 2 }} />
        
        <List sx={{ px: 2 }}>
          <ListItem 
            button
            onClick={handleLogout} 
            sx={{ 
              borderRadius: 2, 
              color: 'error.main',
              '&:hover': {
                backgroundColor: alpha(theme.palette.error.main, 0.1),
              }
            }}
          >
            <ListItemIcon><LogoutIcon color="error" /></ListItemIcon>
            <ListItemText primary={t('menu.logout')} primaryTypographyProps={{ fontWeight: 500 }} />
          </ListItem>
        </List>
      </Box>
    );
  }, [currentPage, inventoryOpen, securityOpen, loading, roleInfo, userRole, handlePageChange, handleInventoryClick, theme, t]);

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar 
        position="fixed"
        sx={{ 
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          bgcolor: 'background.paper',
          borderBottom: 1,
          borderColor: 'divider',
          backdropFilter: 'blur(8px)'
        }}
        elevation={0}
      >
        <Toolbar>
          <IconButton
            color="primary"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Box sx={{ flexGrow: 1 }} />
          <Box sx={{ mr: 2 }}>
            <PremiumLanguageSwitcher />
          </Box>
          <Avatar sx={{ 
            bgcolor: theme.palette.primary.main,
            cursor: 'pointer',
            transition: '0.3s',
            '&:hover': {
              transform: 'scale(1.1)',
            }
          }}>
            {userRole ? userRole.charAt(0).toUpperCase() : 'U'}
          </Avatar>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              borderRight: 'none',
              boxShadow: 3
            },
          }}
        >
          {drawer}
        </Drawer>

        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              borderRight: 'none',
              boxShadow: 3
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
          bgcolor: alpha(theme.palette.primary.main, 0.02),
          minHeight: 'calc(100vh - 64px)',
          overflow: 'auto',
          height: 'calc(100vh - 64px)',
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
};

export default HomePage;