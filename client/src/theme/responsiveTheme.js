import { createTheme } from '@mui/material/styles';

/**
 * Responsive theme configuration for the application
 * Includes breakpoints, typography, spacing, and component overrides
 */

// Custom breakpoints for better responsive control
const breakpoints = {
  values: {
    xs: 0,
    sm: 600,
    md: 900,
    lg: 1200,
    xl: 1536,
  },
};

// Responsive typography configuration
const typography = {
  fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  
  // Responsive font sizes
  h1: {
    fontSize: '2.5rem',
    fontWeight: 700,
    lineHeight: 1.2,
    '@media (max-width:600px)': {
      fontSize: '2rem',
    },
    '@media (max-width:480px)': {
      fontSize: '1.75rem',
    },
  },
  h2: {
    fontSize: '2rem',
    fontWeight: 600,
    lineHeight: 1.3,
    '@media (max-width:600px)': {
      fontSize: '1.75rem',
    },
    '@media (max-width:480px)': {
      fontSize: '1.5rem',
    },
  },
  h3: {
    fontSize: '1.75rem',
    fontWeight: 600,
    lineHeight: 1.3,
    '@media (max-width:600px)': {
      fontSize: '1.5rem',
    },
    '@media (max-width:480px)': {
      fontSize: '1.25rem',
    },
  },
  h4: {
    fontSize: '1.5rem',
    fontWeight: 600,
    lineHeight: 1.4,
    '@media (max-width:600px)': {
      fontSize: '1.25rem',
    },
    '@media (max-width:480px)': {
      fontSize: '1.125rem',
    },
  },
  h5: {
    fontSize: '1.25rem',
    fontWeight: 500,
    lineHeight: 1.4,
    '@media (max-width:600px)': {
      fontSize: '1.125rem',
    },
    '@media (max-width:480px)': {
      fontSize: '1rem',
    },
  },
  h6: {
    fontSize: '1.125rem',
    fontWeight: 500,
    lineHeight: 1.5,
    '@media (max-width:600px)': {
      fontSize: '1rem',
    },
    '@media (max-width:480px)': {
      fontSize: '0.875rem',
    },
  },
  body1: {
    fontSize: '1rem',
    lineHeight: 1.6,
    '@media (max-width:480px)': {
      fontSize: '0.875rem',
    },
  },
  body2: {
    fontSize: '0.875rem',
    lineHeight: 1.6,
    '@media (max-width:480px)': {
      fontSize: '0.8125rem',
    },
  },
  button: {
    fontSize: '0.875rem',
    fontWeight: 500,
    textTransform: 'none',
    '@media (max-width:480px)': {
      fontSize: '0.8125rem',
    },
  },
};

// Responsive spacing configuration
const spacing = (factor) => `${0.25 * factor}rem`;

// Component overrides for responsive behavior
const components = {
  MuiContainer: {
    styleOverrides: {
      root: {
        paddingLeft: '16px',
        paddingRight: '16px',
        '@media (max-width:600px)': {
          paddingLeft: '12px',
          paddingRight: '12px',
        },
        '@media (max-width:480px)': {
          paddingLeft: '8px',
          paddingRight: '8px',
        },
      },
    },
  },
  MuiCard: {
    styleOverrides: {
      root: {
        borderRadius: '12px',
        '@media (max-width:600px)': {
          borderRadius: '8px',
        },
      },
    },
  },
  MuiButton: {
    styleOverrides: {
      root: {
        borderRadius: '8px',
        padding: '8px 16px',
        minHeight: '40px',
        '@media (max-width:600px)': {
          padding: '10px 16px',
          minHeight: '44px', // Better touch target
          borderRadius: '6px',
        },
        '@media (max-width:480px)': {
          padding: '12px 16px',
          minHeight: '48px', // Even better touch target
          fontSize: '0.875rem',
        },
      },
      sizeSmall: {
        padding: '6px 12px',
        minHeight: '32px',
        '@media (max-width:600px)': {
          padding: '8px 12px',
          minHeight: '40px',
        },
        '@media (max-width:480px)': {
          padding: '10px 12px',
          minHeight: '44px',
        },
      },
      sizeLarge: {
        padding: '12px 24px',
        minHeight: '48px',
        '@media (max-width:600px)': {
          padding: '14px 24px',
          minHeight: '52px',
        },
        '@media (max-width:480px)': {
          padding: '16px 24px',
          minHeight: '56px',
        },
      },
    },
  },
  MuiIconButton: {
    styleOverrides: {
      root: {
        padding: '8px',
        '@media (max-width:600px)': {
          padding: '12px',
          minWidth: '44px',
          minHeight: '44px',
        },
        '@media (max-width:480px)': {
          padding: '14px',
          minWidth: '48px',
          minHeight: '48px',
        },
      },
    },
  },
  MuiTextField: {
    styleOverrides: {
      root: {
        '& .MuiInputBase-root': {
          minHeight: '40px',
          '@media (max-width:600px)': {
            minHeight: '44px',
          },
          '@media (max-width:480px)': {
            minHeight: '48px',
          },
        },
      },
    },
  },
  MuiTableCell: {
    styleOverrides: {
      root: {
        padding: '12px 16px',
        '@media (max-width:900px)': {
          padding: '8px 12px',
          fontSize: '0.875rem',
        },
        '@media (max-width:600px)': {
          padding: '6px 8px',
          fontSize: '0.8125rem',
        },
      },
    },
  },
  MuiDrawer: {
    styleOverrides: {
      paper: {
        '@media (max-width:600px)': {
          width: '280px',
        },
        '@media (max-width:480px)': {
          width: '100vw',
          maxWidth: '320px',
        },
      },
    },
  },
  MuiFab: {
    styleOverrides: {
      root: {
        '@media (max-width:600px)': {
          width: '48px',
          height: '48px',
        },
        '@media (max-width:480px)': {
          width: '52px',
          height: '52px',
        },
      },
    },
  },
  MuiDialog: {
    styleOverrides: {
      paper: {
        margin: '32px',
        '@media (max-width:600px)': {
          margin: '16px',
          maxHeight: 'calc(100vh - 32px)',
        },
        '@media (max-width:480px)': {
          margin: '8px',
          maxHeight: 'calc(100vh - 16px)',
          borderRadius: '8px',
        },
      },
    },
  },
};

// Create the responsive theme
export const createResponsiveTheme = (mode = 'light') => {
  const baseTheme = createTheme({
    breakpoints,
    typography,
    spacing,
    components,
    palette: {
      mode,
      primary: {
        main: '#1976d2',
        light: '#42a5f5',
        dark: '#1565c0',
      },
      secondary: {
        main: '#dc004e',
        light: '#ff5983',
        dark: '#9a0036',
      },
      background: {
        default: mode === 'light' ? '#fafafa' : '#121212',
        paper: mode === 'light' ? '#ffffff' : '#1e1e1e',
      },
    },
  });

  return baseTheme;
};

export default createResponsiveTheme;
