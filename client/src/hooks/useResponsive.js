import { useState, useEffect } from 'react';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';

/**
 * Custom hook for responsive design utilities
 * Provides breakpoint detection and responsive values
 */
export const useResponsive = () => {
  const theme = useTheme();
  
  // Material-UI breakpoint queries
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
  const isLargeDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  
  // Custom breakpoint queries
  const isSmallMobile = useMediaQuery('(max-width: 480px)');
  const isMediumMobile = useMediaQuery('(min-width: 481px) and (max-width: 767px)');
  const isSmallTablet = useMediaQuery('(min-width: 768px) and (max-width: 991px)');
  const isLargeTablet = useMediaQuery('(min-width: 992px) and (max-width: 1199px)');
  
  // Screen orientation
  const isLandscape = useMediaQuery('(orientation: landscape)');
  const isPortrait = useMediaQuery('(orientation: portrait)');
  
  // Touch device detection
  const isTouchDevice = useMediaQuery('(hover: none) and (pointer: coarse)');
  
  // High DPI detection
  const isHighDPI = useMediaQuery('(-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)');

  return {
    // Basic breakpoints
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
    
    // Detailed breakpoints
    isSmallMobile,
    isMediumMobile,
    isSmallTablet,
    isLargeTablet,
    
    // Device characteristics
    isLandscape,
    isPortrait,
    isTouchDevice,
    isHighDPI,
    
    // Utility functions
    getResponsiveValue: (mobileValue, tabletValue, desktopValue) => {
      if (isMobile) return mobileValue;
      if (isTablet) return tabletValue || mobileValue;
      return desktopValue || tabletValue || mobileValue;
    },
    
    // Grid columns helper
    getGridColumns: (mobile = 1, tablet = 2, desktop = 3) => {
      if (isMobile) return mobile;
      if (isTablet) return tablet;
      return desktop;
    },
    
    // Spacing helper
    getSpacing: (mobile = 1, tablet = 2, desktop = 3) => {
      if (isMobile) return theme.spacing(mobile);
      if (isTablet) return theme.spacing(tablet);
      return theme.spacing(desktop);
    }
  };
};

/**
 * Hook for responsive drawer width
 */
export const useResponsiveDrawer = () => {
  const { isMobile, isTablet } = useResponsive();
  
  const getDrawerWidth = () => {
    if (isMobile) return 280;
    if (isTablet) return 260;
    return 280;
  };
  
  const getDrawerVariant = () => {
    if (isMobile) return 'temporary';
    return 'permanent';
  };
  
  return {
    drawerWidth: getDrawerWidth(),
    drawerVariant: getDrawerVariant(),
    shouldShowDrawer: !isMobile
  };
};

/**
 * Hook for responsive typography
 */
export const useResponsiveTypography = () => {
  const { isMobile, isTablet } = useResponsive();
  
  const getVariant = (desktop, tablet, mobile) => {
    if (isMobile) return mobile || tablet || desktop;
    if (isTablet) return tablet || desktop;
    return desktop;
  };
  
  const getFontSize = (desktop, tablet, mobile) => {
    if (isMobile) return mobile || tablet || desktop;
    if (isTablet) return tablet || desktop;
    return desktop;
  };
  
  return {
    getVariant,
    getFontSize,
    // Predefined responsive variants
    h1: getVariant('h1', 'h2', 'h3'),
    h2: getVariant('h2', 'h3', 'h4'),
    h3: getVariant('h3', 'h4', 'h5'),
    h4: getVariant('h4', 'h5', 'h6'),
    h5: getVariant('h5', 'h6', 'subtitle1'),
    h6: getVariant('h6', 'subtitle1', 'subtitle2')
  };
};

/**
 * Hook for window dimensions
 */
export const useWindowDimensions = () => {
  const [windowDimensions, setWindowDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    function handleResize() {
      setWindowDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowDimensions;
};

/**
 * Hook for responsive container maxWidth
 */
export const useResponsiveContainer = () => {
  const { isMobile, isTablet } = useResponsive();
  
  const getMaxWidth = () => {
    if (isMobile) return 'sm';
    if (isTablet) return 'md';
    return 'xl';
  };
  
  const getPadding = () => {
    if (isMobile) return 1;
    if (isTablet) return 2;
    return 3;
  };
  
  return {
    maxWidth: getMaxWidth(),
    padding: getPadding()
  };
};
