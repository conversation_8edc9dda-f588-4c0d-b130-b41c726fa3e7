import { BrowserRouter as Router, Route, Routes, Navigate } from "react-router-dom";
import HomePage from "./pages/HomePage";
import Dashboard from "./pages/Dashboard";
import LoginPage from "./pages/LoginPage";
import Inventory from "./pages/Inventory"
import ChartsPage from "./pages/ChartsPage";
import BillDetails from "./pages/BillDetails"
import LandingPage from "./pages/LandingPage"
import RequestPage from "./pages/RequestPage"
import UserManagement from "./pages/UserManagement"
import AdvancedReports from "./pages/AdvancedReports"
import Recommendation from "./pages/Recommendation"
import CustomerAnalysis from "./pages/CustomerAnalysis"
import SupplierAnalysis from "./pages/SupplierAnalysis"
import RegistrationPage from "./pages/RegistrationPage"
import ProductsPage from "./pages/ProductsPage"
import StoreLocationsPage from "./pages/StoreLocationsPage"
import StoreRegistrationPage from "./pages/StoreRegistrationPage"
import ProtectedRoute from './utils/ProtectedRoute';
import InitiateTransfer from './pages/InitiateTransfer';
import ReceivedTransfers from './pages/ReceivedTransfers';
import MachineryWorkPage from "./pages/MachineryWorkPage";
import RoleManagement from "./pages/RoleManagement";


function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegistrationPage />} />
        <Route path="/request" element={<RequestPage />} />

        <Route
          path="/app"
          element={
            <ProtectedRoute allowedRoles={['superadmin', 'admin', 'user', 'siteengineer', 'storekeeper']}>
              <HomePage />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="inventory" element={<Inventory />} />
          <Route path="products" element={<ProductsPage />} />
          <Route path="store-locations" element={<StoreLocationsPage />} />
          <Route path="store-registration" element={<StoreRegistrationPage />} />
          <Route path="charts" element={<ChartsPage />} />
          <Route path="bill-details" element={<BillDetails />} />
          <Route path="user-management" element={<UserManagement />} />
          <Route path="role-management" element={<RoleManagement />} />
          <Route path="advanced-reports" element={<AdvancedReports />} />
          <Route path="recommendation" element={<Recommendation />} />
          <Route path="customer-analysis" element={<CustomerAnalysis />} />
          <Route path="supplier-analysis" element={<SupplierAnalysis />} />
          <Route path="initiate-transfer" element={<InitiateTransfer />} />
          <Route path="received-transfers" element={<ReceivedTransfers />} />
          <Route path="machinery-work" element={<MachineryWorkPage />} />
        </Route>

        {/* Redirect old paths to new structure */}
        <Route path="/manager/*" element={<Navigate to="/app" replace />} />
        <Route path="/user/*" element={<Navigate to="/app" replace />} />

        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    </Router>
  );
}

export default App;