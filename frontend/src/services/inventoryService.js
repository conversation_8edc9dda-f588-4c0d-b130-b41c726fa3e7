import { apiService } from './api';

/**
 * Inventory Service
 * Handles all inventory-related API calls
 */
export class InventoryService {
  // Get all products
  async getAllProducts() {
    try {
      const response = await apiService.get('/api/product/');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  }

  // Get product by ID
  async getProductById(id) {
    try {
      const response = await apiService.get(`/api/product/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  }

  // Get product by SKU
  async getProductBySku(sku) {
    try {
      const response = await apiService.get(`/api/product/sku/${sku}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product by SKU:', error);
      throw new Error('Failed to fetch product');
    }
  }

  // Add new product
  async addProduct(productData) {
    try {
      const response = await apiService.post('/api/product/add', productData);
      return response;
    } catch (error) {
      console.error('Error adding product:', error);
      throw new Error('Failed to add product');
    }
  }

  // Update product
  async updateProduct(id, productData) {
    try {
      const response = await apiService.put(`/api/product/${id}`, productData);
      return response;
    } catch (error) {
      console.error('Error updating product:', error);
      throw new Error('Failed to update product');
    }
  }

  // Update product by SKU
  async updateProductBySku(sku, productData) {
    try {
      const response = await apiService.put(`/api/product/update/${sku}`, productData);
      return response;
    } catch (error) {
      console.error('Error updating product:', error);
      throw new Error('Failed to update product');
    }
  }

  // Delete product
  async deleteProduct(id) {
    try {
      const response = await apiService.delete(`/api/product/${id}`);
      return response;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw new Error('Failed to delete product');
    }
  }

  // Delete product by SKU
  async deleteProductBySku(sku) {
    try {
      const response = await apiService.delete(`/api/product/delete/${sku}`);
      return response;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw new Error('Failed to delete product');
    }
  }

  // Update stock level
  async updateStock(id, stockData) {
    try {
      const response = await apiService.patch(`/api/product/${id}/stock`, stockData);
      return response;
    } catch (error) {
      console.error('Error updating stock:', error);
      throw new Error('Failed to update stock');
    }
  }

  // Get low stock products
  async getLowStockProducts() {
    try {
      const response = await apiService.get('/api/product/low-stock');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching low stock products:', error);
      throw new Error('Failed to fetch low stock products');
    }
  }

  // Get products by category
  async getProductsByCategory(category) {
    try {
      const response = await apiService.get(`/api/product/category/${category}`);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching products by category:', error);
      throw new Error('Failed to fetch products by category');
    }
  }

  // Search products
  async searchProducts(query) {
    try {
      const response = await apiService.get(`/api/product/search?q=${encodeURIComponent(query)}`);
      return response.data || [];
    } catch (error) {
      console.error('Error searching products:', error);
      throw new Error('Failed to search products');
    }
  }

  // Get inventory statistics
  async getInventoryStats() {
    try {
      const response = await apiService.get('/api/product/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching inventory stats:', error);
      throw new Error('Failed to fetch inventory statistics');
    }
  }

  // Export inventory data
  async exportInventory(format = 'csv') {
    try {
      const response = await apiService.get(`/api/product/export?format=${format}`);
      return response;
    } catch (error) {
      console.error('Error exporting inventory:', error);
      throw new Error('Failed to export inventory');
    }
  }

  // Bulk update products
  async bulkUpdateProducts(updates) {
    try {
      const response = await apiService.post('/api/product/bulk-update', { updates });
      return response;
    } catch (error) {
      console.error('Error bulk updating products:', error);
      throw new Error('Failed to bulk update products');
    }
  }

  // Get product categories
  async getCategories() {
    try {
      const response = await apiService.get('/api/product/categories');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Return default categories if API fails
      return [
        'Laying',
        'ElectricalMaterial',
        'StructureMaterial',
        'PlumbingMaterials',
        'ToolsAndEquipment',
        'Electronics'
      ];
    }
  }
}

// Create and export singleton instance
export const inventoryService = new InventoryService();
