import { apiService } from './api';

/**
 * Store Service
 * Handles all store-related API calls
 */
export class StoreService {
  // Get all stores
  async getAllStores(params = {}) {
    try {
      const queryString = new URLSearchParams(params).toString();
      const endpoint = queryString ? `/api/store?${queryString}` : '/api/store';
      const response = await apiService.get(endpoint);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching stores:', error);
      throw new Error('Failed to fetch stores');
    }
  }

  // Get store by ID
  async getStoreById(id) {
    try {
      const response = await apiService.get(`/api/store/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching store:', error);
      throw new Error('Failed to fetch store');
    }
  }

  // Create new store
  async createStore(storeData) {
    try {
      const response = await apiService.post('/api/store/add', storeData);
      return response;
    } catch (error) {
      console.error('Error creating store:', error);
      throw new Error(error.message || 'Failed to create store');
    }
  }

  // Update store
  async updateStore(id, storeData) {
    try {
      const response = await apiService.put(`/api/store/${id}`, storeData);
      return response;
    } catch (error) {
      console.error('Error updating store:', error);
      throw new Error(error.message || 'Failed to update store');
    }
  }

  // Delete store
  async deleteStore(id) {
    try {
      const response = await apiService.delete(`/api/store/${id}`);
      return response;
    } catch (error) {
      console.error('Error deleting store:', error);
      throw new Error('Failed to delete store');
    }
  }

  // Get store inventory
  async getStoreInventory(storeId, params = {}) {
    try {
      const queryString = new URLSearchParams(params).toString();
      const endpoint = queryString 
        ? `/api/store/${storeId}/inventory?${queryString}` 
        : `/api/store/${storeId}/inventory`;
      const response = await apiService.get(endpoint);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching store inventory:', error);
      throw new Error('Failed to fetch store inventory');
    }
  }

  // Update store inventory
  async updateStoreInventory(storeId, inventoryData) {
    try {
      const response = await apiService.put(`/api/store/${storeId}/inventory`, inventoryData);
      return response;
    } catch (error) {
      console.error('Error updating store inventory:', error);
      throw new Error('Failed to update store inventory');
    }
  }

  // Get store statistics
  async getStoreStats(storeId) {
    try {
      const response = await apiService.get(`/api/store/${storeId}/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching store stats:', error);
      throw new Error('Failed to fetch store statistics');
    }
  }

  // Get all stores statistics
  async getAllStoresStats() {
    try {
      const response = await apiService.get('/api/store/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching stores stats:', error);
      throw new Error('Failed to fetch stores statistics');
    }
  }

  // Search stores
  async searchStores(query) {
    try {
      const response = await apiService.get(`/api/store/search?q=${encodeURIComponent(query)}`);
      return response.data || [];
    } catch (error) {
      console.error('Error searching stores:', error);
      throw new Error('Failed to search stores');
    }
  }

  // Get stores by location
  async getStoresByLocation(location) {
    try {
      const response = await apiService.get(`/api/store/location/${encodeURIComponent(location)}`);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching stores by location:', error);
      throw new Error('Failed to fetch stores by location');
    }
  }

  // Get store managers
  async getStoreManagers(storeId) {
    try {
      const response = await apiService.get(`/api/store/${storeId}/managers`);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching store managers:', error);
      throw new Error('Failed to fetch store managers');
    }
  }

  // Assign manager to store
  async assignManager(storeId, managerId) {
    try {
      const response = await apiService.post(`/api/store/${storeId}/managers`, { managerId });
      return response;
    } catch (error) {
      console.error('Error assigning manager:', error);
      throw new Error('Failed to assign manager to store');
    }
  }

  // Remove manager from store
  async removeManager(storeId, managerId) {
    try {
      const response = await apiService.delete(`/api/store/${storeId}/managers/${managerId}`);
      return response;
    } catch (error) {
      console.error('Error removing manager:', error);
      throw new Error('Failed to remove manager from store');
    }
  }

  // Get store transfer requests
  async getStoreTransferRequests(storeId, params = {}) {
    try {
      const queryString = new URLSearchParams(params).toString();
      const endpoint = queryString 
        ? `/api/store/${storeId}/transfers?${queryString}` 
        : `/api/store/${storeId}/transfers`;
      const response = await apiService.get(endpoint);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching store transfer requests:', error);
      throw new Error('Failed to fetch store transfer requests');
    }
  }

  // Create transfer request
  async createTransferRequest(transferData) {
    try {
      const response = await apiService.post('/api/store/transfer-request', transferData);
      return response;
    } catch (error) {
      console.error('Error creating transfer request:', error);
      throw new Error('Failed to create transfer request');
    }
  }

  // Update transfer request status
  async updateTransferStatus(transferId, status, notes = '') {
    try {
      const response = await apiService.patch(`/api/store/transfer/${transferId}/status`, {
        status,
        notes
      });
      return response;
    } catch (error) {
      console.error('Error updating transfer status:', error);
      throw new Error('Failed to update transfer status');
    }
  }

  // Get store activity log
  async getStoreActivity(storeId, params = {}) {
    try {
      const queryString = new URLSearchParams(params).toString();
      const endpoint = queryString 
        ? `/api/store/${storeId}/activity?${queryString}` 
        : `/api/store/${storeId}/activity`;
      const response = await apiService.get(endpoint);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching store activity:', error);
      throw new Error('Failed to fetch store activity');
    }
  }

  // Export store data
  async exportStoreData(storeId, format = 'csv') {
    try {
      const response = await apiService.get(`/api/store/${storeId}/export?format=${format}`);
      return response;
    } catch (error) {
      console.error('Error exporting store data:', error);
      throw new Error('Failed to export store data');
    }
  }

  // Bulk update stores
  async bulkUpdateStores(updates) {
    try {
      const response = await apiService.post('/api/store/bulk-update', { updates });
      return response;
    } catch (error) {
      console.error('Error bulk updating stores:', error);
      throw new Error('Failed to bulk update stores');
    }
  }
}

// Create and export singleton instance
export const storeService = new StoreService();
