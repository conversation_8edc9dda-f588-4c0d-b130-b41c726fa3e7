import { apiService } from './api';

/**
 * Authentication Service
 * Handles all authentication-related API calls
 */
export class AuthService {
  // Login user
  async login(credentials) {
    try {
      const response = await apiService.post('/api/auth/login', credentials);
      
      if (response.success && response.token) {
        // Store token in localStorage
        localStorage.setItem('authToken', response.token);
        localStorage.setItem('userRole', response.user.role);
        localStorage.setItem('userId', response.user.id);
      }
      
      return response;
    } catch (error) {
      console.error('Login error:', error);
      throw new Error(error.message || 'Login failed');
    }
  }

  // Logout user
  async logout() {
    try {
      await apiService.post('/api/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API call success
      localStorage.removeItem('authToken');
      localStorage.removeItem('userRole');
      localStorage.removeItem('userId');
    }
  }

  // Register new user
  async register(userData) {
    try {
      const response = await apiService.post('/api/auth/register', userData);
      return response;
    } catch (error) {
      console.error('Registration error:', error);
      throw new Error(error.message || 'Registration failed');
    }
  }

  // Verify token
  async verifyToken() {
    try {
      const response = await apiService.get('/api/auth/verify');
      return response.user;
    } catch (error) {
      console.error('Token verification error:', error);
      throw new Error('Invalid token');
    }
  }

  // Refresh token
  async refreshToken() {
    try {
      const response = await apiService.post('/api/auth/refresh');
      
      if (response.success && response.token) {
        localStorage.setItem('authToken', response.token);
      }
      
      return response;
    } catch (error) {
      console.error('Token refresh error:', error);
      throw new Error('Failed to refresh token');
    }
  }

  // Forgot password
  async forgotPassword(email) {
    try {
      const response = await apiService.post('/api/auth/forgot-password', { email });
      return response;
    } catch (error) {
      console.error('Forgot password error:', error);
      throw new Error(error.message || 'Failed to send reset email');
    }
  }

  // Reset password
  async resetPassword(token, newPassword) {
    try {
      const response = await apiService.post('/api/auth/reset-password', {
        token,
        password: newPassword
      });
      return response;
    } catch (error) {
      console.error('Reset password error:', error);
      throw new Error(error.message || 'Failed to reset password');
    }
  }

  // Change password
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await apiService.post('/api/auth/change-password', {
        currentPassword,
        newPassword
      });
      return response;
    } catch (error) {
      console.error('Change password error:', error);
      throw new Error(error.message || 'Failed to change password');
    }
  }

  // Get current user
  async getCurrentUser() {
    try {
      const response = await apiService.get('/api/auth/me');
      return response.user;
    } catch (error) {
      console.error('Get current user error:', error);
      throw new Error('Failed to get user information');
    }
  }

  // Update user profile
  async updateProfile(userData) {
    try {
      const response = await apiService.put('/api/auth/profile', userData);
      return response;
    } catch (error) {
      console.error('Update profile error:', error);
      throw new Error(error.message || 'Failed to update profile');
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    const token = localStorage.getItem('authToken');
    return !!token;
  }

  // Get stored user role
  getUserRole() {
    return localStorage.getItem('userRole');
  }

  // Get stored user ID
  getUserId() {
    return localStorage.getItem('userId');
  }

  // Get stored token
  getToken() {
    return localStorage.getItem('authToken');
  }

  // Check if user has specific role
  hasRole(role) {
    const userRole = this.getUserRole();
    return userRole === role;
  }

  // Check if user is admin
  isAdmin() {
    const userRole = this.getUserRole();
    return userRole === 'admin' || userRole === 'superadmin';
  }

  // Check if user is superadmin
  isSuperAdmin() {
    const userRole = this.getUserRole();
    return userRole === 'superadmin';
  }
}

// Create and export singleton instance
export const authService = new AuthService();
