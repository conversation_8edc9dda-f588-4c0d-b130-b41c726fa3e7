import { apiService } from './api';

/**
 * User Service
 * Handles all user-related API calls
 */
export class UserService {
  // Get all users
  async getAllUsers(params = {}) {
    try {
      const queryString = new URLSearchParams(params).toString();
      const endpoint = queryString ? `/api/user?${queryString}` : '/api/user';
      const response = await apiService.get(endpoint);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching users:', error);
      throw new Error('Failed to fetch users');
    }
  }

  // Get user by ID
  async getUserById(id) {
    try {
      const response = await apiService.get(`/api/user/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user:', error);
      throw new Error('Failed to fetch user');
    }
  }

  // Create new user
  async createUser(userData) {
    try {
      const response = await apiService.post('/api/user/add', userData);
      return response;
    } catch (error) {
      console.error('Error creating user:', error);
      throw new Error(error.message || 'Failed to create user');
    }
  }

  // Update user
  async updateUser(id, userData) {
    try {
      const response = await apiService.put(`/api/user/${id}`, userData);
      return response;
    } catch (error) {
      console.error('Error updating user:', error);
      throw new Error(error.message || 'Failed to update user');
    }
  }

  // Delete user
  async deleteUser(id) {
    try {
      const response = await apiService.delete(`/api/user/${id}`);
      return response;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw new Error('Failed to delete user');
    }
  }

  // Update user role
  async updateUserRole(id, role) {
    try {
      const response = await apiService.patch(`/api/user/${id}/role`, { role });
      return response;
    } catch (error) {
      console.error('Error updating user role:', error);
      throw new Error('Failed to update user role');
    }
  }

  // Update user status (active/inactive)
  async updateUserStatus(id, isActive) {
    try {
      const response = await apiService.patch(`/api/user/${id}/status`, { isActive });
      return response;
    } catch (error) {
      console.error('Error updating user status:', error);
      throw new Error('Failed to update user status');
    }
  }

  // Get users by role
  async getUsersByRole(role) {
    try {
      const response = await apiService.get(`/api/user/role/${role}`);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching users by role:', error);
      throw new Error('Failed to fetch users by role');
    }
  }

  // Search users
  async searchUsers(query) {
    try {
      const response = await apiService.get(`/api/user/search?q=${encodeURIComponent(query)}`);
      return response.data || [];
    } catch (error) {
      console.error('Error searching users:', error);
      throw new Error('Failed to search users');
    }
  }

  // Get user statistics
  async getUserStats() {
    try {
      const response = await apiService.get('/api/user/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw new Error('Failed to fetch user statistics');
    }
  }

  // Bulk update users
  async bulkUpdateUsers(updates) {
    try {
      const response = await apiService.post('/api/user/bulk-update', { updates });
      return response;
    } catch (error) {
      console.error('Error bulk updating users:', error);
      throw new Error('Failed to bulk update users');
    }
  }

  // Export users data
  async exportUsers(format = 'csv') {
    try {
      const response = await apiService.get(`/api/user/export?format=${format}`);
      return response;
    } catch (error) {
      console.error('Error exporting users:', error);
      throw new Error('Failed to export users');
    }
  }

  // Get user permissions
  async getUserPermissions(id) {
    try {
      const response = await apiService.get(`/api/user/${id}/permissions`);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      throw new Error('Failed to fetch user permissions');
    }
  }

  // Update user permissions
  async updateUserPermissions(id, permissions) {
    try {
      const response = await apiService.put(`/api/user/${id}/permissions`, { permissions });
      return response;
    } catch (error) {
      console.error('Error updating user permissions:', error);
      throw new Error('Failed to update user permissions');
    }
  }

  // Get user activity log
  async getUserActivity(id, params = {}) {
    try {
      const queryString = new URLSearchParams(params).toString();
      const endpoint = queryString 
        ? `/api/user/${id}/activity?${queryString}` 
        : `/api/user/${id}/activity`;
      const response = await apiService.get(endpoint);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching user activity:', error);
      throw new Error('Failed to fetch user activity');
    }
  }

  // Reset user password (admin only)
  async resetUserPassword(id, newPassword) {
    try {
      const response = await apiService.post(`/api/user/${id}/reset-password`, { 
        newPassword 
      });
      return response;
    } catch (error) {
      console.error('Error resetting user password:', error);
      throw new Error('Failed to reset user password');
    }
  }

  // Send user invitation
  async sendUserInvitation(email, role) {
    try {
      const response = await apiService.post('/api/user/invite', { email, role });
      return response;
    } catch (error) {
      console.error('Error sending user invitation:', error);
      throw new Error('Failed to send user invitation');
    }
  }

  // Get pending invitations
  async getPendingInvitations() {
    try {
      const response = await apiService.get('/api/user/invitations/pending');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching pending invitations:', error);
      throw new Error('Failed to fetch pending invitations');
    }
  }

  // Cancel user invitation
  async cancelInvitation(invitationId) {
    try {
      const response = await apiService.delete(`/api/user/invitations/${invitationId}`);
      return response;
    } catch (error) {
      console.error('Error canceling invitation:', error);
      throw new Error('Failed to cancel invitation');
    }
  }
}

// Create and export singleton instance
export const userService = new UserService();
