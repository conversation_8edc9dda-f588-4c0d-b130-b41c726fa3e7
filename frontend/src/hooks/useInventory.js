import { useState, useEffect, useCallback } from 'react';
import { inventoryService } from '../services/inventoryService';

/**
 * Custom hook for inventory management
 * Provides CRUD operations and state management for inventory
 */
export const useInventory = () => {
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [categories, setCategories] = useState([]);

  // Fetch all products
  const fetchInventory = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const products = await inventoryService.getAllProducts();
      setInventory(products);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching inventory:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      const categoryList = await inventoryService.getCategories();
      setCategories(categoryList);
    } catch (err) {
      console.error('Error fetching categories:', err);
      // Use default categories if fetch fails
      setCategories([
        'Laying',
        'ElectricalMaterial',
        'StructureMaterial',
        'PlumbingMaterials',
        'ToolsAndEquipment',
        'Electronics'
      ]);
    }
  }, []);

  // Add new product
  const addProduct = useCallback(async (productData) => {
    try {
      setLoading(true);
      setError(null);
      const response = await inventoryService.addProduct(productData);
      
      if (response.success) {
        // Add the new product to local state
        const newProduct = {
          id: response.product._id,
          ...productData
        };
        setInventory(prev => [...prev, newProduct]);
        return { success: true, product: newProduct };
      } else {
        throw new Error(response.message || 'Failed to add product');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error adding product:', err);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Update existing product
  const updateProduct = useCallback(async (productId, productData) => {
    try {
      setLoading(true);
      setError(null);
      const response = await inventoryService.updateProduct(productId, productData);
      
      if (response.success) {
        // Update the product in local state
        setInventory(prev => prev.map(item => 
          item.id === productId 
            ? { ...item, ...productData }
            : item
        ));
        return { success: true };
      } else {
        throw new Error(response.message || 'Failed to update product');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error updating product:', err);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete product
  const deleteProduct = useCallback(async (productId) => {
    try {
      setLoading(true);
      setError(null);
      const response = await inventoryService.deleteProduct(productId);
      
      if (response.success) {
        // Remove the product from local state
        setInventory(prev => prev.filter(item => item.id !== productId));
        return { success: true };
      } else {
        throw new Error(response.message || 'Failed to delete product');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error deleting product:', err);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Update stock level
  const updateStock = useCallback(async (productId, stockData) => {
    try {
      setLoading(true);
      setError(null);
      const response = await inventoryService.updateStock(productId, stockData);
      
      if (response.success) {
        // Update stock in local state
        setInventory(prev => prev.map(item => 
          item.id === productId 
            ? { ...item, stock: stockData.stock }
            : item
        ));
        return { success: true };
      } else {
        throw new Error(response.message || 'Failed to update stock');
      }
    } catch (err) {
      setError(err.message);
      console.error('Error updating stock:', err);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Search products
  const searchProducts = useCallback(async (query) => {
    try {
      setLoading(true);
      setError(null);
      const results = await inventoryService.searchProducts(query);
      return results;
    } catch (err) {
      setError(err.message);
      console.error('Error searching products:', err);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Get low stock products
  const getLowStockProducts = useCallback(async () => {
    try {
      const lowStockItems = await inventoryService.getLowStockProducts();
      return lowStockItems;
    } catch (err) {
      console.error('Error fetching low stock products:', err);
      return [];
    }
  }, []);

  // Filter products by category
  const filterByCategory = useCallback((category) => {
    if (category === 'all' || !category) {
      return inventory;
    }
    return inventory.filter(product => product.category === category);
  }, [inventory]);

  // Get inventory statistics
  const getInventoryStats = useCallback(() => {
    const totalProducts = inventory.length;
    const totalValue = inventory.reduce((sum, product) => sum + (product.price * product.stock), 0);
    const lowStockCount = inventory.filter(product => product.stock <= product.lowStockThreshold).length;
    const outOfStockCount = inventory.filter(product => product.stock === 0).length;
    const categoriesCount = new Set(inventory.map(product => product.category)).size;

    return {
      totalProducts,
      totalValue,
      lowStockCount,
      outOfStockCount,
      categoriesCount,
      averageStock: totalProducts > 0 ? inventory.reduce((sum, product) => sum + product.stock, 0) / totalProducts : 0
    };
  }, [inventory]);

  // Load initial data
  useEffect(() => {
    fetchInventory();
    fetchCategories();
  }, [fetchInventory, fetchCategories]);

  return {
    // State
    inventory,
    loading,
    error,
    categories,
    
    // Actions
    fetchInventory,
    addProduct,
    updateProduct,
    deleteProduct,
    updateStock,
    searchProducts,
    getLowStockProducts,
    
    // Utilities
    filterByCategory,
    getInventoryStats,
    
    // Clear error
    clearError: () => setError(null)
  };
};
