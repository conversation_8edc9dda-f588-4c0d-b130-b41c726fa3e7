import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Box,
  Typography,
  CircularProgress
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

const InventoryTable = ({
  inventory = [],
  loading = false,
  onEdit,
  onDelete,
  isAdmin = false
}) => {
  const getStockStatusColor = (stock, lowStockThreshold) => {
    if (stock === 0) return 'error';
    if (stock <= lowStockThreshold) return 'warning';
    return 'success';
  };

  const getStockStatusText = (stock, lowStockThreshold) => {
    if (stock === 0) return 'Out of Stock';
    if (stock <= lowStockThreshold) return 'Low Stock';
    return 'In Stock';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-IN');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" py={4}>
        <CircularProgress />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Loading inventory...
        </Typography>
      </Box>
    );
  }

  if (inventory.length === 0) {
    return (
      <Box textAlign="center" py={4}>
        <Typography variant="h6" color="text.secondary">
          No products found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {isAdmin ? 'Add some products to get started' : 'Check back later for updates'}
        </Typography>
      </Box>
    );
  }

  return (
    <TableContainer component={Paper} elevation={1}>
      <Table>
        <TableHead>
          <TableRow sx={{ backgroundColor: 'grey.50' }}>
            <TableCell sx={{ fontWeight: 600 }}>Product Name</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>SKU</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Category</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Stock</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Price</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Mfg Date</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Expiry Date</TableCell>
            {isAdmin && (
              <TableCell sx={{ fontWeight: 600 }} align="center">
                Actions
              </TableCell>
            )}
          </TableRow>
        </TableHead>
        <TableBody>
          {inventory.map((product) => (
            <TableRow
              key={product.id}
              hover
              sx={{ '&:hover': { backgroundColor: 'grey.25' } }}
            >
              <TableCell>
                <Typography variant="body2" fontWeight={500}>
                  {product.name}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Typography variant="body2" color="text.secondary">
                  {product.sku}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Chip
                  label={product.category}
                  size="small"
                  variant="outlined"
                  sx={{ borderRadius: 1 }}
                />
              </TableCell>
              
              <TableCell>
                <Box display="flex" alignItems="center" gap={1}>
                  <Chip
                    label={`${product.stock} units`}
                    size="small"
                    color={getStockStatusColor(product.stock, product.lowStockThreshold)}
                    variant="filled"
                  />
                  {product.stock <= product.lowStockThreshold && (
                    <WarningIcon color="warning" fontSize="small" />
                  )}
                </Box>
                <Typography variant="caption" color="text.secondary">
                  {getStockStatusText(product.stock, product.lowStockThreshold)}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Typography variant="body2" fontWeight={500}>
                  {formatCurrency(product.price)}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Typography variant="body2">
                  {formatDate(product.manufacturing_date)}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Typography variant="body2">
                  {formatDate(product.expiry_date)}
                </Typography>
              </TableCell>
              
              {isAdmin && (
                <TableCell align="center">
                  <Box display="flex" justifyContent="center" gap={1}>
                    <IconButton
                      size="small"
                      onClick={() => onEdit(product)}
                      color="primary"
                      sx={{ '&:hover': { backgroundColor: 'primary.50' } }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => onDelete(product)}
                      color="error"
                      sx={{ '&:hover': { backgroundColor: 'error.50' } }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default InventoryTable;
